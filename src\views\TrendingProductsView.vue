<template>
  <div class="trending-products-view">
    <div class="dark-bg">
      <!-- 添加背景遮罩 -->
      <div class="dropdown-backdrop" v-if="isMobile && selectedCategory !== null" @click="selectedCategory = null"></div>
      
      <!-- 添加"Beyond The Spreadsheet"区域 -->
      <div class="hero-section">
        <div class="hero-content">
          <h1 class="hero-title">Beyond The Spreadsheet</h1>
          <p class="hero-subtitle">Every Product has QC</p>
          
          <div class="search-container">
            <span class="search-icon">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </span>
            <input type="text" placeholder="Search" class="search-input">
          </div>
          
          <!-- 移动端分类和过滤按钮 -->
          <div class="mobile-filter-category" v-if="isMobile">
            <div class="filter-icon" @click="toggleFilterDropdown">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotate': showFilterDropdown }">
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
              </svg>
            </div>
            
            <button class="category-btn" @click="toggleCategoryMenu">
              CATEGORY
            </button>
            
            <!-- 移动端过滤下拉框 -->
            <div v-if="showFilterDropdown && isMobile" class="mobile-filter-dropdown">
              <div class="filter-options">
                <button class="filter-option" :class="{ 'active': activeFilter === 'recommend' }" @click="setActiveFilter('recommend')">
                  <span>Recommend</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'favorite' }" @click="setActiveFilter('favorite')">
                  <span>Favorite</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'amountSold' }" @click="setActiveFilter('amountSold')">
                  <span>Amount Sold</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'price' }" @click="setActiveFilter('price')">
                  <span>Price</span>
                </button>
                <div class="filter-option seller-option" :class="{ 'active': activeFilter === 'seller' }" @click="setActiveFilter('seller')">
                  <span>Seller</span>
                  <div class="arrow-container">
                    <svg class="arrow-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('asc')">
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                    <svg class="arrow-down" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('desc')">
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </div>
                </div>
                <button class="filter-option refresh" @click="refreshFilter">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="refresh-icon">
                    <path d="M23 4v6h-6"></path>
                    <path d="M20.49 15a9 9 0 11-2.12-9.36L23 10"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
          
          <div class="category-filter">
            <!-- 添加背景遮罩 -->
            <div v-if="showFilterDropdown" class="filter-dropdown-backdrop" @click="showFilterDropdown = false"></div>
            
            <!-- PC端过滤图标 -->
            <div class="filter-icon" v-if="!isMobile" @click="toggleFilterDropdown">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotate': showFilterDropdown }">
                <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
              </svg>
            </div>
            
            <!-- PC端过滤下拉框 -->
            <div v-if="showFilterDropdown && !isMobile" class="filter-dropdown">
              <div class="filter-options">
                <button class="filter-option" :class="{ 'active': activeFilter === 'recommend' }" @click="setActiveFilter('recommend')">
                  <span>Recommend</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'favorite' }" @click="setActiveFilter('favorite')">
                  <span>Favorite</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'amountSold' }" @click="setActiveFilter('amountSold')">
                  <span>Amount Sold</span>
                </button>
                <button class="filter-option" :class="{ 'active': activeFilter === 'price' }" @click="setActiveFilter('price')">
                  <span>Price</span>
                </button>
                <div class="filter-option seller-option" :class="{ 'active': activeFilter === 'seller' }" @click="setActiveFilter('seller')">
                  <span>Seller</span>
                  <div class="arrow-container">
                    <svg class="arrow-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('asc')">
                      <polyline points="18 15 12 9 6 15"></polyline>
                    </svg>
                    <svg class="arrow-down" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('desc')">
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </div>
                </div>
                <button class="filter-option refresh" @click="refreshFilter">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="refresh-icon">
                    <path d="M23 4v6h-6"></path>
                    <path d="M20.49 15a9 9 0 11-2.12-9.36L23 10"></path>
                  </svg>
                </button>
              </div>
            </div>
            
            <!-- PC端分类标签 -->
            <div class="filter-chips-container" v-if="!isMobile">
              <div class="filter-chips">
                <div 
                  v-for="(category, index) in categories" 
                  :key="`cat-${index}`" 
                  class="filter-chip"
                  :class="{ 'active': selectedCategory === category.id }"
                  @click="toggleCategoryDropdown(category.id)"
                >
                  {{ category.name }}
                  <span v-if="selectedCategory === category.id" class="dropdown-arrow">▼</span>
                  <span v-else class="dropdown-arrow">▼</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 品牌下拉菜单 - 作为单独元素展示 -->
          <div 
            class="brands-dropdown-container" 
            v-if="selectedCategory !== null && categoryBrands.length > 0" 
            :style="dropdownPosition"
            :class="{ 'mobile-dropdown': isMobile }"
          >
            <div class="brands-dropdown">
              <div class="dropdown-header" v-if="isMobile">
                <span>选择品牌</span>
                <button class="close-btn" @click="selectedCategory = null">✕</button>
              </div>
              <div 
                v-for="(brand, bIndex) in categoryBrands" 
                :key="`brand-${bIndex}`" 
                class="brand-dropdown-item"
                :class="{ 'active': selectedBrand === brand.id }"
                @click="selectBrand(brand.id)"
              >
                {{ brand.name }}
              </div>
            </div>
          </div>
        </div>
        
        <div class="hero-slider" v-show="!isMobile">
          <div class="slider-controls">
            <button class="slider-arrow prev">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <button class="slider-arrow next">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>
          <div class="slider-content">
            <h2 class="slider-title">This is a title anything you can type</h2>
            <div class="slider-actions">
              <button class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
              </button>
              <button class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              </button>
              <button class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </button>
              <button class="action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
              </button>
            </div>
          </div>
        </div>
        
        <!-- 移动端轮播图 -->
        <div class="mobile-hero-slider" v-show="isMobile">
          <button class="mobile-slider-arrow prev">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          <button class="mobile-slider-arrow next">
            <svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
          
          <div class="mobile-slider-content">
            <h2 class="mobile-slider-title">This is a title anything you can type</h2>
            <div class="mobile-slider-actions">
              <button class="mobile-action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                </svg>
              </button>
              <button class="mobile-action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                </svg>
              </button>
              <button class="mobile-action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                  <circle cx="12" cy="12" r="3"></circle>
                </svg>
              </button>
              <button class="mobile-action-button">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
                  <line x1="22" y1="2" x2="11" y2="13"></line>
                  <polygon points="22 2 15 22 11 13 2 9 22 2"></polygon>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 现有的TRENDING NOW区域 -->
      <div class="trending-products-container">
        <div class="header-section">
          <h2 class="trending-title">TRENDING NOW</h2>
          <div class="discount-code">Discount Code: OMGBUY 15% OFF</div>
        </div>
        
        <div class="products-grid">
          <ProductCard 
            v-for="(product, index) in products" 
            :key="`product-${index}`" 
            :product="product" 
            :showTag="index === 0" 
            :showCheck="index === 2" 
          />
        </div>
      </div>
      
      <!-- 新增 FRESHLY QC 部分 -->
      <div class="freshly-qc-container">
        <h2 class="freshly-qc-title">FRESHLY QC</h2>
        
        <div v-if="qcLoading" class="qc-loading">
          <div class="loading-spinner"></div>
          <p>加载QC数据中...</p>
        </div>
        
        <div v-else-if="validQcGroups && validQcGroups.length > 0" class="qc-groups-grid">
          <!-- 轮播控制按钮 -->
          <div class="qc-carousel-controls">
            <button class="qc-carousel-btn prev" @click="nextImage">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="15 18 9 12 15 6"></polyline>
              </svg>
            </button>
            <button class="qc-carousel-btn next" @click="nextImage">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>
          
          <!-- 各个QC组 -->
          <div 
            class="qc-group" 
            v-for="(group, groupIndex) in validQcGroups" 
            :key="groupIndex"
          >
            <template v-if="group.length > 0">
              <div class="qc-group-header">
                <span>{{ group[0]?.groupName || `Group ${groupIndex + 1}` }}</span>
                <span class="group-count">
                  {{ (currentImageIndex % group.length) + 1 }}/{{ group.length }}
                </span>
              </div>
              <div class="qc-group-content">
                <img 
                  :src="getImageUrl(group, currentImageIndex)" 
                  :alt="getImageAlt(group, currentImageIndex)"
                  class="qc-image"
                  @error="handleImageError"
                >
              </div>
            </template>
          </div>
        </div>
        
        <div v-else class="qc-empty">
          <p>暂无QC数据</p>
        </div>
      </div>
      
      <!-- 新增 CREATOR CHOICE 部分 -->
      <div class="creator-choice-container">
        <h2 class="creator-choice-title">CREATOR CHOICE</h2>
        
        <div class="creator-choice-carousel">
          <!-- 轮播控制按钮 -->
          <button class="carousel-arrow prev" @click="scrollCreatorCarousel('prev')">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="15 18 9 12 15 6"></polyline>
            </svg>
          </button>
          <button class="carousel-arrow next" @click="scrollCreatorCarousel('next')">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
          </button>
          
          <!-- 轮播内容 -->
          <div class="creator-choice-slides">
            <div class="creator-choice-slide" v-for="video in creatorVideos" :key="video.id">
              <div class="creator-video">
                <div class="video-container">
                  <div class="video-placeholder" @click="playCreatorVideo(video.id)">
                    <div class="play-button" :id="`play-button-${video.id}`">
                      <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="white" stroke="none">
                        <polygon points="5 3 19 12 5 21 5 3"></polygon>
                      </svg>
                    </div>
                    <img :src="video.thumbnail" alt="Creator video thumbnail" :id="`thumbnail-${video.id}`">
                    <video 
                      :id="`video-${video.id}`" 
                      :src="video.videoUrl" 
                      class="creator-video-element" 
                      preload="auto"
                      playsinline
                      controls
                      muted
                      webkit-playsinline
                      x5-playsinline
                      @error="(e) => console.error('视频加载错误:', e)"
                    ></video>
                  </div>
                  <div class="creator-avatar">
                    <img :src="video.avatar" alt="Creator avatar">
                  </div>
                </div>
                <div class="creator-info">
                  <div class="creator-name">{{ video.username }}</div>
                  <div class="creator-description">{{ video.description }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加全屏分类菜单 -->
    <div class="category-fullscreen" v-if="showCategoryMenu">
      <div class="category-fullscreen-header">
        <h2>CATEGORY</h2>
        <button class="close-btn" @click="toggleCategoryMenu">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
      </div>
      
      <div class="category-fullscreen-content">
        <div class="category-sidebar">
          <!-- 推荐分类 -->
          <div class="category-sidebar-item" :class="{ 'active': activeSidebarItem === 'recommended' }" @click="setActiveSidebarItem('recommended')">
            <div class="check-icon" v-if="activeSidebarItem === 'recommended'">✓</div>
            <span>OMGBUY Recommends</span>
          </div>

          <!-- 动态分类列表 -->
          <div
            v-for="category in categories.slice(0, 5)"
            :key="category.id"
            class="category-sidebar-item"
            :class="{ 'active': activeSidebarItem === category.id }"
            @click="setActiveSidebarItem(category.id)"
          >
            <div class="check-icon" v-if="activeSidebarItem === category.id">✓</div>
            <span :class="{ 'active-text': activeSidebarItem === category.id }">{{ category.name }}</span>
          </div>
        </div>
        
        <div class="category-content">
          <div class="category-grid">
            <div class="category-card" v-for="(item, index) in currentCategoryItems" :key="item.id || index" @click="selectCategoryItem(item)">
              <div class="category-image">
                <img :src="item.image || 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg'" :alt="item.name">
              </div>
              <div class="category-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import ProductCard from '@/components/ProductCard.vue'
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { getSummerNewProductsList } from '@/api/summerNewProducts'
import productsApi from '@/api/products'
import { getQcGroupsList } from '@/api/qcGroups'

export default {
  name: 'TrendingProductsView',
  components: {
    ProductCard
  },
  setup() {
    const products = ref([])
    const loading = ref(false)
    const error = ref(null)
    const categories = ref([])
    const selectedCategory = ref(null)
    const showAllCategories = ref(true)
    const categoryBrands = ref([])
    const selectedBrand = ref(null)
    const brandsByCategory = ref({}) // 存储每个分类对应的品牌列表
    const isMobile = ref(window.innerWidth <= 480)
    
    // 全屏分类菜单相关状态
    const showCategoryMenu = ref(false)
    const activeSidebarItem = ref('recommended')
    // 分类菜单数据 - 使用API数据
    const categoryItems = ref({})
    
    // 计算当前显示的分类项目
    const currentCategoryItems = computed(() => {
      if (activeSidebarItem.value === 'recommended') {
        // 推荐分类显示所有分类
        return categories.value.slice(0, 6); // 限制显示6个
      } else {
        // 其他分类显示对应的品牌数据
        const categoryId = activeSidebarItem.value;
        return brandsByCategory.value[categoryId] || [];
      }
    })
    
    // 切换分类菜单显示状态
    const toggleCategoryMenu = () => {
      showCategoryMenu.value = !showCategoryMenu.value
      
      // 如果打开菜单，添加body的overflow:hidden防止滚动
      if (showCategoryMenu.value) {
        document.body.style.overflow = 'hidden'
        // 添加show类来触发动画
        setTimeout(() => {
          const menuElement = document.querySelector('.category-fullscreen')
          if (menuElement) {
            menuElement.classList.add('show')
          }
        }, 10)
      } else {
        // 先移除show类，等动画结束后再恢复滚动
        const menuElement = document.querySelector('.category-fullscreen')
        if (menuElement) {
          menuElement.classList.remove('show')
          setTimeout(() => {
            document.body.style.overflow = ''
          }, 300) // 等待动画结束
        } else {
          document.body.style.overflow = ''
        }
      }
    }
    
    // 设置活动侧边栏项目
    const setActiveSidebarItem = async (item) => {
      activeSidebarItem.value = item

      // 如果不是推荐分类，需要获取对应分类的品牌数据
      if (item !== 'recommended' && !brandsByCategory.value[item]) {
        try {
          await fetchBrandsByCategory(item);
        } catch (error) {
          console.error('获取分类品牌数据失败:', error);
        }
      }
    }
    
    // 选择分类项目
    const selectCategoryItem = async (item) => {
      console.log('选择分类项目:', item)

      if (activeSidebarItem.value === 'recommended') {
        // 如果是推荐分类，选择的是分类
        selectedCategory.value = item.id
        selectedBrand.value = null

        // 获取该分类的产品
        await fetchProducts()
      } else {
        // 如果是其他分类，选择的是品牌
        selectedCategory.value = activeSidebarItem.value // 设置当前选中的分类
        selectedBrand.value = item.id // 设置选中的品牌

        // 获取该品牌的产品
        await fetchProducts()
      }

      toggleCategoryMenu() // 选择后关闭菜单
    }
    
    // 为FRESHLY QC部分添加数据
    const freshlyQcGroups = ref([])
    const qcLoading = ref(false)
    
    // 添加当前显示的图片索引状态（使用单一索引控制所有组）
    const currentImageIndex = ref(0)
    
    // 计算所有组中图片的最大数量
    const maxImagesPerGroup = computed(() => {
      if (!validQcGroups.value || validQcGroups.value.length === 0) {
        return 0
      }
      
      return validQcGroups.value.reduce((max, group) => 
        Math.max(max, group.length), 0)
    })
    
    // 自动轮播定时器
    const autoplayTimer = ref(null)
    const autoplayInterval = 3000 // 3秒切换一次
    
    // 切换分类显示状态
    const toggleCategoriesVisibility = () => {
      showAllCategories.value = !showAllCategories.value;
      console.log('切换分类可见性:', showAllCategories.value);
    }
    
    // 选择分类
    const toggleCategoryDropdown = async (categoryId) => {
      console.log('toggleCategoryDropdown被调用，分类ID:', categoryId);
      
      // 检查categoryId是否有效
      if (categoryId === undefined || categoryId === null) {
        console.error('无效的分类ID:', categoryId);
        return;
      }
      
      if (selectedCategory.value === categoryId) {
        // 如果点击的是当前选中的分类，则关闭下拉菜单
        selectedCategory.value = null;
        categoryBrands.value = [];
      } else {
        // 切换到新的分类
        selectedCategory.value = categoryId;
        selectedBrand.value = null;
        
        // 计算下拉菜单位置
        calculateDropdownPosition();
        
        // 如果已经缓存了该分类的品牌数据，直接使用缓存
        if (brandsByCategory.value[categoryId]) {
          categoryBrands.value = brandsByCategory.value[categoryId];
          console.log('使用缓存的品牌数据:', categoryBrands.value);
        } else {
          // 否则，获取该分类下的品牌
          await fetchBrandsByCategory(categoryId);
        }
      }
      console.log('切换分类:', categoryId, '下拉菜单位置:', dropdownPosition.value);
    }
    
    // 下拉菜单位置
    const dropdownPosition = ref({
      position: 'absolute',
      top: '0px',
      left: '0px',
      bottom: 'auto',
      width: 'auto'
    });
    
    // 计算下拉菜单位置
    const calculateDropdownPosition = () => {
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        const selectedChip = document.querySelector(`.filter-chip.active`);
        if (selectedChip) {
          const rect = selectedChip.getBoundingClientRect();
          
          // 根据设备类型设置下拉菜单位置
          if (isMobile.value) {
            // 移动端显示在分类正下方，紧贴分类
            dropdownPosition.value = {
              position: 'fixed',
              top: `${rect.bottom + 5}px`,
              left: '50%',
              transform: 'translateX(-50%)',
              width: '90%',
              maxWidth: '350px',
              bottom: 'auto'
            };
          } else {
            // PC端显示在分类下方
            dropdownPosition.value = {
              position: 'absolute',
              top: `${rect.bottom + window.scrollY + 5}px`,
              left: `${rect.left + window.scrollX}px`,
              bottom: 'auto',
              width: '180px',
              transform: 'none'
            };
          }
        }
      }, 10);
    };
    
    // 选择品牌
    const selectBrand = (brandId) => {
      if (selectedBrand.value === brandId) {
        selectedBrand.value = null;
      } else {
        selectedBrand.value = brandId;
      }
      console.log('选中品牌:', brandId);
      // 这里可以添加根据品牌筛选产品的逻辑
    }
    
    // 获取分类下的品牌
    const fetchBrandsByCategory = async (categoryId) => {
      try {
        // 检查categoryId是否有效
        if (categoryId === undefined || categoryId === null) {
          console.error('无效的分类ID:', categoryId);
          return;
        }
        
        // 确保categoryId是字符串类型
        const catId = categoryId.toString();
        console.log('正在获取分类ID:', catId, '的品牌数据');
        
        // 调用API获取品牌，确保传递categoryId参数
        const response = await productsApi.getBrandsByCategory(catId);
        console.log('分类ID:', catId, '的品牌数据:', response);
        
        if (response && response.data && Array.isArray(response.data)) {
          categoryBrands.value = response.data;
          brandsByCategory.value[categoryId] = response.data;
        } else if (Array.isArray(response)) {
          categoryBrands.value = response;
          brandsByCategory.value[categoryId] = response;
        } else {
          // 没有品牌数据或数据格式不符合预期，使用模拟数据
          console.log('使用模拟数据替代API返回的数据');
          const mockData = [
            { id: 1, name: 'Hoodies', parentCategoryId: categoryId },
            { id: 2, name: 'T-Shirt', parentCategoryId: categoryId },
            { id: 3, name: 'Pants', parentCategoryId: categoryId },
            { id: 4, name: 'Sets', parentCategoryId: categoryId }
          ];
          categoryBrands.value = mockData;
          brandsByCategory.value[categoryId] = mockData;
        }
      } catch (err) {
        console.error('获取品牌数据失败:', err, '分类ID:', categoryId);
        // 发生错误时使用模拟数据
        const mockData = [
          { id: 1, name: 'Hoodies', parentCategoryId: categoryId },
          { id: 2, name: 'T-Shirt', parentCategoryId: categoryId },
          { id: 3, name: 'Pants', parentCategoryId: categoryId },
          { id: 4, name: 'Sets', parentCategoryId: categoryId }
        ];
        categoryBrands.value = mockData;
        brandsByCategory.value[categoryId] = mockData;
      }
    }
    
    // 处理下一张图片按钮点击
    const nextImage = () => {
      if (!validQcGroups.value || validQcGroups.value.length === 0) return
      
      // 更新当前索引，循环显示
      currentImageIndex.value = (currentImageIndex.value + 1) % maxImagesPerGroup.value
    }
    
    // 启动自动轮播
    const startAutoplay = () => {
      // 防止在服务器端渲染时调用
      if (typeof window === 'undefined') return
      
      // 检查是否有有效的QC组
      if (!validQcGroups.value || validQcGroups.value.length === 0) return
      
      // 先清除可能存在的定时器
      if (autoplayTimer.value) {
        clearInterval(autoplayTimer.value)
      }
      
      // 设置新的定时器
      autoplayTimer.value = setInterval(() => {
        nextImage()
      }, autoplayInterval)
    }
    
    // 停止自动轮播
    const stopAutoplay = () => {
      if (autoplayTimer.value) {
        clearInterval(autoplayTimer.value)
        autoplayTimer.value = null
      }
    }
    
    // 获取QC组数据
    const fetchQcGroups = async () => {
      qcLoading.value = true
      try {
        const qcData = await getQcGroupsList()
        
        if (Array.isArray(qcData) && qcData.length > 0) {
          // 过滤掉空组或无效组
          const validGroups = qcData.filter(group => Array.isArray(group) && group.length > 0)
          freshlyQcGroups.value = validGroups
          
          // 只有在有有效数据时才启动轮播
          if (validGroups.length > 0) {
            // 延迟启动自动轮播，确保DOM已渲染
            setTimeout(() => {
              if (maxImagesPerGroup.value > 1) {
                startAutoplay()
              }
            }, 500)
          }
        } else {
          console.warn('未获取到QC组数据或数据格式不正确，使用模拟数据')
          // 使用模拟数据作为备用
          useMockQcData()
        }
      } catch (err) {
        console.error('获取QC组数据失败:', err)
        // 使用模拟数据作为备用
        useMockQcData()
      } finally {
        qcLoading.value = false
      }
    }
    
    // 使用模拟数据
    const useMockQcData = () => {
      try {
        const mockGroups = [
          [
            {
              id: '1_0',
              groupId: '1',
              groupName: 'Group 1',
              count: '1/4',
              description: 'Group 1 Image 1',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '1'
            },
            {
              id: '1_1',
              groupId: '1',
              groupName: 'Group 1',
              count: '2/4',
              description: 'Group 1 Image 2',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '1'
            },
            {
              id: '1_2',
              groupId: '1',
              groupName: 'Group 1',
              count: '3/4',
              description: 'Group 1 Image 3',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '1'
            },
            {
              id: '1_3',
              groupId: '1',
              groupName: 'Group 1',
              count: '4/4',
              description: 'Group 1 Image 4',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '1'
            }
          ],
          [
            {
              id: '2_0',
              groupId: '2',
              groupName: 'Group 2',
              count: '1/4',
              description: 'Group 2 Image 1',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '2'
            },
            {
              id: '2_1',
              groupId: '2',
              groupName: 'Group 2',
              count: '2/4',
              description: 'Group 2 Image 2',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '2'
            },
            {
              id: '2_2',
              groupId: '2',
              groupName: 'Group 2',
              count: '3/4',
              description: 'Group 2 Image 3',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '2'
            },
            {
              id: '2_3',
              groupId: '2',
              groupName: 'Group 2',
              count: '4/4',
              description: 'Group 2 Image 4',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '2'
            }
          ],
          [
            {
              id: '3_0',
              groupId: '3',
              groupName: 'Group 3',
              count: '1/4',
              description: 'Group 3 Image 1',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '3'
            },
            {
              id: '3_1',
              groupId: '3',
              groupName: 'Group 3',
              count: '2/4',
              description: 'Group 3 Image 2',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '3'
            },
            {
              id: '3_2',
              groupId: '3',
              groupName: 'Group 3',
              count: '3/4',
              description: 'Group 3 Image 3',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '3'
            },
            {
              id: '3_3',
              groupId: '3',
              groupName: 'Group 3',
              count: '4/4',
              description: 'Group 3 Image 4',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '3'
            }
          ],
          [
            {
              id: '4_0',
              groupId: '4',
              groupName: 'Group 4',
              count: '1/4',
              description: 'Group 4 Image 1',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '4'
            },
            {
              id: '4_1',
              groupId: '4',
              groupName: 'Group 4',
              count: '2/4',
              description: 'Group 4 Image 2',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '4'
            },
            {
              id: '4_2',
              groupId: '4',
              groupName: 'Group 4',
              count: '3/4',
              description: 'Group 4 Image 3',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '4'
            },
            {
              id: '4_3',
              groupId: '4',
              groupName: 'Group 4',
              count: '4/4',
              description: 'Group 4 Image 4',
              imageUrl: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
              position: '4'
            }
          ]
        ]
        
        freshlyQcGroups.value = mockGroups
        
        // 延迟启动自动轮播，确保DOM已渲染
        if (typeof window !== 'undefined') {
          setTimeout(() => {
            if (maxImagesPerGroup.value > 1) {
              startAutoplay()
            }
          }, 500)
        }
      } catch (error) {
        console.error('初始化模拟数据失败:', error)
        // 出错时设置为空数组，不启动轮播
        freshlyQcGroups.value = []
      }
    }
    
    const fetchProducts = async () => {
      loading.value = true
      try {
        // 使用summerNewProducts.js中的接口获取数据
        const productData = await getSummerNewProductsList()
        
        console.log('API返回数据:', productData) // 调试用，查看接口返回的数据格式
        
        if (Array.isArray(productData) && productData.length > 0) {
          const processedData = productData.map(item => {
            // 确保每个商品对象都有必要的字段
            return {
              ...item,
              // 如果没有以下字段，则设置默认值
              id: item.id || item.itemId || item.productId || '000',
              name: item.name || item.title || item.productName || 'Product Name',
              price: item.price || item.salePrice || item.newPrice || '0',
              likes: item.likes || item.likeCount || '0',
              views: item.views || item.viewCount || '0',
              comments: item.comments || item.commentCount || '0',
              image: item.image || item.imageUrl || item.thumbnail || 'https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg',
              platform: item.platform || 'Amazon'
            }
          })
          
          products.value = processedData
          console.log('处理后的数据:', products.value) // 调试用，查看处理后的数据
        } else {
          // 如果API返回空数据，使用模拟数据
          console.log('使用模拟数据') // 调试用
          products.value = [
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "371"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "372"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "373"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "374"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "375"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "376"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "377"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "378"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "379"
            },
            { 
              id: "1688", 
              name: "Supreme James Jebbia Clothes", 
              price: "5.3", 
              likes: "1.5k", 
              comments: "81", 
              views: "34", 
              image: "https://si.geilicdn.com/open1618703805-1234478995-05a10000019762ececfa0a8115c2_800_800.jpg",
              platform: "380"
            }
          ]
        }
      } catch (err) {
        console.error('Error fetching summer new products:', err)
        error.value = 'Failed to load trending products'
      } finally {
        loading.value = false
      }
    }

    // 获取所有分类
    const fetchCategories = async () => {
      try {
        const response = await productsApi.getAllCategories()
        console.log('分类数据原始响应:', response) // Debug log
        
        // Handle different response structures
        if (response && response.data && Array.isArray(response.data)) {
          // 确保每个分类对象都有id属性
          categories.value = response.data.map(cat => {
            return {
              ...cat,
              id: cat.id || cat.categoryId || cat._id || String(Math.random()).substring(2, 10)
            };
          });
          console.log('处理后的分类数据:', categories.value);

          // 预加载前几个分类的品牌数据
          const preloadCategories = categories.value.slice(0, 3);
          for (const category of preloadCategories) {
            try {
              await fetchBrandsByCategory(category.id);
            } catch (error) {
              console.warn(`预加载分类 ${category.name} 的品牌数据失败:`, error);
            }
          }
        } else if (Array.isArray(response)) {
          // 确保每个分类对象都有id属性
          categories.value = response.map(cat => {
            return {
              ...cat,
              id: cat.id || cat.categoryId || cat._id || String(Math.random()).substring(2, 10)
            };
          });
          console.log('处理后的分类数据:', categories.value);

          // 预加载前几个分类的品牌数据
          const preloadCategories = categories.value.slice(0, 3);
          for (const category of preloadCategories) {
            try {
              await fetchBrandsByCategory(category.id);
            } catch (error) {
              console.warn(`预加载分类 ${category.name} 的品牌数据失败:`, error);
            }
          }
        } else {
          // Fallback to mock data if API returns unexpected format
          console.warn('API返回格式不符合预期，使用模拟数据');
          categories.value = [
            { id: 1, name: '夏季新品' },
            { id: 2, name: '最新上架' },
            { id: 3, name: '服装' },
            { id: 4, name: '鞋靴' },
            { id: 5, name: '配饰' },
            { id: 6, name: '手表' },
            { id: 7, name: '眼镜' },
            { id: 8, name: '手包' },
            { id: 9, name: '运动' },
            { id: 10, name: '休闲' }
          ]
        }
      } catch (err) {
        console.error('获取分类数据失败:', err)
        error.value = 'Failed to load categories'
        // Fallback to mock data
        categories.value = [
          { id: 1, name: '夏季新品' },
          { id: 2, name: '最新上架' },
          { id: 3, name: '服装' },
          { id: 4, name: '鞋靴' },
          { id: 5, name: '配饰' },
          { id: 6, name: '手表' },
          { id: 7, name: '眼镜' },
          { id: 8, name: '手包' },
          { id: 9, name: '运动' },
          { id: 10, name: '休闲' }
        ]
      }
    }

    // 添加点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      // 如果点击的不是分类或其子元素，也不是品牌下拉菜单
      const filterChips = document.querySelectorAll('.filter-chip');
      const brandsDropdown = document.querySelector('.brands-dropdown-container');
      let clickedInside = false;
      
      // 检查是否点击在分类上
      filterChips.forEach(chip => {
        if (chip.contains(event.target)) {
          clickedInside = true;
        }
      });
      
      // 检查是否点击在品牌下拉菜单上
      if (brandsDropdown && brandsDropdown.contains(event.target)) {
        clickedInside = true;
      }
      
      // 如果点击在外部，关闭下拉菜单
      if (!clickedInside && selectedCategory.value !== null) {
        selectedCategory.value = null;
        categoryBrands.value = [];
      }
    };

    // 添加窗口尺寸变化监听
    const handleResize = () => {
      isMobile.value = window.innerWidth <= 480;
      // 重新设置分类样式
      setupCategoriesStyle();
      if (selectedCategory.value !== null) {
        // 重新计算下拉菜单位置
        calculateDropdownPosition();
      }
    };

    // 设置分类样式
    const setupCategoriesStyle = () => {
      if (typeof window === 'undefined') return
      
      setTimeout(() => {
        const filterChips = document.querySelector('.filter-chips')
        if (filterChips) {
          // 根据设备类型设置样式
          if (isMobile.value) {
            filterChips.style.flexWrap = 'wrap'
            filterChips.style.width = '100%'
          } else {
            // PC端可以根据需要设置不同样式
            filterChips.style.flexWrap = 'wrap'
          }
        }
      }, 100)
    }
    
    // 初始化视频元素
    const initVideoElements = () => {
      console.log('初始化视频元素')
      setTimeout(() => {
        creatorVideos.value.forEach(video => {
          const videoElement = document.getElementById(`video-${video.id}`)
          const thumbnail = document.getElementById(`thumbnail-${video.id}`)
          const playButton = document.getElementById(`play-button-${video.id}`)
          
          if (videoElement && thumbnail && playButton) {
            // 确保视频元素初始状态正确
            videoElement.style.display = 'none'
            thumbnail.style.display = 'block'
            playButton.style.display = 'flex'
            
            // 添加视频点击事件（视频播放时点击暂停/继续）
            videoElement.addEventListener('click', () => {
              if (videoElement.paused) {
                videoElement.play()
              } else {
                videoElement.pause()
              }
            })
            
            // 预加载视频
            videoElement.load()
          }
        })
      }, 500)
    }
    
    onMounted(() => {
      // 确保在浏览器环境中执行
      if (typeof window !== 'undefined') {
        fetchProducts()
        fetchCategories()
        fetchQcGroups() // 获取QC组数据
        initVideoElements() // 初始化视频元素
        // 设置分类样式
        setupCategoriesStyle()
        // 添加全局点击事件
        document.addEventListener('click', handleClickOutside)
        // 添加窗口尺寸变化事件
        window.addEventListener('resize', handleResize)
      }
    })
    
    onUnmounted(() => {
      // 确保在浏览器环境中执行
      if (typeof window !== 'undefined') {
        // 移除全局点击事件
        document.removeEventListener('click', handleClickOutside)
        // 移除窗口尺寸变化事件
        window.removeEventListener('resize', handleResize)
        // 清除自动轮播定时器
        stopAutoplay()
        // 如果分类菜单打开，关闭它
        if (showCategoryMenu.value) {
          document.body.style.overflow = ''
        }
      }
    })

    // 获取当前组的当前图片
    const getCurrentImageForGroup = (group, currentIndex) => {
      if (!group || !Array.isArray(group) || group.length === 0) {
        return null
      }
      
      const index = currentIndex % group.length
      return group[index] || null
    }
    
    // 默认图片URL
    const defaultImageUrl = 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg'
    
    // 获取图片URL
    const getImageUrl = (group, index) => {
      if (!group || !Array.isArray(group) || group.length === 0) {
        return defaultImageUrl
      }
      
      const currentIndex = index % group.length
      const image = group[currentIndex]
      
      return image?.imageUrl || defaultImageUrl
    }
    
    // 获取图片描述
    const getImageAlt = (group, index) => {
      if (!group || !Array.isArray(group) || group.length === 0) {
        return 'Default image'
      }
      
      const currentIndex = index % group.length
      const image = group[currentIndex]
      
      return image?.description || 'QC image'
    }
    
    // 处理图片加载错误
    const handleImageError = (e) => {
      e.target.src = defaultImageUrl
    }
    
    // 添加计算属性，过滤有效的QC组
    const validQcGroups = computed(() => {
      if (!freshlyQcGroups.value || !Array.isArray(freshlyQcGroups.value)) {
        return []
      }
      
      return freshlyQcGroups.value.filter(group => 
        Array.isArray(group) && group.length > 0
      )
    })

    // 过滤下拉框显示状态
    const showFilterDropdown = ref(false)
    
    // 当前激活的过滤选项
    const activeFilter = ref('recommend')
    
    // 排序方向
    const sortDirection = ref('asc')
    
    // 切换过滤下拉框显示状态
    const toggleFilterDropdown = () => {
      // 恢复原有功能，不再区分移动端和PC端
      showFilterDropdown.value = !showFilterDropdown.value
      
      // 点击外部区域关闭下拉框
      if (showFilterDropdown.value) {
        setTimeout(() => {
          document.addEventListener('click', handleOutsideClick)
        }, 10)
      } else {
        document.removeEventListener('click', handleOutsideClick)
      }
    }
    
    // 设置激活的过滤选项
    const setActiveFilter = (filter) => {
      activeFilter.value = filter
      
      // 这里可以添加根据过滤选项排序的逻辑
      console.log('设置过滤选项:', filter)
      
      // 关闭下拉框
      showFilterDropdown.value = false
    }
    
    // 刷新过滤
    const refreshFilter = () => {
      console.log('刷新过滤')
      // 这里可以添加重置过滤的逻辑
      
      // 不关闭下拉框
    }
    
    // 处理点击外部区域
    const handleOutsideClick = (event) => {
      const filterIcon = document.querySelector('.filter-icon')
      const filterDropdown = document.querySelector('.filter-dropdown')
      
      if (filterIcon && filterDropdown) {
        // 如果点击的不是过滤图标或下拉框内的元素，则关闭下拉框
        if (!filterIcon.contains(event.target) && !filterDropdown.contains(event.target)) {
          showFilterDropdown.value = false
          document.removeEventListener('click', handleOutsideClick)
        }
      }
    }
    
    // 组件卸载时移除事件监听器
    onUnmounted(() => {
      document.removeEventListener('click', handleOutsideClick)
    })

    // 设置排序方向
    const setSortDirection = (direction) => {
      sortDirection.value = direction
      activeFilter.value = 'seller' // 自动选择seller作为过滤条件
      console.log('设置排序方向:', direction)
      
      // 这里可以添加根据排序方向进行排序的逻辑
      
      // 不关闭下拉框，让用户可以继续交互
    }

    // 为CREATOR CHOICE部分添加数据
    const creatorVideos = ref([
      {
        id: 1,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 2,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg', 
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 3,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 4,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 5,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      },
      {
        id: 6,
        videoUrl: 'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/05/ebf080c2105e2d35e25e41ee8ab07b79.mp4',
        thumbnail: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        avatar: 'https://cdn.findqc.com/bt/20250607/mbme4p4h_lete.jpg',
        username: '@craig_love',
        description: 'The most satisfying job #fyp #satisfying #roadmarking'
      }
    ])
    
    // 视频播放状态
    const playingVideoId = ref(null)
    
    // 播放视频
    const playCreatorVideo = (videoId) => {
      console.log('尝试播放视频:', videoId)
      
      const videoElement = document.getElementById(`video-${videoId}`)
      const thumbnail = document.getElementById(`thumbnail-${videoId}`)
      const playButton = document.getElementById(`play-button-${videoId}`)
      
      if (!videoElement || !thumbnail || !playButton) {
        console.error('未找到视频元素或缩略图元素')
        return
      }
      
      // 如果点击的是当前正在播放的视频，则切换播放/暂停状态
      if (playingVideoId.value === videoId) {
        if (videoElement.paused) {
          videoElement.play()
          console.log('继续播放视频:', videoId)
        } else {
          videoElement.pause()
          console.log('暂停视频:', videoId)
        }
        return
      }
      
      // 如果有其他视频正在播放，先停止它
      if (playingVideoId.value !== null) {
        const prevVideo = document.getElementById(`video-${playingVideoId.value}`)
        const prevThumbnail = document.getElementById(`thumbnail-${playingVideoId.value}`)
        const prevPlayButton = document.getElementById(`play-button-${playingVideoId.value}`)
        
        if (prevVideo) {
          prevVideo.pause()
          prevVideo.currentTime = 0
          prevVideo.style.display = 'none'
        }
        
        if (prevThumbnail) prevThumbnail.style.display = 'block'
        if (prevPlayButton) prevPlayButton.style.display = 'flex'
      }
      
      // 设置当前播放的视频ID
      playingVideoId.value = videoId
      
      // 隐藏缩略图和播放按钮
      thumbnail.style.display = 'none'
      playButton.style.display = 'none'
      
      // 显示视频并播放
      videoElement.style.display = 'block'
      
      // 确保视频适合容器
      videoElement.style.objectFit = 'cover'
      
      // 尝试播放视频
      try {
        const playPromise = videoElement.play()
        
        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              console.log('视频开始播放:', videoId)
            })
            .catch(err => {
              console.error('视频播放失败:', err)
              // 如果自动播放失败，显示播放按钮
              thumbnail.style.display = 'block'
              playButton.style.display = 'flex'
              videoElement.style.display = 'none'
              playingVideoId.value = null
            })
        }
      } catch (err) {
        console.error('播放视频时出错:', err)
        // 恢复到初始状态
        thumbnail.style.display = 'block'
        playButton.style.display = 'flex'
        videoElement.style.display = 'none'
        playingVideoId.value = null
      }
      
      // 视频结束后恢复缩略图
      videoElement.onended = () => {
        console.log('视频播放结束:', videoId)
        videoElement.style.display = 'none'
        thumbnail.style.display = 'block'
        playButton.style.display = 'flex'
        playingVideoId.value = null
      }
    }

    // 轮播相关方法
    const scrollCreatorCarousel = (direction) => {
      const container = document.querySelector('.creator-choice-slides')
      if (!container) return
      
      const slideWidth = container.querySelector('.creator-choice-slide')?.offsetWidth || 0
      const scrollAmount = slideWidth + 20 // 20px是slide之间的间距
      
      if (direction === 'prev') {
        container.scrollBy({ left: -scrollAmount, behavior: 'smooth' })
      } else {
        container.scrollBy({ left: scrollAmount, behavior: 'smooth' })
      }
    }

    return {
      products,
      loading,
      error,
      freshlyQcGroups,
      currentImageIndex,
      nextImage,
      maxImagesPerGroup,
      categories,
      selectedCategory,
      toggleCategoryDropdown,
      showAllCategories,
      toggleCategoriesVisibility,
      categoryBrands,
      selectedBrand,
      selectBrand,
      dropdownPosition,
      isMobile,
      qcLoading,
      validQcGroups,
      getCurrentImageForGroup,
      getImageUrl,
      getImageAlt,
      handleImageError,
      setupCategoriesStyle,
      showFilterDropdown,
      activeFilter,
      toggleFilterDropdown,
      setActiveFilter,
      refreshFilter,
      sortDirection,
      setSortDirection,
      creatorVideos,
      scrollCreatorCarousel,
      playCreatorVideo,
      playingVideoId,
      showCategoryMenu,
      activeSidebarItem,
      categoryItems,
      currentCategoryItems,
      toggleCategoryMenu,
      setActiveSidebarItem,
      selectCategoryItem
    }
  }
}
</script>

<style scoped>
/* ================ 全局页面样式 ================ */
.trending-products-view {
  color: #fff;
  min-height: 100vh;
}

.dark-bg {
  background-color: #050112;
  padding: 40px 20px;
  min-height: 100vh;
}

/* ================ Beyond The Spreadsheet 顶部区域 ================ */
.hero-section {
  max-width: 1400px;
  margin: 0 auto 60px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 40px;
}

/* --- 左侧内容区域 --- */
.hero-content {
  flex: 1;
  max-width: 500px;
}

.hero-title {
  font-size: 42px;
  font-weight: 700;
  margin-bottom: 8px;
  color: #ffffff;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 22px;
  color: #ffffff;
  margin-bottom: 30px;
  font-weight: 400;
}

/* --- 搜索框 --- */
.search-container {
  margin-bottom: 24px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(255, 255, 255, 0.5);
  z-index: 2;
}

.search-input {
  background-color: rgba(30, 20, 50, 0.6);
  border: none;
  outline: none;
  color: #ffffff;
  font-size: 16px;
  width: 100%;
  padding: 12px 16px 12px 45px;
  border-radius: 12px;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

/* --- 分类过滤器 --- */
.category-filter {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  position: relative;
  overflow: visible;
}

.filter-icon {
  margin-right: 16px;
  margin-top: 0;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(142, 45, 226, 0.3);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.filter-icon:hover {
  background-color: rgba(142, 45, 226, 0.5);
  transform: scale(1.05);
}

.filter-icon svg {
  transition: transform 0.3s ease;
  color: white;
}

.filter-icon svg.rotate {
  transform: rotate(180deg);
}

.filter-chips-container {
  flex: 1;
  max-width: 100%;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.filter-chips {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  max-width: 100%;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.filter-chip {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 10px;
  white-space: nowrap;
  position: relative;
  display: flex;
  align-items: center;
  gap: 5px;
}

.filter-chip:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.filter-chip.active {
  background-color: rgba(142, 45, 226, 0.6);
  color: white;
}

.filter-chip.more-button {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: rgba(255, 255, 255, 0.15);
}

.filter-chip.more-button:hover {
  background-color: rgba(255, 255, 255, 0.25);
}

.filter-chip.more-button svg {
  transition: transform 0.3s ease;
}

.filter-chip.more-button svg.rotate {
  transform: rotate(180deg);
}

.expand-indicator {
  position: absolute;
  width: 6px;
  height: 6px;
  background-color: #8e2de2;
  border-radius: 50%;
  bottom: 1px;
  right: 1px;
  transition: all 0.3s ease;
  opacity: 0;
  transform: scale(0);
}

.expand-indicator.expanded {
  opacity: 1;
  transform: scale(1);
}

/* --- 右侧滑块区域 --- */
.hero-slider {
  flex: 1;
  position: relative;
  background: linear-gradient(135deg, #6b3294 0%, #8540ac 100%);
  border-radius: 12px;
  height: 250px;
  overflow: hidden;
}

.slider-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  z-index: 10;
}

.slider-arrow {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: background 0.2s;
}

.slider-arrow:hover {
  background: rgba(255, 255, 255, 0.3);
}

.slider-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20px;
}

.slider-title {
  font-size: 22px;
  color: white;
  margin-bottom: 16px;
  font-weight: 500;
}

.slider-actions {
  display: flex;
  gap: 16px;
}

.action-button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
}

/* ================ TRENDING NOW 产品展示区域 ================ */
.trending-products-container {
  max-width: 1400px;
  margin: 0 auto 60px;
  position: relative;
}

.header-section {
  margin-bottom: 40px;
  text-align: center;
}

.trending-title {
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 16px;
  letter-spacing: 1px;
  text-transform: uppercase;
}

.discount-code {
  color: #e0e0e0;
  font-size: 18px;
  margin-bottom: 30px;
  background: rgba(142, 45, 226, 0.2);
  width: fit-content;
  margin: 0 auto;
  padding: 8px 20px;
  border-radius: 20px;
  backdrop-filter: blur(5px);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  padding: 0 5px;
}

/* ================ FRESHLY QC 质检展示区域 ================ */
.freshly-qc-container {
  max-width: 1400px;
  margin: 0 auto 80px;
  position: relative;
  padding: 40px 20px;
}

.freshly-qc-title {
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  text-align: center;
  margin-bottom: 30px;
}

.qc-groups-grid {
  display: flex;
  overflow-x: auto;
  padding-bottom: 20px;
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  gap: 20px;
  scrollbar-width: thin;
  padding-left: 15px;
  padding-right: 15px;
}

.qc-group {
  flex: 0 0 calc(25% - 15px);
  min-width: 280px;
  background-color: #13101F;
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.qc-group:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

.qc-group-header {
  background-color: #5a33a0;
  color: #ffffff;
  padding: 12px 15px;
  font-size: 14px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.group-count {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 3px 10px;
  font-size: 12px;
}

.qc-group-content {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #13101F;
  padding: 0;
  position: relative;
  overflow: hidden;
}

.qc-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.qc-group:hover .qc-image {
  transform: scale(1.02);
}

.nav-button {
  display: none;
}

/* 优化横向滚动轮播组的样式 */
.qc-groups-grid::-webkit-scrollbar {
  height: 4px;
}

.qc-groups-grid::-webkit-scrollbar-track {
  background: rgba(20, 20, 30, 0.5);
  border-radius: 2px;
}

.qc-groups-grid::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #352458, #5a33a0);
  border-radius: 2px;
}

.qc-groups-grid::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #42306a, #6a43b0);
}

/* 添加滚动条底部的渐变效果 */
.qc-groups-grid::after {
  content: '';
  display: block;
  width: 100%;
  height: 4px;
  position: absolute;
  bottom: 0;
  left: 0;
  background: linear-gradient(90deg, #352458, #5a33a0, #352458);
  opacity: 0.4;
  border-radius: 2px;
}

/* ================ 响应式设计 ================ */
@media (max-width: 1200px) {
  /* 大屏幕响应式调整 */
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  
  .hero-section {
    flex-direction: column;
  }
  
  .hero-content {
    max-width: 100%;
  }
}

@media (max-width: 992px) {
  /* 中等屏幕响应式调整 */
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .qc-group {
    flex: 0 0 calc(33.333% - 10px);
  }
}

@media (max-width: 768px) {
  /* 平板屏幕响应式调整 */
  .hero-section {
    flex-direction: column;
    gap: 20px;
    margin-bottom: 40px;
  }
  
  .hero-content {
    max-width: 100%;
  }
  
  .hero-slider {
    width: 100%;
    margin-top: 20px;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 0 10px;
  }
  
  .trending-title, .freshly-qc-title {
    font-size: 24px;
  }
  
  .discount-code {
    font-size: 16px;
  }
  
  .hero-title {
    font-size: 36px;
  }
  
  .hero-subtitle {
    font-size: 18px;
  }
  
  .qc-group {
    flex: 0 0 calc(50% - 8px);
    min-width: 200px;
  }
}

@media (max-width: 480px) {
  /* 手机屏幕响应式调整 */
  .dark-bg {
    padding: 20px 0;
  }
  
  /* Hero标题区域调整 */
  .hero-section {
    flex-direction: column;
    margin-bottom: 30px;
    padding: 0 20px;
  }
  
  .hero-content {
    max-width: 100%;
    width: 100%;
  }
  
  .hero-title {
    font-size: 28px;
    text-align: center;
  }
  
  .hero-subtitle {
    font-size: 16px;
    text-align: center;
  }
  
  /* 搜索框调整 */
  .search-container {
    padding: 0;
    margin-bottom: 15px;
  }
  
  .search-input {
    height: 42px;
    border-radius: 8px;
  }
  
  /* 过滤器调整 */
  .category-filter {
    padding: 0 10px;
    justify-content: flex-start;
    margin-bottom: 15px;
    flex-wrap: wrap;
  }
  
  .filter-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 36px;
    height: 36px;
    margin-right: 10px;
    margin-bottom: 10px;
    background-color: rgba(142, 45, 226, 0.3);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  .filter-chips-container {
    width: 100%;
    overflow-x: visible;
    padding-left: 0;
  }
  
  .filter-chips {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    padding-bottom: 5px;
    width: 100%;
  }
  
  .filter-chip {
    margin-bottom: 5px;
    white-space: nowrap;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 13px;
  }
  
  /* 轮播图区域调整 */
  .hero-slider {
    width: 100%;
    height: 200px;
    margin-top: 20px;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, #6b3294 0%, #8540ac 100%);
  }
  
  .slider-controls {
    z-index: 10;
  }
  
  .slider-content {
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 15px;
    z-index: 5;
  }
  
  .slider-title {
    font-size: 18px;
    margin-bottom: 10px;
    color: white;
  }
  
  .slider-actions {
    display: flex;
    gap: 12px;
  }
  
  .action-button {
    background: rgba(255, 255, 255, 0.15);
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .slider-arrow {
    width: 32px;
    height: 32px;
  }
  
  /* 品牌下拉菜单调整 */
  .brands-dropdown-container.mobile-dropdown {
    position: fixed;
    top: auto;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 350px;
    z-index: 1000;
    border-radius: 12px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.5);
  }
  
  .mobile-dropdown .brands-dropdown {
    border-radius: 12px;
    max-height: 60vh;
    overflow-y: auto;
    padding-bottom: 10px;
    background-color: #2a1a4a;
  }
  
  .mobile-dropdown .brands-dropdown::before {
    display: none;
  }
  
  .mobile-dropdown .dropdown-header {
    padding: 15px;
    background-color: #3a2a5a;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: sticky;
    top: 0;
    z-index: 2;
  }
  
  .mobile-dropdown .brand-dropdown-item {
    padding: 14px 20px;
    font-size: 15px;
    text-align: left;
  }
  
  /* Trending部分调整 */
  .trending-products-container {
    padding: 0;
  }
  
  .header-section {
    margin-bottom: 20px;
  }
  
  .trending-title {
    font-size: 24px;
    text-align: center;
    margin-bottom: 5px;
  }
  
  .discount-code {
    font-size: 14px;
    padding: 6px 16px;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    margin: 0 auto;
    padding: 0 10px;
    justify-items: center;
  }
  
  /* 调整移动端商品卡片尺寸为223*302 */
  :deep(.product-card) {
    margin-bottom: 0;
    border-radius: 16px;
    width: 223px;
    height: 302px;
    display: flex;
    flex-direction: column;
  }
  
  :deep(.main-image-container) {
    min-height: 200px;
    height: 200px;
  }
  
  :deep(.product-info-section) {
    padding: 8px 10px;
    height: 102px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  
  :deep(.current-price) {
    font-size: 22px;
    font-weight: 700;
  }
  
  :deep(.product-id) {
    font-size: 10px;
    padding: 1px 6px;
  }
  
  :deep(.product-name) {
    margin-bottom: 4px;
    font-size: 12px;
    max-height: 36px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  
  :deep(.social-stats) {
    gap: 10px;
    font-size: 11px;
  }
  
  :deep(.stat-container i) {
    font-size: 12px;
  }
  
  .qc-group {
    flex: 0 0 calc(100% - 30px);
    min-width: 200px;
  }
}

/* 品牌下拉菜单样式 */
.brands-dropdown-container {
  position: absolute;
  z-index: 100;
  width: 180px;
}

.brands-dropdown-container.mobile-dropdown {
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  z-index: 200;
}

.brands-dropdown {
  background-color: #2a1a4a;
  border-radius: 12px;
  padding: 5px 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: fadeIn 0.3s ease;
  overflow: hidden;
  position: relative;
}

.mobile-dropdown .brands-dropdown {
  border-radius: 12px;
  padding-top: 0;
  max-height: 300px;
  overflow-y: auto;
  background-color: #2a1a4a;
}

.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #3a2a5a;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.dropdown-header span {
  font-weight: 500;
  font-size: 14px;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.brands-dropdown::before {
  content: '';
  position: absolute;
  top: -6px;
  left: 20px;
  width: 12px;
  height: 12px;
  background-color: #2a1a4a;
  transform: rotate(45deg);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-dropdown .brands-dropdown::before {
  left: 15px;
}

.brand-dropdown-item {
  padding: 10px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
  font-size: 14px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  text-align: center;
}

.mobile-dropdown .brand-dropdown-item {
  padding: 12px 15px;
  font-size: 14px;
  text-align: left;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdown-arrow {
  font-size: 10px;
  transition: transform 0.3s ease;
  opacity: 0.7;
}

.filter-chip.active .dropdown-arrow {
  transform: rotate(180deg);
  opacity: 1;
}

.brand-dropdown-item:last-child {
  border-bottom: none;
}

.brand-dropdown-item:hover {
  background-color: rgba(142, 45, 226, 0.2);
}

.brand-dropdown-item.active {
  background-color: rgba(142, 45, 226, 0.4);
  font-weight: 500;
}

/* 移动端轮播图样式 */
.mobile-hero-slider {
  width: calc(112% - 20px);
  height: 220px;
  margin: 20px -13px 30px;
  border-radius: 15px;
  background: linear-gradient(135deg, #6b3294 0%, #8540ac 100%);
  position: relative;
  overflow: hidden;
}

.mobile-slider-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 15px;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 100%);
}

.mobile-slider-title {
  font-size: 20px;
  color: white;
  margin-bottom: 5px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.mobile-slider-actions {
  display: flex;
  gap: 30px;
  margin-left: 0;
}

.mobile-action-button {
  background: none;
  border: none;
  color: white;
  padding: 0;
  cursor: pointer;
  line-height: 0;
}

.mobile-action-button svg {
  width: 25px;
  height: 25px;
  stroke-width: 2.5;
}

.mobile-slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  padding: 0;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  z-index: 10;
  cursor: pointer;
  opacity: 0.8;
}

.mobile-slider-arrow.prev {
  left: 5px;
}

.mobile-slider-arrow.next {
  right: 5px;
}

/* 背景遮罩 */
.dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 100;
  animation: fadeIn 0.2s ease;
}

/* QC加载状态样式 */
.qc-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 250px;
  width: 100%;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(142, 45, 226, 0.2);
  border-top: 4px solid #8e2de2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.qc-loading p {
  color: #bbb;
  font-size: 16px;
}

/* QC轮播控制按钮 */
.qc-carousel-controls {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 0;
  display: flex;
  justify-content: space-between;
  padding: 0 15px;
  z-index: 30;
  pointer-events: none;
}

.qc-carousel-btn {
  width: 40px;
  height: 40px;
  background: rgba(90, 51, 160, 0.7);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  top: 150px;
  pointer-events: auto;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.qc-carousel-btn:hover {
  background: rgba(90, 51, 160, 0.9);
  transform: scale(1.05);
}

.qc-carousel-btn.prev {
  left: -20px;
}

.qc-carousel-btn.next {
  right: -20px;
}

/* 图片过渡效果 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 调整QC组容器和定位 */
.freshly-qc-container {
  position: relative;
  padding-top: 30px;
}

.qc-groups-grid {
  position: relative;
  padding: 10px 30px;
}

.qc-group-content {
  position: relative;
  overflow: hidden;
}

@media (max-width: 768px) {
  .qc-carousel-btn {
    width: 36px;
    height: 36px;
    top: 120px;
  }
  
  .qc-carousel-btn.prev {
    left: -15px;
  }
  
  .qc-carousel-btn.next {
    right: -15px;
  }
  
  .qc-groups-grid {
    padding: 10px 20px;
  }
}

/* QC空数据状态 */
.qc-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  width: 100%;
  background-color: rgba(43, 32, 73, 0.3);
  border-radius: 12px;
  margin-top: 20px;
}

.qc-empty p {
  color: #bbb;
  font-size: 16px;
}

/* 过滤下拉框 */
.filter-dropdown {
  position: absolute;
  top: 46px;
  left: 0;
  width: 100%;
  background: rgba(43, 32, 73, 0.98);
  border-radius: 12px;
  z-index: 100;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  padding: 14px;
  animation: fadeIn 0.2s ease;
  backdrop-filter: blur(5px);
}

.filter-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 12px;
  padding: 2px;
}

.filter-option {
  height: 52px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(123, 74, 170, 0.5);
  color: white;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  padding: 0 15px;
  position: relative;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.filter-option:hover {
  background-color: rgba(123, 74, 170, 0.7);
  box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.filter-option:active {
  transform: translateY(0);
}

.filter-option.active {
  background-color: rgba(142, 45, 226, 0.9);
  box-shadow: 0 3px 10px rgba(142, 45, 226, 0.3);
}

.seller-option {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  grid-column: 1;
}

.seller-option span {
  padding-left: 0;
  padding-right: 0;
}

.arrow-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  position: absolute;
  right: 15px;
  top: 0;
  bottom: 0;
}

.arrow-up, .arrow-down {
  cursor: pointer;
  height: 14px;
  color: white;
  opacity: 0.7;
  transition: all 0.2s ease;
  margin: 1px 0;
}

.arrow-up:hover, .arrow-down:hover {
  opacity: 1;
  transform: scale(1.15);
  color: #fff;
}

.active .arrow-up, .active .arrow-down {
  opacity: 0.9;
}

@media (max-width: 480px) {
  .filter-dropdown {
    width: calc(100% - 20px);
    left: 10px;
    top: 42px;
  }
  
  .filter-options {
    grid-gap: 8px;
  }
  
  .filter-option {
    height: 46px;
    font-size: 15px;
    padding: 0 10px;
    border-radius: 8px;
  }
  
  .seller-option {
    grid-column: 1;
    grid-row: 3;
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
  }
  
  .refresh {
    grid-column: 2;
    grid-row: 3;
    box-shadow: 0 2px 8px rgba(103, 58, 183, 0.2);
    background-color: rgba(114, 69, 193, 0.75);
  }
  
  .refresh:hover {
    background-color: rgba(114, 69, 193, 0.85);
  }
  
  .refresh-icon {
    transform: scale(0.9);
  }
  
  .arrow-container {
    right: 12px;
  }
}

/* 过滤下拉框背景遮罩 */
.filter-dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 90;
  animation: fadeIn 0.2s ease;
}

/* 刷新按钮样式 */
.refresh {
  background-color: rgba(103, 58, 183, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.refresh:after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.refresh:focus:not(:active)::after {
  animation: ripple 1s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0) translate(-50%, -50%);
    opacity: 0;
  }
  20% {
    transform: scale(25, 25) translate(-50%, -50%);
    opacity: 0.2;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40) translate(-50%, -50%);
  }
}

.refresh:hover {
  background-color: rgba(103, 58, 183, 0.85);
  box-shadow: 0 2px 12px 0 rgba(103, 58, 183, 0.2);
}

.refresh:active {
  background-color: rgba(103, 58, 183, 0.9);
  transform: scale(0.98);
}

.refresh-icon {
  transition: all 0.3s;
}

.refresh:hover .refresh-icon {
  transform: rotate(30deg);
}

.refresh:active .refresh-icon {
  transform: rotate(60deg);
}

/* PC端样式 - 确保商品卡片尺寸保持不变 */
@media (min-width: 481px) {
  .products-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
    padding: 0 5px;
  }
  
  :deep(.product-card) {
    width: 100%;
    height: auto;
    border-radius: 12px;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  
  :deep(.main-image-container) {
    width: 100%;
    position: relative;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
  }
}

@media (max-width: 480px) {
  /* Trending部分调整 */
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 2px;
    margin: 0 auto;
    padding: 0 2px;
    justify-items: center;
  }
  
  /* 调整移动端商品卡片尺寸 */
  :deep(.product-card) {
    margin-bottom: 0;
    border-radius: 12px;
    width: 100%;
    max-width: 223px;
    height: 280px;
    display: flex;
    flex-direction: column;
  }
}

/* ================ CREATOR CHOICE 区域 ================ */
.creator-choice-container {
  max-width: 1400px;
  margin: 0 auto 80px;
  position: relative;
  padding: 40px 20px;
}

.creator-choice-title {
  font-size: 32px;
  font-weight: 600;
  color: #ffffff;
  text-transform: uppercase;
  text-align: center;
  margin-bottom: 30px;
}

.creator-choice-carousel {
  position: relative;
  padding: 0 40px;
}

.carousel-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(90, 51, 160, 0.7);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
}

.carousel-arrow:hover {
  background: rgba(90, 51, 160, 0.9);
  transform: translateY(-50%) scale(1.05);
}

.carousel-arrow.prev {
  left: 0;
}

.carousel-arrow.next {
  right: 0;
}

.creator-choice-slides {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding: 10px 0;
}

.creator-choice-slides::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.creator-choice-slide {
  flex: 0 0 calc(33.333% - 14px);
  min-width: 300px;
  scroll-snap-align: start;
}

.creator-video {
  background-color: #13101F;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.creator-video:hover {
  transform: translateY(-5px);
}

.video-placeholder {
  height: 529px;
  width: 100%;
  max-width: 374px;
  position: relative;
  overflow: hidden;
  background-color: #000;
  margin: 0 auto;
  cursor: pointer;
  border-radius: 12px 12px 0 0;
}

.video-placeholder img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.creator-video:hover .video-placeholder img {
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  z-index: 4;
}

.play-button:hover {
  background-color: rgba(90, 51, 160, 0.8);
}

.creator-info {
  padding: 15px;
  position: relative;
  padding-top: 10px;
  padding-left: 65px;
}

.creator-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  position: absolute;
  bottom: -20px;
  left: 15px;
  border: 2px solid #13101F;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.creator-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.creator-name {
  color: #b99dff;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
}

.creator-description {
  color: #ffffff;
  font-size: 13px;
  line-height: 1.4;
  opacity: 0.9;
  margin-bottom: 10px;
}

@media (max-width: 992px) {
  .creator-choice-slide {
    flex: 0 0 calc(50% - 10px);
    min-width: 250px;
  }
  
  .video-placeholder {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .creator-choice-title {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  .creator-choice-carousel {
    padding: 0 30px;
  }
  
  .carousel-arrow {
    width: 36px;
    height: 36px;
  }
}

@media (max-width: 480px) {
  .creator-choice-container {
    padding: 30px 10px;
    margin-bottom: 60px;
  }
  
  .creator-choice-slide {
    flex: 0 0 calc(100% - 40px);
    min-width: 220px;
  }
  
  .video-placeholder {
    height: 320px;
  }
  
  .creator-choice-carousel {
    padding: 0 20px;
  }
  
  .carousel-arrow {
    width: 32px;
    height: 32px;
  }
  
  .play-button {
    width: 50px;
    height: 50px;
  }
  
  .play-button svg {
    width: 40px;
    height: 40px;
  }
}

.creator-video-element {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
  display: none;
  z-index: 5;
  background-color: #000;
  cursor: pointer;
}

@media (max-width: 480px) {
  .video-placeholder {
    height: 420px;
  }
}

.video-container {
  position: relative;
}

/* 添加全屏分类菜单 */
.category-fullscreen {
  position: fixed;
  top: auto;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #462b66;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  height: 50vh;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  box-shadow: 0 -5px 25px rgba(0, 0, 0, 0.3);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.category-fullscreen.show {
  transform: translateY(0);
}

@media (min-width: 481px) {
  .category-fullscreen {
    display: none;
  }
}

.category-fullscreen-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background-color: #462b66;
  position: relative;
}

.category-fullscreen-header h2 {
  font-size: 20px;
  color: #ffffff;
  font-weight: 500;
  margin: 0;
  text-align: center;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  padding: 5px;
  cursor: pointer;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
}

.category-fullscreen-content {
  display: flex;
  height: calc(100% - 50px);
  padding: 0;
  max-width: 100%;
  overflow: hidden;
}

.category-sidebar {
  width: 35%;
  background-color: #3b2556;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0;
  margin: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.category-content {
  width: 65%;
  padding: 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 5px;
}

.category-sidebar-item {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 15px;
  transition: background-color 0.3s ease;
  cursor: pointer;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
}

.category-sidebar-item.active {
  background-color: #462b66;
}

.category-sidebar-item span {
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
}

.check-icon {
  position: absolute;
  left: 15px;
  color: #ffffff;
  font-size: 16px;
  font-weight: bold;
}

.category-sidebar-item.active span {
  margin-left: 20px;
}

.category-card {
  width: 100%;
  aspect-ratio: 1;
  background-color: #5d3c85;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.category-card:hover {
  transform: scale(1.05);
}

.category-image {
  width: 60%;
  height: 60%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  opacity: 0.7;
}

.category-name {
  padding: 10px 5px;
  color: #ffffff;
  font-size: 14px;
  text-align: center;
  font-weight: 500;
}

.top-buttons {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 5;
}

.category-button {
  background-color: #3b2556;
  border: 1px solid #5a33a0;
  border-radius: 4px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
}

.category-button:hover {
  background-color: #5a33a0;
}

.category-button svg {
  color: white;
  width: 20px;
  height: 20px;
}

@media (max-width: 480px) {
  .top-buttons {
    top: 5px;
    left: 5px;
  }
}

/* 移动端分类和过滤按钮 */
.mobile-filter-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  padding: 0 5px;
  position: relative;
}

.category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(30, 30, 45, 0.8);
  border: 1px solid rgba(90, 51, 160, 0.5);
  border-radius: 30px;
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  margin-left: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.category-btn:hover {
  background-color: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

.category-btn:active {
  transform: scale(0.98);
}

.category-btn svg {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  display: none; /* 隐藏SVG图标，与截图一致 */
}

.mobile-filter-category .filter-icon {
  width: 48px;
  height: 48px;
  min-width: 48px;
  background-color: #8e2de2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-filter-category .filter-icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

/* 移动端过滤下拉框 */
.mobile-filter-dropdown {
  position: absolute;
  top: 45px;
  left: -10px;
  width: calc(100% + 10px);
  background: #2b1a45;
  border-radius: 12px;
  z-index: 100;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  padding: 10px;
  animation: fadeIn 0.2s ease;
}

.mobile-filter-dropdown .filter-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 10px;
  padding: 2px;
}

.mobile-filter-dropdown .filter-option {
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #462b66;
  color: white;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  padding: 0 15px;
  position: relative;
  letter-spacing: 0.5px;
}

.mobile-filter-dropdown .filter-option.active {
  background-color: #8e2de2;
}

.mobile-filter-dropdown .seller-option {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  grid-column: 1;
}

.mobile-filter-dropdown .refresh {
  grid-column: 2;
  background-color: #462b66;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 