<template>
  <div class="sellers-page" :class="{ 'dark-theme': isDarkMode }" :data-theme="isDarkMode ? 'dark' : 'light'">
    <!-- Header -->
    <div class="sellers-header">
      <h1 class="page-title">SELLERS</h1>
      <!-- Filter Tabs -->
      <div class="filter-tabs">
        <button class="filter-tab" :class="{ active: activeFilter === 'promoted' }" @click="setFilter('promoted')">Promoted Sellers</button>
        <button class="filter-tab" :class="{ active: activeFilter === 'trusted' }" @click="setFilter('trusted')">Trusted Sellers</button>
      </div>
    </div>

    <!-- Sellers Grid -->
    <div class="sellers-grid">
      <div 
        class="seller-card fade-in" 
        v-for="seller in filteredSellers" 
        :key="seller.id"
        :class="{'promoted': seller.type === 'promoted', 'trusted': seller.type === 'trusted'}"
      >
        <div class="card-glow"></div>
        <div class="seller-content">
          <div class="seller-logo-container">
          <div class="seller-logo">
              <img v-if="seller.logoUrl" :src="seller.logoUrl" :alt="seller.name || 'Seller'">
              <div v-else class="initials-placeholder">{{ getInitials(seller.name) }}</div>
          </div>
            <div v-if="seller.type === 'promoted'" class="seller-badge promoted">PROMOTED</div>
            <div v-else-if="seller.type === 'trusted'" class="seller-badge trusted">TRUSTED</div>
          </div>
          <div class="seller-info">
            <h3 class="seller-name">{{ seller.name || 'Unnamed Seller' }}</h3>
            <el-tooltip
              class="description-tooltip"
              effect="dark"
              :content="seller.description || 'No description available'"
              placement="top"
              :enterable="false"
              :show-after="200"
            >
            <p class="seller-desc">{{ seller.description || 'No description available' }}</p>
              </el-tooltip>
            <div class="seller-stats">
              <!-- <div class="stat-item">
                <span class="stat-icon products-icon"></span>
                <span class="stat-value">350+ Products</span>
            </div>
              <div class="stat-item">
                <span class="stat-icon followers-icon"></span>
                <span class="stat-value">12K Followers</span>
              </div> -->
            </div>
          </div>
          
          <!-- Moved the rating container here, above the buttons -->
          <div class="rating-container">
            <div class="rating-item" @click="toggleLike(seller.id)">
              <div class="rating-icon like-icon" :class="{'active': seller.liked}"></div>
              <span class="rating-value">{{ seller.likes }}</span>
            </div>
            <div class="rating-item" @click="toggleBookmark(seller.id)">
              <div class="rating-icon bookmark-icon" :class="{'active': seller.bookmarked}"></div>
              <span class="rating-value">{{ seller.collect || '0' }}</span>
            </div>
          </div>
          
          <div class="seller-buttons">
            <button class="marketplace-btn" @click="goToSellerPage(seller.platformUrl || '')">
              <span class="btn-text">MARKETPLACE</span>
              <span class="btn-icon">→</span>
            </button>
            <button 
              class="learn-more-btn"
              :class="{ 'collected': userInteractions[seller.id] && userInteractions[seller.id].bookmarked }"
              @click="goToSellerPage(seller.websiteUrl || '')"
            >
              <span class="btn-text">LEARN MORE</span>
              <span class="btn-icon">+</span>
            </button>
          </div>
          <pre>{{ seller }}</pre>
        </div>
      </div>
    </div>

    <!-- Loading Indicator -->
    <div class="loading-state">
    <div v-if="isLoading" class="loading-spinner"></div>
      <div v-if="isLoading" class="loading-text">Loading sellers...</div>
      <div v-if="loadComplete && filteredSellers.length > 0" class="status-message load-complete-message">
        All sellers loaded
      </div>
      <div v-if="filteredSellers.length === 0 && !isLoading" class="status-message no-results-message">
        No sellers found for this category
      </div>
    </div>

    <!-- Sentinel Element for Intersection Observer -->
    <div ref="loadMoreTrigger" class="load-more-trigger"></div>

    <!-- Footer Disclaimer Section -->
    <!-- <div class="footer-disclaimer">
      <div class="disclaimer-container">
        <div class="disclaimer-logo">
          <img src="../assets/logo.png" alt="AGTFIND Logo">
    </div>
        <div class="disclaimer-content">
          <h3>Disclaimer: All sellers and their links displayed on AGTFIND are user-submitted content</h3>
          <p>All content on this website should only be used for educational purposes. We provide no guarantee for the quality of the items, and we are not a substitution for the sale of the item, only for batch identification as a trusted source.</p>
          <p>Our site will not be liable if you are not satisfied with the items received and is in no way associated with the Chinese website or brand.</p>
          <p>Please be aware that this website contains affiliate links. This doesn't cost you extra if you make a purchase through these links, but may earn a small commission.</p>
          <p>This commission helps us continue to provide you with the opportunity to get support.</p>
        </div>
      </div>
      
      <div class="footer-links">
        <a href="#">Cookie</a>
        <a href="#">Data Security</a>
        <a href="#">Privacy Policy</a>
        <a href="#">Disclaimer</a>
        <a href="mailto:<EMAIL>"><EMAIL></a>
      </div>
      
      <div class="copyright">
        Copyright {{ new Date().getFullYear() }} AGTFIND.com - All Rights Reserved Manufactured by Antagonist Enterprise
      </div> -->
    <!-- </div> -->
  </div>
</template>

<script>
import sellerApi from '@/api/seller';
// Import Element UI components
import { ElTooltip, ElMessage } from 'element-plus';
import 'element-plus/dist/index.css';
// import { addFavoriteSeller, removeFavoriteSeller, checkFavoriteSeller } from '@/api/favoriteSellers';
import { tokenUtil } from '@/services/api';
import { addSellerCollect, checkSellerCollectStatus, cancelSellerCollect } from '@/api/sellerCollect';
import { checkSellerLikeStatus, addSellerLike, cancelSellerLike } from '@/api/sellerLike';
import { emitter } from '@/utils/eventBus';

export default {
  name: 'SellersView',
  components: {
    ElTooltip
  },
  data() {
    // 从localStorage获取主题设置，如果没有则设为深色主题
    const savedDarkMode = localStorage.getItem('darkMode');
    const initialDarkMode = savedDarkMode !== null ? savedDarkMode === 'true' : true;

    return {
      sellers: {
        promoted: [],
        trusted: []
      },         // Store all seller data by type
      visibleSellers: {
        promoted: [],
        trusted: []
      },  // Store currently displayed sellers by type
      pageIndices: {
        promoted: 0,
        trusted: 0
      },     // Current loading index by type
      batchSize: 8,      // Number of items to load each time (2 rows of 4 cards on desktop)
      initialLoad: 12,   // Initial load (3 rows of 4 cards on desktop)
      activeFilter: 'promoted', // Default to promoted tab
      isLoading: false,
      loadComplete: false,
      hasError: false,
      errorMessage: '',
      defaultLogoUrl: require("../assets/logo.png"),
      userInteractions: {}, // Store user interactions (likes, bookmarks) by seller ID
      isDarkMode: initialDarkMode // 主题状态
    };
  },
  computed: {
    filteredSellers() {
      const sellers = this.visibleSellers[this.activeFilter] || [];
      // 调试：打印每个seller的likes和完整对象
      return sellers.map(seller => {
        const sellerId = String(seller.id);
        const userInteraction = this.userInteractions[sellerId] || {};
        // console.log('filteredSellers seller.likes:', seller.likes, seller);
        return {
          ...seller,
          id: sellerId,
          liked: userInteraction.liked || false,
          bookmarked: userInteraction.bookmarked || false
        };
      });
    },
    // Check if we have reached the end of the current filter's data
    isCurrentFilterComplete() {
      return this.pageIndices[this.activeFilter] >= (this.sellers[this.activeFilter]?.length || 0);
    },
  },
  created() {
    this.fetchInitialData();

    // 监听主题变化事件
    emitter.on('theme-changed', this.handleThemeChange);
    emitter.on('apply-theme-to-page', this.handleThemeChange);
  },
  mounted() {
    this.createObserver();
    this.checkFavoriteStatus();

    // 初始化主题
    this.applyTheme();
  },
  beforeUnmount() {
    // 清理事件监听器
    emitter.off('theme-changed', this.handleThemeChange);
    emitter.off('apply-theme-to-page', this.handleThemeChange);
  },
  methods: {
    // 获取名称缩写
    getInitials(name) {
      if (!name) return 'AG';
      return name.substring(0, 2).toUpperCase();
    },
    
    // 点赞功能
    toggleLike(sellerId) {
      sellerId = String(sellerId);
      const seller = (this.visibleSellers.promoted.concat(this.visibleSellers.trusted)).find(s => String(s.id) === sellerId);
      if (!seller) return;
      if (!this.userInteractions[sellerId]) {
        this.userInteractions[sellerId] = {};
      }
      // 动态获取 userId
      let userId = null;
      try {
        const userInfo = JSON.parse(localStorage.getItem('userInfo'));
        userId = userInfo?.userId || userInfo?.id || null;
      } catch (e) {
        userId = null;
      }
      if (!userId) {
        ElMessage.error('请先登录');
        return;
      }
      // 获取当前点赞状态
      const isCurrentlyLiked = this.userInteractions[sellerId].liked || false;
      // 先更新UI状态（无感知更新）
      this.userInteractions[sellerId].liked = !isCurrentlyLiked;
      if (isCurrentlyLiked) {
        cancelSellerLike(userId, sellerId).then(() => {
          ElMessage.success('取消点赞成功');
          this.fetchSellersByType(this.activeFilter); // 重新拉取数据
        }).catch(() => {
          this.userInteractions[sellerId].liked = true;
          ElMessage.error('Failed to unlike');
        });
        } else {
        addSellerLike(userId, sellerId).then(() => {
          ElMessage.success('点赞成功');
          this.fetchSellersByType(this.activeFilter); // 重新拉取数据
        }).catch(() => {
          this.userInteractions[sellerId].liked = false;
          ElMessage.error('Failed to like');
        });
      }
    },
    
    // 收藏功能
    toggleBookmark(sellerId) {
      sellerId = String(sellerId);
      if (!tokenUtil.isLoggedIn()) {
        ElMessage({
          message: 'Please login to add sellers to favorites',
          type: 'warning'
        });
        return;
      }
      // 动态获取 userId
      let userId = null;
      try {
        const userInfo = JSON.parse(localStorage.getItem('userInfo'));
        userId = userInfo?.userId || userInfo?.id || null;
      } catch (e) {
        userId = null;
      }
      if (!userId) {
        ElMessage.error('请先登录');
        return;
      }
      // 找到当前seller对象
      const seller = (this.visibleSellers.promoted.concat(this.visibleSellers.trusted)).find(s => String(s.id) === sellerId);
      if (!seller) return;
      if (!this.userInteractions[sellerId]) {
        this.userInteractions[sellerId] = {};
      }
      // 获取当前收藏状态
      const isCurrentlyBookmarked = this.userInteractions[sellerId].bookmarked || false;
      // 先更新UI状态（无感知更新）
      this.userInteractions[sellerId].bookmarked = !isCurrentlyBookmarked;
      // 更新收藏数
      if (isCurrentlyBookmarked) {
        if (typeof seller.collect === 'number' && seller.collect > 0) {
          seller.collect--;
        } else if (typeof seller.collect === 'string') {
          const count = parseInt(seller.collect);
          if (!isNaN(count) && count > 0) {
            seller.collect = (count - 1).toString();
          }
        }
      } else {
        if (typeof seller.collect === 'number') {
          seller.collect++;
        } else if (typeof seller.collect === 'string') {
          const count = parseInt(seller.collect);
          if (!isNaN(count)) {
            seller.collect = (count + 1).toString();
          } else {
            seller.collect = '1';
          }
        } else {
          seller.collect = '1';
        }
      }
      // 发送API请求
      if (isCurrentlyBookmarked) {
        cancelSellerCollect(userId, sellerId).then(() => {
          ElMessage({
            message: 'Removed from favorite sellers',
            type: 'success'
          });
        }).catch(() => {
          this.userInteractions[sellerId].bookmarked = true;
          if (typeof seller.collect === 'number' && seller.collect > 0) {
            seller.collect++;
          } else if (typeof seller.collect === 'string') {
            const count = parseInt(seller.collect);
            if (!isNaN(count)) {
              seller.collect = (count + 1).toString();
            }
          }
          ElMessage.error('Failed to remove from favorites');
        });
      } else {
        addSellerCollect(userId, sellerId).then(() => {
          ElMessage({
            message: 'Added to favorite sellers',
            type: 'success'
          });
        }).catch(() => {
          this.userInteractions[sellerId].bookmarked = false;
          if (typeof seller.collect === 'number' && seller.collect > 0) {
            seller.collect--;
          } else if (typeof seller.collect === 'string') {
            const count = parseInt(seller.collect);
            if (!isNaN(count) && count > 0) {
              seller.collect = (count - 1).toString();
            }
          }
          ElMessage.error('Failed to add to favorites');
        });
      }
    },
    
    async fetchInitialData() {
      try {
        this.hasError = false;
        this.errorMessage = '';
        await Promise.all([
          this.fetchSellersByType('promoted'),
          this.fetchSellersByType('trusted')
        ]);
        // 数据加载后再检查收藏和点赞状态
        await this.checkAllSellerCollectStatus();
        await this.checkAllSellerLikeStatus();
          } catch (error) {
        this.hasError = true;
        this.errorMessage = 'Failed to load initial data. Please try again later.';
        console.error('Error fetching initial data:', error);
      }
    },
    async fetchSellersByType(type) {
      this.isLoading = true;
      try {
        const response = await sellerApi.getSellersByType(type);
        const data = response.data || [];
        // Ensure each seller has required fields，并做字段映射，id始终为字符串
        this.sellers[type] = data.map(seller => ({
          ...seller,
          id: String(seller.sellerId || seller.id || Math.random().toString(36).substr(2, 9)),
          name: seller.storeName || seller.name || 'Unnamed Seller',
          description: seller.description || 'No description available',
          logoUrl: seller.logoUrl || seller.logo || this.defaultLogoUrl,
          type: seller.type || type,
          likes: seller.likes != null ? seller.likes : (seller.like != null ? seller.like : '0'),
          collect: seller.collect != null ? seller.collect : '0'
        }));
        // console.log('接口原始数据:', data);
        // Initially load the first 3 rows (12 items) or fewer if there aren't enough
        const initialCount = Math.min(this.initialLoad, this.sellers[type].length);
        this.visibleSellers[type] = this.sellers[type].slice(0, initialCount);
        this.pageIndices[type] = initialCount;
        // Update loadComplete state if this is the active filter
        if (type === this.activeFilter) {
          this.loadComplete = this.sellers[type].length <= initialCount;
        }
      } catch (error) {
        console.error(`Error fetching ${type} sellers:`, error);
        this.sellers[type] = [];
        this.visibleSellers[type] = [];
        if (type === this.activeFilter) {
          this.loadComplete = true;
        }
      } finally {
        this.isLoading = false;
      }
    },
    loadMore() {
      if (this.isLoading || this.loadComplete) return;
      
      // Get the next batch from the loaded data for the current filter type
      const currentType = this.activeFilter;
      const currentIndex = this.pageIndices[currentType];
      const currentSellers = this.sellers[currentType];
      
      if (!currentSellers || currentIndex >= currentSellers.length) {
        this.loadComplete = true;
        return;
      }
      
      this.isLoading = true;
      
      // Simulate network delay for smoother UX
      setTimeout(() => {
        const nextItems = currentSellers.slice(
          currentIndex, 
          currentIndex + this.batchSize
      );
      
      if (nextItems.length > 0) {
        // Add new data to the visible list
          this.visibleSellers[currentType] = [...this.visibleSellers[currentType], ...nextItems];
          this.pageIndices[currentType] += this.batchSize;
        
          // Check if all data has been loaded for current filter
          if (this.pageIndices[currentType] >= currentSellers.length) {
          this.loadComplete = true;
        }
      } else {
        this.loadComplete = true;
      }
        
        this.isLoading = false;
      }, 500);
    },
    async setFilter(filter) {
      if (this.activeFilter === filter) return;
      
      this.activeFilter = filter;
      this.loadComplete = this.isCurrentFilterComplete;
      
      // If we haven't loaded this filter type before, fetch it now
      if (!this.sellers[filter] || this.sellers[filter].length === 0) {
        await this.fetchSellersByType(filter);
      }
    },
    createObserver() {
      const options = {
        root: null,
        rootMargin: '200px', // Load more when user is 200px from the bottom
        threshold: 0.1
      };
      const observer = new IntersectionObserver(this.handleIntersect, options);
      this.$nextTick(() => {
        if (this.$refs.loadMoreTrigger) {
      observer.observe(this.$refs.loadMoreTrigger);
        }
      });
    },
    handleIntersect(entries) {
      if (entries[0].isIntersecting && !this.isLoading && !this.loadComplete) {
        this.loadMore();
      }
    },
    goToSellerPage(url) {
      if (!url) return;
      
      // Check if URL contains protocol
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url;
      }
      
      // Open external link in new tab
      window.open(url, '_blank');
    },
    async searchSellers(query) {
      if (!query.trim()) {
        // If search is cleared, reset to normal view
        await this.fetchInitialData();
        return;
      }
      this.isLoading = true;
      this.hasError = false;
      this.errorMessage = '';
      try {
        const response = await sellerApi.searchSellers(query);
        const results = response.data || [];
        // Ensure each result has required fields和字段映射
        const processedResults = results.map(seller => ({
          ...seller,
          id: seller.sellerId || seller.id || Math.random().toString(36).substr(2, 9),
          name: seller.storeName || seller.name || 'Unnamed Seller',
          description: seller.description || 'No description available',
          logoUrl: seller.logoUrl || seller.logo || this.defaultLogoUrl,
          type: seller.type || 'trusted',
          likes: seller.likes != null ? seller.likes : (seller.like != null ? seller.like : '0'),
          collect: seller.collect != null ? seller.collect : '0'
        }));
        // Categorize results by type
        const categorized = {
          promoted: processedResults.filter(s => s.type === 'promoted'),
          trusted: processedResults.filter(s => s.type === 'trusted')
        };
        // Update data stores
        this.sellers = categorized;
        // Update visible sellers for each type
        for (const type of ['promoted', 'trusted']) {
          const initialCount = Math.min(this.initialLoad, categorized[type].length);
          this.visibleSellers[type] = categorized[type].slice(0, initialCount);
          this.pageIndices[type] = initialCount;
        }
        // Update loadComplete for active filter
        this.loadComplete = this.isCurrentFilterComplete;
        // 搜索后也检查一次收藏状态
        this.checkAllSellerCollectStatus();
      } catch (error) {
        this.hasError = true;
        this.errorMessage = 'Failed to search sellers. Please try again later.';
        console.error('Error searching sellers:', error);
      } finally {
        this.isLoading = false;
      }
    },
    // 检查收藏状态
    async checkFavoriteStatus() {
      // 只有登录用户才检查收藏状态
      if (!tokenUtil.isLoggedIn()) return;
      // 此处原本有 checkFavoriteSeller 相关代码，已注释/移除
    },
    async checkAllSellerCollectStatus() {
      // 动态获取 userId
      let userId = null;
      try {
        const userInfo = JSON.parse(localStorage.getItem('userInfo'));
        userId = userInfo?.userId || userInfo?.id || null;
      } catch (e) {
        userId = null;
      }
      if (!userId) return;
      // 获取所有sellerId（初始数据加载后）
      this.$nextTick(async () => {
        const allSellers = [
          ...(this.sellers.promoted || []),
          ...(this.sellers.trusted || [])
        ];
        const sellerIds = allSellers.map(s => s.id);
        if (sellerIds.length === 0) return;
        try {
          const res = await checkSellerCollectStatus(userId, sellerIds);
          // res.data 是一个与 sellerIds 顺序对应的布尔数组
          const statusArr = Array.isArray(res.data) ? res.data : [];
          for (let i = 0; i < sellerIds.length; i++) {
            const id = sellerIds[i];
            if (!this.userInteractions[id]) this.userInteractions[id] = {};
            this.userInteractions[id].bookmarked = !!statusArr[i];
          }
        } catch (e) {
          // 失败时全部设为未收藏
          for (const id of sellerIds) {
            if (!this.userInteractions[id]) this.userInteractions[id] = {};
            this.userInteractions[id].bookmarked = false;
          }
        }
      });
    },
    async checkAllSellerLikeStatus() {
      // 动态获取 userId
      let userId = null;
      try {
        const userInfo = JSON.parse(localStorage.getItem('userInfo'));
        userId = userInfo?.userId || userInfo?.id || null;
      } catch (e) {
        userId = null;
      }
      if (!userId) return;
      this.$nextTick(async () => {
        const allSellers = [
          ...(this.sellers.promoted || []),
          ...(this.sellers.trusted || [])
        ];
        const sellerIds = allSellers.map(s => s.id);
        if (sellerIds.length === 0) return;
        try {
          const res = await checkSellerLikeStatus(userId, sellerIds);
          const statusArr = Array.isArray(res.data) ? res.data : [];
          for (let i = 0; i < sellerIds.length; i++) {
            const id = sellerIds[i];
            if (!this.userInteractions[id]) this.userInteractions[id] = {};
            this.userInteractions[id].liked = !!statusArr[i];
        }
      } catch (e) {
          for (const id of sellerIds) {
            if (!this.userInteractions[id]) this.userInteractions[id] = {};
            this.userInteractions[id].liked = false;
          }
        }
      });
    },

    // 主题相关方法
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode !== 'undefined') {
        this.isDarkMode = data.isDarkMode;
        this.applyTheme();
      }
    },

    applyTheme() {
      // 应用主题到页面根元素
      const pageElement = this.$el;
      if (pageElement) {
        if (this.isDarkMode) {
          pageElement.style.background = 'linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%)';
          pageElement.style.color = '#ffffff';
        } else {
          pageElement.style.background = 'linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%)';
          pageElement.style.color = '#333333';
        }
      }
    }
  },
};
</script>

<style scoped>
.sellers-page {
  padding: 2rem 5rem;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
  transition: all 0.3s ease;
}

/* 深色主题样式 */
.sellers-page[data-theme="dark"],
.sellers-page.dark-theme {
  color: #e0e0e0;
  background: #1e0940;
  background-image: linear-gradient(125deg, #1e0940 0%, #380d6d 50%, #1e0940 100%);
}

/* 浅色主题样式 */
.sellers-page[data-theme="light"] {
  color: #333333;
  background: #f0d5fc;
  background-image: linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%);
}

/* Header styles */
.sellers-header {
  margin-bottom: 3rem;
  position: relative;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 2rem;
  letter-spacing: 2px;
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

/* 深色主题标题样式 */
.sellers-page[data-theme="dark"] .page-title,
.sellers-page.dark-theme .page-title {
  color: #ffffff;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.7), 0 0 30px rgba(128, 102, 204, 0.5);
}

/* 浅色主题标题样式 */
.sellers-page[data-theme="light"] .page-title {
  color: #230F48;
  text-shadow: 0 0 15px rgba(35, 15, 72, 0.3), 0 0 30px rgba(147, 51, 234, 0.2);
}

.page-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, rgba(128, 102, 204, 0), rgba(128, 102, 204, 1), rgba(128, 102, 204, 0));
  border-radius: 3px;
}

/* Filter tabs with improved style */
.filter-tabs {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.filter-tab {
  border-radius: 30px;
  padding: 0.8rem 2rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  letter-spacing: 1px;
}

/* 深色主题筛选标签样式 */
.sellers-page[data-theme="dark"] .filter-tab,
.sellers-page.dark-theme .filter-tab {
  background: rgba(20, 20, 30, 0.5);
  border: 2px solid #8066cc;
  color: #ffffff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
}

/* 浅色主题筛选标签样式 */
.sellers-page[data-theme="light"] .filter-tab {
  background: rgba(255, 255, 255, 0.7);
  border: 2px solid #9333ea;
  color: #333333;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.filter-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(128, 102, 204, 0.1), rgba(128, 102, 204, 0.05));
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 深色主题筛选标签悬停效果 */
.sellers-page[data-theme="dark"] .filter-tab:hover,
.sellers-page.dark-theme .filter-tab:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(128, 102, 204, 0.3), 0 0 15px rgba(128, 102, 204, 0.2);
  border-color: #a080e0;
}

/* 浅色主题筛选标签悬停效果 */
.sellers-page[data-theme="light"] .filter-tab:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(147, 51, 234, 0.2), 0 0 15px rgba(147, 51, 234, 0.1);
  border-color: #a855f7;
}

.filter-tab:hover::before {
  opacity: 1;
}

/* 深色主题激活状态 */
.sellers-page[data-theme="dark"] .filter-tab.active,
.sellers-page.dark-theme .filter-tab.active {
  background: linear-gradient(135deg, #8066cc, #6a56b8);
  color: #ffffff;
  border-color: #a080e0;
  font-weight: 700;
  box-shadow: 0 8px 25px rgba(128, 102, 204, 0.5), 0 0 20px rgba(128, 102, 204, 0.3);
  transform: translateY(-3px) scale(1.05);
}

/* 浅色主题激活状态 */
.sellers-page[data-theme="light"] .filter-tab.active {
  background: linear-gradient(135deg, #9333ea, #7c3aed);
  color: #ffffff;
  border-color: #a855f7;
  font-weight: 700;
  box-shadow: 0 8px 25px rgba(147, 51, 234, 0.4), 0 0 20px rgba(147, 51, 234, 0.2);
  transform: translateY(-3px) scale(1.05);
}

/* Sellers grid with improved layout */
.sellers-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  perspective: 1000px;
}

/* Modern card design with glass effect */
.seller-card {
  backdrop-filter: blur(10px);
  border-radius: 20px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
  height: 100%;
  z-index: 1;
  border: 2px solid transparent;
  min-height: 370px;
  max-height: 390px;
  height: auto;
  cursor: pointer;
}

/* 深色主题卡片样式 */
.sellers-page[data-theme="dark"] .seller-card,
.sellers-page.dark-theme .seller-card {
  background: rgba(25, 25, 35, 0.7);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(0, 0, 0, 0.3);
}

/* 浅色主题卡片样式 */
.sellers-page[data-theme="light"] .seller-card {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 0 20px rgba(147, 51, 234, 0.1);
}

/* 深色主题卡片边框 */
.sellers-page[data-theme="dark"] .seller-card.promoted,
.sellers-page[data-theme="dark"] .seller-card.trusted,
.sellers-page.dark-theme .seller-card.promoted,
.sellers-page.dark-theme .seller-card.trusted {
  border: 2px solid #8066cc;
  transition: border-color 0.3s ease;
}

/* 浅色主题卡片边框 */
.sellers-page[data-theme="light"] .seller-card.promoted,
.sellers-page[data-theme="light"] .seller-card.trusted {
  border: 2px solid #9333ea;
  transition: border-color 0.3s ease;
}

/* Glowing effect for cards */
.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  z-index: -1;
  transition: all 0.5s ease;
  opacity: 0.5; /* Make glow visible by default */
  box-shadow: none;
}

.seller-card.promoted .card-glow, .seller-card.trusted .card-glow {
  box-shadow: 0 0 30px rgba(128, 102, 204, 0.4);
  border: 1px solid rgba(128, 102, 204, 0.7);
}

/* 深色主题卡片悬停效果 */
.sellers-page[data-theme="dark"] .seller-card:hover,
.sellers-page.dark-theme .seller-card:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6), 0 0 30px rgba(0, 0, 0, 0.4);
  z-index: 2;
  border-color: #ffffff;
}

/* 浅色主题卡片悬停效果 */
.sellers-page[data-theme="light"] .seller-card:hover {
  transform: translateY(-10px) rotateX(5deg);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15), 0 0 30px rgba(147, 51, 234, 0.2);
  z-index: 2;
  border-color: #7c3aed;
}

.seller-card:hover .card-glow {
  opacity: 1;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
}

.seller-card.promoted:hover .card-glow, .seller-card.trusted:hover .card-glow {
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.6), 0 0 50px rgba(255, 255, 255, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.seller-content {
  display: flex;
  flex-direction: column;
  padding: 0.8rem 0.8rem 0.6rem; /* Reduced padding from 1rem 1rem 0.8rem */
  flex: 1;
  position: relative;
  z-index: 1;
}

/* Improved logo container with badge */
.seller-logo-container {
  position: relative;
  margin-bottom: 0.4rem; /* Reduced from 0.6rem */
}

.seller-logo {
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  margin: 0 auto;
  position: relative;
  z-index: 1;
  border: 4px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

/* Colored border for logos */
.seller-card.promoted .seller-logo, .seller-card.trusted .seller-logo {
  border: 4px solid rgba(128, 102, 204, 0.6);
}

.seller-card:hover .seller-logo {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 15px rgba(255, 255, 255, 0.2);
}

.seller-card.promoted:hover .seller-logo, .seller-card.trusted:hover .seller-logo {
  border: 4px solid rgba(128, 102, 204, 0.9);
}

.seller-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.seller-card:hover .seller-logo img {
  transform: scale(1.1);
}

.initials-placeholder {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a1a25;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Stylish badge design */
.seller-badge {
  position: absolute;
  top: 0;
  right: 30%;
  z-index: 2;
  padding: 0.4rem 1rem;
  font-size: 0.7rem;
  font-weight: 800;
  letter-spacing: 1px;
  text-transform: uppercase;
  border-radius: 0 0 15px 15px;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.seller-badge.promoted, .seller-badge.trusted {
  background: linear-gradient(135deg, #8066cc, #5a46a8);
  color: white;
}

.seller-card:hover .seller-badge {
  padding: 0.5rem 1.2rem;
  transform: translateY(2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

/* Seller info section */
.seller-info {
  text-align: center;
  margin-bottom: 0.3rem; /* Reduced from 0.5rem */
}

.seller-name {
  font-size: 1.3rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  display: inline-block;
}

/* 深色主题卖家名称样式 */
.sellers-page[data-theme="dark"] .seller-name,
.sellers-page.dark-theme .seller-name {
  color: #ffffff;
}

.sellers-page[data-theme="dark"] .seller-card.promoted .seller-name,
.sellers-page[data-theme="dark"] .seller-card.trusted .seller-name,
.sellers-page.dark-theme .seller-card.promoted .seller-name,
.sellers-page.dark-theme .seller-card.trusted .seller-name {
  background-image: linear-gradient(135deg, #ffffff, #d9c7ff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 浅色主题卖家名称样式 */
.sellers-page[data-theme="light"] .seller-name {
  color: #230F48;
}

.sellers-page[data-theme="light"] .seller-card.promoted .seller-name,
.sellers-page[data-theme="light"] .seller-card.trusted .seller-name {
  background-image: linear-gradient(135deg, #230F48, #7c3aed);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 5px rgba(35, 15, 72, 0.1);
}

.seller-card:hover .seller-name {
  transform: scale(1.05);
}

/* Rating container and items - updated positioning and size */
.rating-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0; /* Remove all margin */
  gap: 1.5rem; /* Reduced from 2rem */
}

/* 重新设计的评分项样式，添加了光晕效果 */
.rating-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  padding: 6px 12px; /* Reduced from 8px 15px */
  border-radius: 20px;
  background-color: rgba(30, 30, 40, 0.4);
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
}

/* 点击涟漪效果 */
.rating-item::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.7);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.rating-item:active::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0) translate(-50%, -50%);
    opacity: 0.5;
  }
  100% {
    transform: scale(20) translate(-50%, -50%);
    opacity: 0;
  }
}

/* 悬停时的闪光边缘效果 */
.rating-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(128, 102, 204, 0.3), transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 0;
}

.rating-item:hover::before {
  opacity: 1;
  animation: pulseGlow 2s infinite alternate;
}

/* 激活状态的持续动画效果 */
.rating-icon.active {
  position: relative;
  z-index: 2;
}

.like-icon.active::before,
.bookmark-icon.active::before {
  content: '';
  position: absolute;
  top: -8px; /* Reduced from -10px */
  left: -8px; /* Reduced from -10px */
  right: -8px; /* Reduced from -10px */
  bottom: -8px; /* Reduced from -10px */
  background: radial-gradient(circle at center, rgba(255, 255, 255, 0.7) 0%, transparent 70%);
  border-radius: 50%;
  z-index: -1;
  opacity: 0;
  animation: pulsating 2s infinite;
}

.like-icon.active::before {
  background: radial-gradient(circle at center, rgba(255, 64, 129, 0.3) 0%, transparent 70%);
}

.bookmark-icon.active::before {
  background: radial-gradient(circle at center, rgba(255, 235, 59, 0.3) 0%, transparent 70%);
}

@keyframes pulsating {
  0% { transform: scale(0.8); opacity: 0; }
  50% { opacity: 0.3; }
  100% { transform: scale(1.2); opacity: 0; }
}

/* 增强图标悬停效果，添加3D变换 */
.rating-item:hover .rating-icon {
  opacity: 1;
  transform: scale(1.15) translateY(-2px) rotateY(10deg);
  filter: drop-shadow(0 3px 6px rgba(128, 102, 204, 0.7));
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.5);
}

/* 点赞图标增强动画效果 */
.like-icon.active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff4081'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
  animation: heartBeat 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  filter: drop-shadow(0 0 8px rgba(255, 64, 129, 0.7));
}

/* 改进心跳动画 */
@keyframes heartBeat {
  0% { transform: scale(1) rotate(0); }
  15% { transform: scale(1.4) rotate(-5deg); }
  30% { transform: scale(0.95) rotate(3deg); }
  45% { transform: scale(1.3) rotate(-3deg); }
  70% { transform: scale(0.98) rotate(1deg); }
  100% { transform: scale(1) rotate(0); }
}

/* 书签收藏弹跳动画增强版 */
@keyframes bookmarkPop {
  0% { transform: scale(1) translateY(0) rotate(0); }
  30% { transform: scale(1.3) translateY(-5px) rotate(5deg); }
  50% { transform: scale(1.15) translateY(-3px) rotate(-3deg); }
  70% { transform: scale(1.2) translateY(-4px) rotate(2deg); }
  100% { transform: scale(1) translateY(0) rotate(0); }
}

/* 浮动动画效果 - 应用到激活状态的图标 */
.rating-icon.active {
  animation-iteration-count: 1; /* 初始动画只执行一次 */
}

.like-icon.active,
.bookmark-icon.active {
  animation-fill-mode: forwards; /* 保持动画结束状态 */
}

.like-icon.active:hover {
  animation: likeHover 1s ease infinite alternate;
}

.bookmark-icon.active:hover {
  animation: bookmarkHover 1s ease infinite alternate;
}

@keyframes likeHover {
  0% { transform: scale(1) translateY(0); }
  100% { transform: scale(1.15) translateY(-3px); }
}

@keyframes bookmarkHover {
  0% { transform: translateY(0) rotate(0); }
  100% { transform: translateY(-3px) rotate(5deg); }
}

/* 光晕闪烁效果增强 */
@keyframes pulseGlow {
  0% { opacity: 0.2; transform: scale(0.95); }
  50% { opacity: 0.4; transform: scale(1); }
  100% { opacity: 0.6; transform: scale(1.05); }
}

/* 闪烁光点效果（用于Active状态） */
.rating-item:has(.rating-icon.active)::after {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  top: 10%;
  right: 20%;
  box-shadow: 0 0 4px 1px rgba(255, 255, 255, 0.8);
  animation: twinkle 3s ease-in-out infinite;
}

.rating-item:has(.rating-icon.active)::before {
  animation: activeGlow 2s infinite alternate;
}

@keyframes twinkle {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.8; }
}

@keyframes activeGlow {
  0% { opacity: 0.3; transform: scale(0.9); }
  100% { opacity: 0.7; transform: scale(1.1); }
}

/* Further optimize the seller description display */
.seller-desc {
  font-size: 0.87rem;
  margin-bottom: 0.3rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  line-height: 1.4;
  text-align: center;
  transition: all 0.3s ease;
  max-width: 100%;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  text-overflow: ellipsis;
  white-space: normal;
  word-wrap: break-word;
  cursor: pointer;
  height: 4.2em;
  max-height: 4.2em;
  position: relative;
}

/* 深色主题描述文本 */
.sellers-page[data-theme="dark"] .seller-desc,
.sellers-page.dark-theme .seller-desc {
  color: rgba(255, 255, 255, 0.8);
}

.sellers-page[data-theme="dark"] .seller-card:hover .seller-desc,
.sellers-page.dark-theme .seller-card:hover .seller-desc {
  color: rgba(255, 255, 255, 0.95);
}

/* 浅色主题描述文本 */
.sellers-page[data-theme="light"] .seller-desc {
  color: rgba(51, 51, 51, 0.8);
}

.sellers-page[data-theme="light"] .seller-card:hover .seller-desc {
  color: rgba(51, 51, 51, 0.95);
}

/* Ensure ellipsis is visible by adding a pseudo-element if needed */
.seller-desc::after {
  content: "...";
  position: absolute;
  bottom: 0;
  right: 0;
  padding-left: 3px;
  background-color: inherit;
  display: none; /* Only shown when needed via JavaScript */
}

/* Style for the Element UI tooltip */
.description-tooltip {
  width: 100%;
  display: block;
}

/* Improved tooltip styling */
:deep(.el-tooltip__popper) {
  max-width: 320px;
  width: auto;
  background-color: rgba(40, 40, 60, 0.95) !important;
  backdrop-filter: blur(5px);
  border: 1px solid #8066cc;
  border-radius: 8px;
  font-size: 0.9rem;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.6);
  padding: 12px 16px;
  line-height: 1.6;
  z-index: 10000;
}

:deep(.el-popper__arrow::before) {
  background-color: rgba(40, 40, 60, 0.95) !important;
  border-color: #8066cc !important;
}

/* Stats indicators */
.seller-stats {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 0.2rem; /* Reduced from 0.3rem */
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.seller-card:hover .stat-item {
  color: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
}

.stat-icon {
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.seller-card:hover .stat-icon {
  opacity: 1;
}

.products-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFFFFF'%3E%3Cpath d='M19 5H5C3.9 5 3 5.9 3 7v10c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 12H5V7h14v10z'/%3E%3C/svg%3E");
}

.followers-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFFFFF'%3E%3Cpath d='M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5c-1.66 0-3 1.34-3 3s1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5C6.34 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5z'/%3E%3C/svg%3E");
}

/* Button styles with animation */
.seller-buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 0.4rem; /* Reduced from 0.5rem */
  margin-top: 0.2rem; /* Reduced from 0.3rem */
}

.marketplace-btn, .learn-more-btn {
  width: 100%;
  border-radius: 12px;
  padding: 0.65rem 0; /* Reduced from 0.7rem */
  cursor: pointer;
  font-weight: 700;
  font-size: 0.9rem;
  letter-spacing: 2px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: none;
}

/* Button text and icon positioning */
.btn-text {
  z-index: 2;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  display: inline-block;
}

.btn-icon {
  position: absolute;
  right: 1.5rem;
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  font-size: 1.1rem;
  font-weight: 700;
}

/* Marketplace button - futuristic design */
.marketplace-btn {
  background: linear-gradient(135deg, #8000ff, #6a00d9);
  color: #ffffff;
  position: relative;
  box-shadow: 0 4px 10px rgba(128, 0, 255, 0.3);
}

.marketplace-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: 12px;
}

.marketplace-btn::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #ff00cc, #3300ff, #00ccff);
  z-index: -1;
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.marketplace-btn:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 20px rgba(128, 0, 255, 0.4), 0 0 15px rgba(128, 0, 255, 0.2);
}

.marketplace-btn:hover::before {
  opacity: 1;
}

.marketplace-btn:hover::after {
  opacity: 0.5;
  animation: borderGlow 2s infinite alternate;
}

.marketplace-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(128, 0, 255, 0.3);
}

/* Learn more button - neon style */
.learn-more-btn {
  background: rgba(20, 20, 30, 0.4);
  color: #ffffff;
  position: relative;
  z-index: 1;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(128, 0, 255, 0.3);
}

.learn-more-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(128, 0, 255, 0.1), rgba(128, 0, 255, 0));
  opacity: 0;
  transition: opacity 0.4s ease;
  border-radius: 12px;
}

.learn-more-btn::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, #8000ff, #00ccff);
  z-index: -1;
  border-radius: 13px;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.learn-more-btn:hover {
  background: rgba(30, 30, 40, 0.6);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3), 0 0 15px rgba(128, 0, 255, 0.2);
  border-color: rgba(128, 0, 255, 0.5);
}

.learn-more-btn:hover::before {
  opacity: 1;
}

.learn-more-btn:hover::after {
  opacity: 0.3;
  animation: borderGlow 2s infinite alternate;
}

.learn-more-btn:active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* Button hover effects */
.marketplace-btn:hover .btn-icon, .learn-more-btn:hover .btn-icon {
  opacity: 1;
  transform: translateX(0);
  animation: pulseIcon 1.5s infinite alternate;
}

.marketplace-btn:hover .btn-text, .learn-more-btn:hover .btn-text {
  transform: translateX(-10px);
}

/* Button animations */
@keyframes borderGlow {
  0% { opacity: 0.3; }
  100% { opacity: 0.7; }
}

@keyframes pulseIcon {
  0% { transform: translateX(0); }
  100% { transform: translateX(3px); }
}

/* Loading styles */
.loading-state {
  min-height: 80px;
  display: flex;
    flex-direction: column;
  justify-content: center;
    align-items: center;
  margin: 3rem 0;
  gap: 1rem;
}

.loading-text {
  font-size: 1.1rem;
  font-weight: 500;
  letter-spacing: 1px;
  transition: color 0.3s ease;
}

/* 深色主题加载文本 */
.sellers-page[data-theme="dark"] .loading-text,
.sellers-page.dark-theme .loading-text {
  color: rgba(255, 255, 255, 0.8);
}

/* 浅色主题加载文本 */
.sellers-page[data-theme="light"] .loading-text {
  color: rgba(51, 51, 51, 0.8);
}

.load-more-trigger {
  height: 20px;
  margin: 3rem 0;
  visibility: hidden;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transition: all 0.3s ease;
}

/* 深色主题加载动画 */
.sellers-page[data-theme="dark"] .loading-spinner,
.sellers-page.dark-theme .loading-spinner {
  border: 3px solid rgba(128, 102, 204, 0.1);
  border-top: 3px solid #8066cc;
  box-shadow: 0 0 20px rgba(128, 102, 204, 0.2);
}

/* 浅色主题加载动画 */
.sellers-page[data-theme="light"] .loading-spinner {
  border: 3px solid rgba(147, 51, 234, 0.1);
  border-top: 3px solid #9333ea;
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-message {
  font-size: 1.1rem;
  font-weight: 500;
  padding: 1rem 2.5rem;
  border-radius: 30px;
  display: inline-block;
  letter-spacing: 1px;
  backdrop-filter: blur(5px);
  transition: all 0.3s ease;
}

/* 深色主题状态消息 */
.sellers-page[data-theme="dark"] .status-message,
.sellers-page.dark-theme .status-message {
  color: #ffffff;
  background: rgba(30, 30, 40, 0.6);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 浅色主题状态消息 */
.sellers-page[data-theme="light"] .status-message {
  color: #333333;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(147, 51, 234, 0.1);
}

.load-complete-message {
  border-left: 4px solid #8066cc;
}

.no-results-message {
  border-left: 4px solid #f44336;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.8s ease forwards;
  opacity: 0;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Responsive styles */
@media (max-width: 1400px) {
  .sellers-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

@media (max-width: 1200px) {
  .sellers-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .sellers-page {
    padding: 2rem 3rem;
  }
}

@media (max-width: 900px) {
  .sellers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
  
  .sellers-page {
    padding: 1.5rem 2rem;
  }
  
  .page-title {
    font-size: 2.5rem;
  }
  
  .seller-logo {
    width: 100px;
    height: 100px;
  }
}

@media (max-width: 600px) {
  .sellers-page {
    padding: 1rem;
  }
  
  .page-title {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
  
  .filter-tabs {
    flex-direction: row;
    gap: 0.8rem;
  }
  
  .filter-tab {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    width: 45%;
  }

  .sellers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    max-width: 100%;
    margin: 0 auto;
  }
  
  .seller-content {
    padding: 1rem;
  }

  .seller-logo {
    width: 80px;
    height: 80px;
  }

  .seller-name {
    font-size: 1.1rem;
  }
  
  .seller-badge {
    right: 25%;
    padding: 0.3rem 0.8rem;
    font-size: 0.65rem;
  }
  
  .seller-desc {
    font-size: 0.8rem;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    margin-bottom: 1rem;
  }
  
  .marketplace-btn, .learn-more-btn {
    padding: 0.6rem 0;
    font-size: 0.8rem;
    letter-spacing: 1px;
    border-radius: 8px;
  }
  
  .btn-icon {
    right: 0.8rem;
    font-size: 0.9rem;
  }
  
  .rating-container {
    gap: 2rem;
    margin: -0.7rem 0;
  }
  
  .rating-item {
    padding: 8px 12px;
  }
  
  .rating-icon {
    width: 24px;
    height: 24px;
  }
  
  .rating-value {
    font-size: 0.75rem;
  }
}

/* For small phones */
@media (max-width: 480px) {
  .sellers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }
  
  .seller-card {
    min-height: 240px; /* 减小最小高度 */
    max-height: 280px; /* 减小最大高度 */
    padding: 0;
  }
  
  .seller-badge {
    right: 20%;
    padding: 0.25rem 0.6rem;
    font-size: 0.6rem;
  }
  
  .seller-logo {
    width: 55px; /* 减小logo尺寸 */
    height: 55px;
    border-width: 2px;
    margin-bottom: 0.1rem; /* 减小底部间距 */
  }
  
  .seller-content {
    padding: 0.5rem 0.5rem 0.3rem; /* 减小内部padding */
  }
  
  .seller-name {
    font-size: 0.9rem;
    margin-bottom: 0.1rem; /* 减小间距 */
  }
  
  .seller-desc {
    font-size: 0.7rem;
    margin-bottom: 0.3rem; /* 减小间距 */
    -webkit-line-clamp: 2;
    line-clamp: 2;
    line-height: 1.2;
    height: 2.4em;
    max-height: 2.4em;
  }
  
  .seller-buttons {
    gap: 0.3rem; /* 减小按钮间距 */
    margin-top: 0.1rem;
  }
  
  .marketplace-btn, .learn-more-btn {
    padding: 0.35rem 0;
    font-size: 0.7rem;
    letter-spacing: 0.5px;
    border-radius: 6px;
  }
  
  .btn-icon {
    right: 0.6rem;
    font-size: 0.8rem;
  }
  
  .rating-container {
    gap: 0.6rem; /* 减小评分图标间距 */
    margin: 0.2rem 0; /* 减小上下间距 */
  }
  
  .rating-item {
    padding: 4px 8px; /* 减小评分项内部padding */
  }
  
  .rating-icon {
    width: 18px; /* 减小评分图标尺寸 */
    height: 18px;
  }
  
  .rating-value {
    font-size: 0.65rem; /* 减小评分数字尺寸 */
    margin-top: 0;
  }
  
  .seller-stats {
    gap: 0.8rem;
    margin-bottom: 0.3rem; /* 减小间距 */
  }
  
  .stat-item {
    font-size: 0.7rem;
  }
}

/* 恢复被删除的基础样式 */
.rating-item:hover {
  transform: translateY(-3px);
  background-color: rgba(40, 40, 50, 0.6);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 10px rgba(128, 102, 204, 0.2);
}

/* 增强图标的视觉效果和动画 */
.rating-icon {
  width: 28px;  /* Reduced from 30px */
  height: 28px;  /* Reduced from 30px */
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  opacity: 0.85;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 图标点击动画 */
.rating-item:active .rating-icon {
  transform: scale(0.9);
  transition: all 0.1s ease;
}

/* 点赞图标基础样式 */
.like-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238066cc'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}

/* 收藏图标基础样式 */
.bookmark-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238066cc'%3E%3Cpath d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 收藏图标动态效果 */
.bookmark-icon.active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffeb3b'%3E%3Cpath d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
  animation: bookmarkPop 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  filter: drop-shadow(0 0 8px rgba(255, 235, 59, 0.7));
}

/* 改进数值样式和动画 */
.rating-value {
  font-size: 0.8rem; /* Slightly reduced from 0.85rem */
  font-weight: 600;
  color: rgba(255, 255, 255, 0.85);
  transition: all 0.3s ease;
  position: relative;
  margin-top: 1px; /* Reduced from 2px - minimal space between icon and text */
}

.rating-item:hover .rating-value {
  color: #ffffff;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* 全局主题适配样式 */
/* 确保所有文本元素都能正确响应主题变化 */
.sellers-page[data-theme="light"] .rating-value {
  color: rgba(51, 51, 51, 0.85);
}

.sellers-page[data-theme="light"] .rating-item:hover .rating-value {
  color: #333333;
  text-shadow: 0 0 8px rgba(51, 51, 51, 0.3);
}

/* 确保按钮在浅色主题下的可见性 */
.sellers-page[data-theme="light"] .marketplace-btn {
  background: linear-gradient(135deg, #9333ea, #7c3aed);
  box-shadow: 0 4px 10px rgba(147, 51, 234, 0.3);
}

.sellers-page[data-theme="light"] .marketplace-btn:hover {
  box-shadow: 0 8px 20px rgba(147, 51, 234, 0.4), 0 0 15px rgba(147, 51, 234, 0.2);
}

.sellers-page[data-theme="light"] .learn-more-btn {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(147, 51, 234, 0.3);
  color: #333333;
}

.sellers-page[data-theme="light"] .learn-more-btn:hover {
  background: rgba(255, 255, 255, 0.8);
  border-color: rgba(147, 51, 234, 0.5);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 0 15px rgba(147, 51, 234, 0.2);
}

/* 确保评分图标在浅色主题下的可见性 */
.sellers-page[data-theme="light"] .like-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%239333ea'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}

.sellers-page[data-theme="light"] .bookmark-icon {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%239333ea'%3E%3Cpath d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

/* 浅色主题下的评分容器背景 */
.sellers-page[data-theme="light"] .rating-item {
  background-color: rgba(255, 255, 255, 0.6);
}

.sellers-page[data-theme="light"] .rating-item:hover {
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1), 0 0 10px rgba(147, 51, 234, 0.1);
}
</style>