<template>
  <div class="footer-disclaimer-glow">
    <div class="footer-grid">
      <div class="footer-logo-links">
        <div class="footer-logo">
          <img src="@/assets/logo2.png" alt="Logo" />
        </div>
        <div class="footer-links mobile-footer-links">
          <a href="#">Cookie</a>
          <a href="#">Data security</a>
          <a href="#">Privacy policy</a>
          <a href="#">Disclaimer</a>
        </div>
      </div>
      <div class="footer-text">
        <p>OMGBUY.com is not affiliated with Weidian.com, Taobao.com, 1688.com, tmall.com or any other shopping site ("platforms").<br>
          This website is not an official offer of these platforms.</p>
        <p>All omgbuy.com links are affiliate links! This includes the price tag buttons, *tagged links and the links embedded in images.<br>
          We do not get a commission for the sale of fake items, only for their mention as a freight forwarder.</p>
        <p>We are not an online store and do not sell any products and is in no way associated with the <PERSON> website or brand.<br>
          Our website is designed solely to help you find products available on Clifford.</p>
        <p>Please be aware that this website contains affiliate links.<br>
          This means that if you make a purchase through these links, we may earn a small commission. <br>
          This commission helps us maintain and improve our website at no extra cost to you. We appreciate your support!</p>
      </div>
      <div class="footer-links desktop-footer-links">
        <a href="#">Cookie</a>
        <a href="#">Data security</a>
        <a href="#">Privacy policy</a>
        <a href="#">Disclaimer</a>
      </div>
      <div class="footer-email">
        <div class="email-icon-container">
          <svg class="email-icon-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="40" height="40">
            <rect width="20" height="16" x="2" y="4" rx="2" stroke="#a259ff" stroke-width="1.5" fill="none"/>
            <path d="M2 7l10 6.5L22 7" stroke="#a259ff" stroke-width="1.5" stroke-linecap="round" fill="none"/>
          </svg>
        </div>
        <a href="mailto:<EMAIL>" class="email-address"><EMAIL></a>
      </div>
    </div>
  </div>
  <div class="footer-copyright-bar">
    Copyright 2025 https://omgbuy.com - All Rights Reserved Manufactured by Antagonist Enterprise
  </div>
</template>

<style scoped>
@font-face {
  font-family: 'CustomFont';
  src: local('Arial'), local('Helvetica');
  font-weight: 400;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.footer-disclaimer-glow {
  margin: 2.5rem auto 0;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  border-radius: clamp(30px, 8vw, 118px);
  background: #111111;
  box-shadow: 0 0 12px 2px #a259ff;
  border: 0.5px solid #a259ff;
  position: relative;
  z-index: 10;
  padding: clamp(1rem, 2vw, 1.5rem) 0;
  overflow: hidden;
  height: auto;
  min-height: clamp(140px, 20vw, 160px);
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #111111;
}

.footer-grid {
  display: grid;
  grid-template-columns: 1fr 2.5fr 1fr 1fr;
  align-items: center;
  gap: clamp(1rem, 2vw, 2rem);
  padding: 0 clamp(1rem, 4vw, 3rem);
  width: 100%;
  max-width: 2507px;
  margin: 0 auto;
}

.footer-logo img {
  width: clamp(120px, 15vw, 210px);
  height: auto;
  display: block;
  filter: drop-shadow(0 0 12px #a259ff);
  margin: 0 auto;
}

.footer-text {
  color: rgba(255, 255, 255, 1);
  font-size: clamp(0.6rem, 1.2vw, 0.75rem);
  line-height: 1.35;
  min-width: 280px;
  padding: 0 clamp(0.5rem, 2vw, 1.5rem);
  max-width: 100%;
  font-weight: normal;
  font-family: 'CustomFont', sans-serif;
  -webkit-font-smoothing: antialiased;
}

.footer-text p {
  margin-bottom: 0.5rem;
  color: rgba(255, 255, 255, 1);
}

.footer-text p:last-child {
  margin-bottom: 0;
}

.footer-links {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: clamp(0.4rem, 1vw, 0.8rem);
  margin-left: clamp(20px, 3vw, 40px);
}

.footer-links a {
  color: rgba(255, 255, 255, 1);
  font-weight: 500;
  text-decoration: none;
  font-size: clamp(0.7rem, 1.5vw, 0.9rem);
  margin: 0 auto;
  font-family: 'CustomFont', sans-serif;
  -webkit-font-smoothing: antialiased;
  transition: all 0.3s ease;
}

.footer-links a:hover {
  color: #ffffff;
  opacity: 0.9;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.8);
}

.footer-email {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
  margin-top: 0;
  position: relative;
  padding: 1rem;
  margin-right: clamp(0.5rem, 2vw, 1rem);
}

.footer-email:before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(162, 89, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
  z-index: -1;
}

.email-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
}

.email-icon-svg {
  width: clamp(24px, 3vw, 28px);
  height: clamp(24px, 3vw, 28px);
  filter: drop-shadow(0 0 5px rgba(162, 89, 255, 0.7));
}

.footer-email a {
  color: rgba(255, 255, 255, 1);
  font-weight: normal;
  text-decoration: none;
  font-size: clamp(0.7rem, 1.5vw, 0.9rem);
  display: block;
  text-align: center;
  font-family: 'CustomFont', sans-serif;
  -webkit-font-smoothing: antialiased;
  transition: all 0.3s ease;
}

.footer-email a:hover {
  text-decoration: none;
  opacity: 1;
  text-shadow: 0 0 2px #fff, 0 0 5px #fff, 0 0 10px #fff;
}

.footer-copyright-bar {
  width: 100%;
  position: relative;
  z-index: 9;
  background: transparent;
  color: #fff;
  font-weight: bold;
  font-size: clamp(0.7rem, 1.5vw, 0.9rem);
  letter-spacing: 1px;
  text-align: center;
  padding: clamp(0.8rem, 2vw, 1rem) 0;
  box-shadow: none;
  text-shadow: 0 0 8px #a259ff, 0 0 16px #fff;
  border-top: none;
  margin-top: clamp(1rem, 2vw, 1.5rem);
}

/* 超小屏幕 (手机竖屏) */
@media (max-width: 480px) {
  .footer-disclaimer-glow {
    margin: 1.5rem auto 2rem;
    width: 95%;
    border-radius: 30px;
    padding: 1rem 0.5rem;
    min-height: 120px;
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 0 0.5rem;
    display: flex;
    flex-direction: column;
  }
  
  .footer-logo-links {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    gap: 1rem;
  }
  
  .footer-logo img {
    width: 100px;
    margin: 0;
  }
  
  .footer-text {
    font-size: 0.6rem;
    line-height: 1.2;
    text-align: center;
    padding: 0;
    min-width: auto;
  }
  
  .footer-text p {
    margin-bottom: 0.3rem;
  }
  
  .mobile-footer-links {
    display: flex !important;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.8rem;
    margin: 0;
  }
  
  .mobile-footer-links a {
    font-size: 0.7rem;
    white-space: nowrap;
  }
  
  .desktop-footer-links {
    display: none !important;
  }
  
  .footer-email {
    margin: 1rem 0 0 0;
    padding: 0.8rem;
    border-top: 1px solid rgba(162, 89, 255, 0.2);
    width: 100%;
  }
  
  .email-icon-svg {
    width: 20px;
    height: 20px;
  }
  
  .footer-email a {
    font-size: 0.7rem;
  }
  
  .footer-copyright-bar {
    font-size: 0.6rem;
    padding: 0.8rem 0.5rem;
    line-height: 1.3;
  }
}

/* 小屏幕 (手机横屏/小平板) */
@media (min-width: 481px) and (max-width: 768px) {
  .footer-disclaimer-glow {
    margin: 2rem auto 2.5rem;
    width: 92%;
    border-radius: 50px;
    padding: 1.2rem 1rem;
    min-height: 140px;
  }
  
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 1.2rem;
    padding: 0 1rem;
    display: flex;
    flex-direction: column;
  }
  
  .footer-logo-links {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
  }
  
  .footer-logo {
    flex: 0 0 40%;
  }
  
  .footer-logo img {
    width: 130px;
    margin: 0;
  }
  
  .mobile-footer-links {
    display: flex !important;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.4rem;
    flex: 0 0 50%;
    margin: 0;
  }
  
  .mobile-footer-links a {
    font-size: 0.8rem;
  }
  
  .desktop-footer-links {
    display: none !important;
  }
  
  .footer-text {
    font-size: 0.65rem;
    text-align: center;
    padding: 0 0.5rem;
    min-width: auto;
  }
  
  .footer-email {
    margin-top: 1.2rem;
    border-top: 1px solid rgba(162, 89, 255, 0.2);
    padding-top: 1rem;
    width: 100%;
  }
  
  .email-icon-svg {
    width: 24px;
    height: 24px;
  }
  
  .footer-email a {
    font-size: 0.8rem;
  }
}

/* 中等屏幕 (平板) */
@media (min-width: 769px) and (max-width: 1023px) {
  .footer-disclaimer-glow {
    margin: 2rem auto 3rem;
    width: 95%;
    border-radius: 80px;
    padding: 1.3rem 1.5rem;
    min-height: 150px;
  }
  
  .footer-grid {
    grid-template-columns: 0.9fr 2.6fr 0.8fr 0.8fr;
    gap: 1.2rem;
    padding: 0 1.5rem;
  }
  
  .footer-logo img {
    width: 140px;
  }
  
  .footer-text {
    font-size: 0.7rem;
    padding: 0 0.8rem 0 0;
    min-width: 280px;
    line-height: 1.4;
  }
  
  .footer-links {
    margin-left: 10px;
  }
  
  .footer-links a {
    font-size: 0.8rem;
  }
  
  .mobile-footer-links {
    display: none !important;
  }
  
  .desktop-footer-links {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    gap: 0.6rem;
    margin-right: 60px;
  }
  
  .footer-email {
    margin-right: 0.3rem;
  }
  
  .email-icon-svg {
    width: 26px;
    height: 26px;
  }
  
  .footer-email a {
    font-size: 0.8rem;
  }
}

/* 小笔记本屏幕 (1024px-1199px) */
@media (min-width: 1024px) and (max-width: 1199px) {
  .footer-disclaimer-glow {
    margin: 2.5rem auto 0;
    width: 96%;
    border-radius: 90px;
    padding: 1.8rem 0;
    height: auto;
    max-height: none;
    min-height: 200px;
  }
  
  .footer-grid {
    grid-template-columns: 1fr 2.8fr 1fr 0.8fr;
    gap: 1rem;
    padding: 0 1.5rem;
    align-items: flex-start;
    justify-items: start;
  }
  
  .footer-logo-links {
    padding-left: 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .footer-logo img {
    width: 140px;
  }
  
  .footer-text {
    font-size: 0.7rem;
    padding: 0;
    min-width: 320px;
    max-width: none;
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  .footer-text p {
    margin-bottom: 0.8rem;
    text-align: left;
  }
  
  .footer-links {
    margin-left: 0;
    align-items: flex-start;
  }
  
  .footer-links a {
    font-size: 0.8rem;
    line-height: 1.4;
    margin-bottom: 0.4rem;
  }
  
  .mobile-footer-links {
    display: none !important;
  }
  
  .desktop-footer-links {
    display: flex !important;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.7rem;
    margin-right: 0;
  }
  
  .footer-email {
    margin-right: 0;
    padding: 0.9rem;
    align-items: center;
  }
  
  .email-icon-svg {
    width: 26px;
    height: 26px;
  }
  
  .footer-email a {
    font-size: 0.8rem;
    text-align: center;
  }
  
  .footer-email:before {
    width: 120%;
    height: 120%;
    background: radial-gradient(circle, rgba(162, 89, 255, 0.1) 0%, rgba(0, 0, 0, 0) 70%);
  }
}

/* 大屏幕 (桌面/笔记本) - 重点优化区域 */
@media (min-width: 1025px) and (max-width: 1199px) {
  .footer-disclaimer-glow {
    margin: 2.5rem auto 0;
    width: 96%;
    border-radius: 100px;
    padding: 1.6rem 0;
    height: auto;
    max-height: none;
    min-height: 170px;
  }
  
  .footer-grid {
    grid-template-columns: 1fr 2.3fr 0.8fr 0.8fr;
    gap: 1.3rem;
    padding: 0 2rem;
    align-items: start;
  }
  
  .footer-logo-links {
    padding-left: 0.8rem;
  }
  
  .footer-logo img {
    width: 150px;
  }
  
  .footer-text {
    font-size: 0.74rem;
    padding: 0 0.8rem 0 0;
    min-width: 300px;
    max-width: 420px;
    line-height: 1.4;
  }
  
  .footer-text p {
    margin-bottom: 0.75rem;
  }
  
  .footer-links {
    margin-left: 12px;
  }
  
  .footer-links a {
    font-size: 0.82rem;
    line-height: 1.3;
  }
  
  .mobile-footer-links {
    display: none !important;
  }
  
  .desktop-footer-links {
    display: flex !important;
    flex-direction: column;
    align-items: center;
    gap: 0.65rem;
    margin-right: 70px;
  }
  
  .footer-email {
    margin-right: 0.4rem;
    padding: 1rem;
  }
  
  .email-icon-svg {
    width: 28px;
    height: 28px;
  }
  
  .footer-email a {
    font-size: 0.82rem;
  }
  
  .footer-email:before {
    width: 125%;
    height: 125%;
    background: radial-gradient(circle, rgba(162, 89, 255, 0.12) 0%, rgba(0, 0, 0, 0) 70%);
  }
}

/* 笔记本屏幕优化 (1200px-1440px) */
@media (min-width: 1200px) and (max-width: 1440px) {
  .footer-disclaimer-glow {
    margin: 2.5rem auto 0;
    width: 98%;
    border-radius: 100px;
    padding: 2rem 0;
    height: auto;
    max-height: none;
    min-height: 200px;
  }
  
  .footer-grid {
    grid-template-columns: 1fr 2.5fr 1fr 0.9fr;
    gap: 1.2rem;
    padding: 0 2rem;
    align-items: flex-start;
    justify-items: start;
  }
  
  .footer-logo-links {
    padding-left: 0.8rem;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .footer-logo img {
    width: 155px;
  }
  
  .footer-text {
    font-size: 0.75rem;
    padding: 0;
    min-width: 350px;
    max-width: none;
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  .footer-text p {
    margin-bottom: 0.9rem;
    text-align: left;
  }
  
  .footer-links {
    margin-left: 0;
    align-items: flex-start;
  }
  
  .footer-links a {
    font-size: 0.85rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
  }
  
  .mobile-footer-links {
    display: none !important;
  }
  
  .desktop-footer-links {
    display: flex !important;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.8rem;
    margin-right: 0;
  }
  
  .footer-email {
    margin-right: 0;
    padding: 1.2rem;
    align-items: center;
  }
  
  .email-icon-svg {
    width: 30px;
    height: 30px;
  }
  
  .footer-email a {
    font-size: 0.85rem;
    text-align: center;
  }
  
  .footer-email:before {
    width: 130%;
    height: 130%;
    background: radial-gradient(circle, rgba(162, 89, 255, 0.15) 0%, rgba(0, 0, 0, 0) 70%);
  }
}

/* 4K和更大屏幕 */
@media (min-width: 2560px) {
  .footer-disclaimer-glow {
    max-height: none;
    min-height: 280px;
    border-radius: 150px;
    padding: 2.5rem 0;
  }
  
  .footer-grid {
    padding: 0 10rem;
    gap: 2rem;
    grid-template-columns: 1fr 2.2fr 1fr 0.9fr;
    align-items: flex-start;
    justify-items: start;
  }
  
  .footer-logo-links {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  
  .footer-logo img {
    width: 260px;
  }
  
  .footer-text {
    font-size: 1rem;
    min-width: 500px;
    max-width: none;
    padding: 0;
    line-height: 1.6;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }
  
  .footer-text p {
    margin-bottom: 1.2rem;
    text-align: left;
  }
  
  .footer-links {
    margin-left: 0;
    align-items: flex-start;
  }
  
  .footer-links a {
    font-size: 1.1rem;
    line-height: 1.4;
    margin-bottom: 0.8rem;
  }
  
  .desktop-footer-links {
    gap: 1rem;
    margin-right: 0;
    align-items: flex-start;
  }
  
  .footer-email {
    align-items: center;
  }
  
  .email-icon-svg {
    width: 36px;
    height: 36px;
  }
  
  .footer-email a {
    font-size: 1.1rem;
    text-align: center;
  }
  
  .footer-copyright-bar {
    font-size: 1.1rem;
    padding: 1.5rem 0;
  }
}

/* 移动端样式 - 替换为更简洁的in.vue样式 */
@media (max-width: 900px) {
  .footer-disclaimer-glow {
    margin: 2rem auto 3rem;
    width: 88%;
    max-width: 88%;
    box-sizing: border-box;
    border-radius: 65px;
    padding: 1.5rem 1.5rem;
    border: 1px solid #a259ff;
    box-shadow: 0 0 15px 2px #a259ff;
    z-index: 5;
    height: auto;
    max-height: none;
    min-height: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow: hidden;
    background: #111111;
    position: relative;
  }
  
  .footer-copyright-bar,
  .desktop-footer-links {
    display: none !important;
  }
  
  .footer-logo-links {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    margin-bottom: 1.5rem;
    padding: 0;
  }
  
  .footer-logo {
    margin: 0;
    flex: 0 0 45%;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: auto;
  }
  
  .footer-logo img {
    width: 120px;
    min-width: 100px;
    margin: 0;
    display: block;
    filter: drop-shadow(0 0 12px #a259ff);
  }
  
  .footer-text {
    font-size: 0.7rem;
    padding: 0 0.5rem;
    text-align: center;
    margin: 0;
    line-height: 1.2;
    color: rgba(255, 255, 255, 1);
    font-weight: normal;
    min-width: auto;
  }
  
  .footer-text p {
    margin-bottom: 0.5rem;
    color: rgba(255, 255, 255, 1); 
  }
  
  .mobile-footer-links {
    display: flex !important;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.25rem;
    margin: 0;
    flex: 0 0 45%;
    text-align: right;
    width: auto;
    padding-right: 0.5rem;
  }
  
  .mobile-footer-links a {
    font-size: 0.9rem;
    margin: 0 auto;
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
    font-family: 'CustomFont', sans-serif;
    -webkit-font-smoothing: antialiased;
    text-decoration: none;
    transition: all 0.3s ease;
  }
  
  .mobile-footer-links a:hover {
    color: #ffffff;
    text-shadow: 0 0 1px #fff, 0 0 2px rgba(255, 255, 255, 0.8);
  }
  
  .footer-email {
    margin-top: 1.5rem;
    border-top: 1px solid rgba(162, 89, 255, 0.2);
    padding-top: 1.2rem;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .email-icon-svg {
    width: 32px;
    height: 32px;
    filter: drop-shadow(0 0 10px rgba(162, 89, 255, 0.8));
  }
  
  .footer-email a {
    font-size: 1rem;
    color: rgba(255, 255, 255, 1);
    margin-top: 0.3rem;
  }
  
  .footer-grid {
    grid-template-columns: auto;
    gap: 1rem;
    padding: 0;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .footer-disclaimer-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(162, 89, 255, 0.12) 0%, rgba(0, 0, 0, 0) 70%);
    z-index: -1;
    pointer-events: none;
  }
}

@media (min-width: 901px) {
  .footer-logo-links {
    display: block;
  }
  
  .mobile-footer-links {
    display: none !important;
  }
  
  .footer-text {
    text-align: left;
    margin: 0 auto;
    max-width: 90%;
  }
  
  .footer-email {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  
  .footer-disclaimer-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(ellipse at center, rgba(162, 89, 255, 0.15) 0%, rgba(0, 0, 0, 0) 70%);
    z-index: -1;
    pointer-events: none;
  }
  
  .footer-disclaimer-glow::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
      radial-gradient(circle at 20% 30%, rgba(162, 89, 255, 0.08) 0%, transparent 25%),
      radial-gradient(circle at 80% 30%, rgba(162, 89, 255, 0.08) 0%, transparent 25%);
    z-index: -1;
    pointer-events: none;
  }
  
  .footer-copyright-bar {
    position: relative;
    width: 100vw;
    left: 0;
    bottom: 0;
    z-index: 9;
    background: transparent;
    color: #fff;
    font-weight: bold;
    letter-spacing: 1px;
    text-align: center;
    box-shadow: none;
    text-shadow: 0 0 8px #a259ff, 0 0 16px #fff;
    border-top: none;
  }
}

/* 邮箱链接悬浮效果 */
.email-address {
  transition: all 0.3s ease;
}

.email-address:hover {
  color: #fff;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .footer-links a,
  .footer-email a,
  .email-address {
    transition: none;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .footer-disclaimer-glow {
    border-width: 2px;
    box-shadow: 0 0 20px 3px #a259ff;
  }
  
  .footer-text {
    color: #ffffff;
  }
  
  .footer-links a,
  .footer-email a {
    color: #ffffff;
  }
}
</style> 