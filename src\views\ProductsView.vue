<template>
  <div class="products">
    <!-- 使用封装的星空背景组件 -->
    <StarryBackground />
    
    <!-- 添加世界地图组件 -->
    <div class="world-map-section">
      <WorldMap />
    </div>
    
    <!-- Banner Carousel Section -->
    <div class="banner-carousel-section">
      <div v-if="isLoadingBanners" class="loading-spinner-container">
        <div class="loading-spinner"></div>
      </div>
      <div v-else-if="banners.length > 0" class="banner-carousel">
        <div class="carousel-container"
             @touchstart="handleTouchStart"
             @touchmove="handleTouchMove"
             @touchend="handleTouchEnd">
          <div class="carousel-track" :style="{ transform: `translateX(-${currentBannerIndex * 100}%)` }">
            <div 
              v-for="(banner, index) in banners" 
              :key="index" 
              class="carousel-slide"
              @click="handleBannerClick(banner)"
            >
              <img :src="banner.imageUrl" :alt="banner.title" class="banner-image">
              <div class="banner-content">
                <h3 v-if="banner.title">{{ banner.title }}</h3>
                <p v-if="banner.description">{{ banner.description }}</p>
              </div>
            </div>
          </div>
          
          <button class="carousel-control prev" @click="prevBanner">
            <i class="fas fa-chevron-left"></i>
          </button>
          <button class="carousel-control next" @click="nextBanner">
            <i class="fas fa-chevron-right"></i>
          </button>
          
          <div class="carousel-indicators">
            <button 
              v-for="(_, index) in banners" 
              :key="index" 
              :class="['indicator', { active: index === currentBannerIndex }]"
              @click="goToBanner(index)"
              @mouseenter="goToBanner(index)"
            ></button>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filters-section">
      <div class="filter-container">
        <!-- Category Filter -->
        <div class="filter-group full-width">
          <div class="category-header product-category-header" style="text-align: center; width: 100%;">
            <h3 class="product-section-title" style="display: inline-block !important; visibility: visible !important;">Categories</h3>
          </div>
          <!-- 产品分类，直接显示，不需要展开按钮 -->
          <div class="category-buttons" :class="{ 
            'expanded': showAllCategories, 
            'all-categories-visible': showAllCategories 
          }">
            <button
              v-for="(category, index) in categories"
              :key="category.id"
              class="category-btn"
              :class="{ 
                active: selectedCategories.includes(category.id)
              }"
              @click="toggleCategory(category.id)"
              v-show="index < visibleCategoriesCount || showAllCategories"
            >
              {{ category.name }}
            </button>
            <!-- 添加查看更多按钮 -->
            <div v-if="categories.length > visibleCategoriesCount" class="view-more-container">
              <button 
                class="view-more-btn"
                @click="toggleShowAllCategories"
              >
                {{ showAllCategories ? 'Show Less' : 'Load more' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 品牌筛选 -->
        <div class="filter-group full-width" v-if="selectedCategories.length > 0" style="margin-top: -1.5rem; padding-top: 0; margin-bottom: 0;">
          <div class="brand-buttons" style="margin-top: 0;">
            <button
              v-for="brand in displayedBrands"
              :key="brand.id"
              class="category-btn"
              :class="{ active: selectedBrands.includes(brand.id) }"
              @click="toggleBrand(brand.id)"
            >
              {{ brand.name }}
            </button>
          </div>
          <!-- 移除了Show More/Less按钮，始终显示所有品牌 -->
        </div>

        <!-- Clear Filters -->
        <div class="filter-group full-width ">
          <button class="clear-filters" @click="clearFilters">
            {{ $t('productsView.reset') }}
          </button>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner">
        <i class="el-icon-loading"></i>
        <span>{{ $t('common.loading') }}</span>
      </div>
    </div>

    <!-- Error State -->
    <div v-if="error" class="error-message">
      {{ error }}
      <button @click="fetchProducts">Retry</button>
    </div>

    <!-- No Products Message -->
    <div v-else-if="!isLoading && filteredProducts.length === 0" class="no-products">
      <i class="el-icon-warning"></i>
      <p>{{ $t('productsView.noProductsFound') }}</p>
    </div>

     <!-- Search Section -->
     <div class="search-section">
      <div class="search-container">
        <!-- <div class="company-logo">
          <img src="@/assets/logo1.png" alt="Company Logo">
        </div> -->
        <div class="search-wrapper">
          <div class="search-icon-left">
            <i class="fas fa-search"></i>
          </div>
          <input 
            type="text" 
            v-model="searchQuery" 
            placeholder=""
            class="search-input"
            @keyup.enter="handleSearch"
            ref="searchInput"
          >
          <div class="typing-placeholder" v-if="!searchQuery">
            <span class="typing-text">{{ typingPlaceholder }}</span>
            <span class="typing-cursor">|</span>
          </div>
          <button class="search-button" @click="handleSearch">
            <span class="search-button-text">Search</span>
            <i class="fas fa-arrow-right"></i>
          </button>
        </div>
      </div>
    </div>


    <!-- Sorting Buttons -->
    <div class="sorting-section">
      <div class="sorting-container">
        <button 
          class="sort-btn" 
          :class="{ active: sortOption === 'views' }"
          @click="sortByViews"
        >
          <i class="fas fa-eye"></i> {{ $t('productsView.sortByViews') }}
        </button>
        
        <button 
          class="sort-btn" 
          :class="{ active: sortOption === 'price' }"
          @click="togglePriceSort"
        >
          <i class="fas fa-dollar-sign"></i> Sort by Price
          <i v-if="sortOption === 'price'" 
             :class="[
               priceSortDirection === 'asc' ? 'fas fa-arrow-up' : 'fas fa-arrow-down',
               'sort-direction-icon'
             ]">
          </i>
        </button>
       <!--  <div class="price-range-dropdown">
          <button 
            class="sort-btn dropdown-trigger" 
            :class="{ active: priceRange.min !== null && priceRange.max !== null }"
            @click="togglePriceRangeDropdown"
          >
            <i class="fas fa-filter"></i> {{ $t('productsView.priceRange') }}
            <i class="fas fa-chevron-down" :class="{ 'is-open': isPriceRangeDropdownOpen }"></i>
          </button>
          
          <div class="dropdown-menu price-range-menu" v-show="isPriceRangeDropdownOpen">
            <div class="price-range-content">
              <div class="price-inputs">
                <input 
                  type="number" 
                  v-model.number="priceRange.min" 
                  :placeholder="`Min ${currencySymbol}`"
                  class="price-input"
                  min="0"
                >
                <span class="price-separator">-</span>
                <input 
                  type="number" 
                  v-model.number="priceRange.max" 
                  :placeholder="`Max ${currencySymbol}`"
                  class="price-input"
                  min="0"
                >
              </div>
              <button 
                class="dropdown-item apply-btn"
                @click="handlePriceSearch"
                :disabled="!priceRange.min || !priceRange.max"
              >
                {{ $t('productsView.apply') }}
              </button>
            </div>
          </div>
        </div> -->
      </div>
    </div>

    <!-- Products Grid -->
    <div class="products-grid">
      <div v-for="product in sortedProducts.slice(0, displayedProducts.length || 16)" :key="product.id" class="product-card" @click="viewDetails(product.id)">
        <div class="product-image">
          <img :src="product.image" :alt="product.name">
        </div>
        <div class="product-info">
          <h3 class="product-name">{{ product.name }}</h3>
          <div class="price-container">
            <p class="current-price">{{ currencySymbol }}{{ formatPrice(product.price) }}</p>
            <p v-if="product.originalPrice" class="original-price">{{ currencySymbol }}{{ formatPrice(product.originalPrice) }}</p>
          </div>
          <div class="product-stats">
            <span @click.stop="handleLike(product)" class="stat-item" :class="{ 'liked': product.isLiked }">
              <i class="fas fa-thumbs-up"></i> {{ product.likes }}
            </span>
            <span @click.stop="goToProductDetail(product.id, 'views')" class="stat-item">
              <i class="fas fa-eye"></i> {{ product.views }}
            </span>
            <span @click.stop="goToProductDetail(product.id, 'comments')" class="stat-item">
              <i class="fas fa-comment"></i> {{ (product.comments && product.comments.length) || 0 }}
            </span>
            <span @click.stop="handleCollect(product)" class="stat-item" :class="{ 'collected': product.isCollected === true }">
              <i class="fas fa-heart"></i>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Spinner and No More Data Message -->
    <div class="load-more-container">
      <!-- Loading spinner when loading more products -->
      <div v-if="isLoadingMore" class="cosmic-loader">
        <div class="cosmic-loader-ring"></div>
        <div class="cosmic-loader-stars">
          <div class="cosmic-star" v-for="n in 8" :key="n"></div>
        </div>
      </div>
      
      <!-- No more products message -->
      <div v-if="!isLoadingMore && noMoreProducts" class="no-more-products">
        <div class="no-more-icon">
          <i class="fas fa-satellite"></i>
        </div>
        <p>No more products available</p>
        <div class="cosmic-line"></div>
      </div>
    </div>

    <!-- 分页控件，不再显示 -->
    <!-- <div v-if="totalPages > 1" class="pagination">
      <button 
        :disabled="currentPage === 1" 
        @click="changePage(currentPage - 1)"
        class="page-btn"
      >
        Previous
      </button>
      <span class="page-numbers">
        <button 
          v-for="page in totalPages" 
          :key="page"
          @click="changePage(page)"
          :class="['page-number', { active: currentPage === page }]"
        >
          {{ page }}
        </button>
      </span>
      <button 
        :disabled="currentPage === totalPages" 
        @click="changePage(currentPage + 1)"
        class="page-btn"
      >
        Next
      </button>
    </div> -->
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import { emitter } from '@/utils/eventBus'
import productsApi from '@/api/products'
import { addCollection, removeFavorite } from '@/api/favorites'
import apiClient from '@/services/api'
import StarryBackground from '@/components/StarryBackground.vue'
import WorldMap from '@/components/WorldMap.vue'


export default {
  name: 'ProductsView',
  components: {
    StarryBackground,
    WorldMap
  },
  data() {
    return {
      searchQuery: '',
      selectedCategories: [],
      selectedBrands: [],
      priceRange: {
        min: null,
        max: null
      },
      currentPage: 1,
      itemsPerPage: 16,
      // API分页参数（用于后端分页）
      apiCurrentPage: 1,
      apiPageSize: 20,
      hasMorePages: true, // 是否还有更多页数据
      categories: [],
      brands: [],
      products: [],
      filteredProducts: [],
      displayedProducts: [],
      totalProducts: 0, // 服务器返回的总条数
      isLoading: false,
      isLoadingMore: false,
      error: null,
      visibleCategoriesCount: 5, // 一行只显示5个分类
      visibleBrandsCount: 60, // 增加品牌显示数量到60个，支持显示更多行
      sortOption: null,
      priceSortDirection: null,
      isPriceDropdownOpen: false,
      isPriceRangeDropdownOpen: false,
      currency: 'USD',
      currencySymbol: '$',
      exchangeRate: 1,
      isRequestPending: false,
      noMoreProducts: false,
      scrollObserver: null,
      // Banner data
      banners: [],
      currentBannerIndex: 0,
      isLoadingBanners: false,
      bannerInterval: null,
      // Brand display
      showAllBrands: false,
      showAllCategories: false, // 新增变量控制分类显示
      initialDisplayCount: 5, // 1 row x 5 brands
      currentDisplayCount: 5, // 当前显示的品牌数量
      brandsPerRow: 5, // 每行显示的品牌数量
      cachedProducts: null,
      // 触摸滑动相关
      touchStartX: 0,
      touchEndX: 0,
      minSwipeDistance: 50, // 最小滑动距离
      resizeTimer: null,
      typingPlaceholder: '',
      placeholderTexts: ['Explore treasures, discover surprises...', 'Find your favorite items...', 'Search for products you love...', 'Discover unique treasures...'],
      currentPlaceholderIndex: 0,
      typingSpeed: 100,
      deletingSpeed: 50,
      pauseDelay: 1500,
      isTyping: true,
      typingTimeout: null,
      // 添加品牌加载状态标志
      isLoadingBrands: false,
      isCheckingLikeStatus: false,
      isCheckingCollectionStatus: false
    }
  },
  computed: {
    queryParams() {
      return {
        search: this.searchQuery,
        categories: this.selectedCategories.join(','),
        minPrice: this.priceRange.min || undefined,
        maxPrice: this.priceRange.max || undefined
      }
    },
    totalPages() {
      return Math.ceil(this.totalProducts / this.itemsPerPage)
    },
    displayedBrands() {
      // 在加载品牌时返回空数组，避免显示全部品牌
      if (this.isLoadingBrands) {
        return [];
      }
      
      // 如果是从分类选择进来的，限制显示的品牌数量
      if (this.selectedCategories.length > 0) {
        // 只显示visibleBrandsCount个品牌
        return this.brands.slice(0, this.visibleBrandsCount);
      }
      
      // 其他情况返回所有品牌
      return this.brands;
    },
    
    // 计算属性：根据排序状态返回排序后的产品
    sortedProducts() {
      console.log('🔄 计算属性sortedProducts被调用，当前排序状态:', this.sortOption, this.priceSortDirection);
      
      if (!this.filteredProducts || this.filteredProducts.length === 0) {
        return [];
      }
      
      let sorted = [...this.filteredProducts];
      
      if (this.sortOption === 'views') {
        sorted.sort((a, b) => b.views - a.views);
        console.log('🔄 计算属性中按浏览量排序');
      } else if (this.sortOption === 'price' && this.priceSortDirection) {
        if (this.priceSortDirection === 'asc') {
          sorted.sort((a, b) => a.price - b.price);
          console.log('🔄 计算属性中按价格升序排序');
        } else {
          sorted.sort((a, b) => b.price - a.price);
          console.log('🔄 计算属性中按价格降序排序');
        }
      }
      
      if (sorted.length > 0) {
        console.log('🔄 排序后前3个商品:', sorted.slice(0, 3).map(p => `${p.name}: $${p.price}`));
      }
      
      return sorted;
    }
  },
  methods: {
    /**
     * 获取分类列表
     */
    async fetchCategories() {
      try {
        const response = await productsApi.getAllCategories();
        if (response.code === 200 && response.data) {
          this.categories = response.data.map(category => ({
            id: category.categoryId || category.id,
            name: category.name
          }));
        } else {
          console.error('Error getting categories:', response);
          this.categories = [];
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        this.categories = [];
      }
    },
    
    /**
     * 获取品牌列表
     */
    async fetchBrands() {
      try {
        const response = await productsApi.getAllBrands();
        if (response.code === 200 && response.data) {
          this.brands = response.data.map(brand => ({
            id: brand.brandId || brand.id,
            name: brand.name,
            logoUrl: brand.logoUrl
          }));
        } else {
          console.error('Error getting brands:', response);
          this.brands = [];
        }
      } catch (error) {
        console.error('Error fetching brands:', error);
        this.brands = [];
      }
    },
    
    /**
     * 获取商品列表
     */
    async fetchProducts(isLoadMore = false) {
      console.log('🔥 fetchProducts被调用，当前选中分类:', this.selectedCategories, '是否加载更多:', isLoadMore);
      console.log('🔥 调用时的排序状态:', this.sortOption, this.priceSortDirection);
      
      let newProducts = []; // 将变量声明移到方法开头，确保整个方法都能访问
      
      try {
        if (!isLoadMore) {
          this.isLoading = true;
          this.apiCurrentPage = 1; // 重置API页码
          this.hasMorePages = true; // 重置分页状态
        } else {
          this.isLoadingMore = true;
        }
        
        this.noMoreProducts = false; // 重置状态
        let response;
        
        // 使用本地变量存储当前选中分类，避免请求过程中被修改
        const selectedCat = this.selectedCategories.length > 0 ? this.selectedCategories[0] : null;
        const selectedBrand = this.selectedBrands.length > 0 ? this.selectedBrands[0] : null;
        
        // 构建分页参数
        const paginationParams = {
          page: this.apiCurrentPage,
          pageSize: this.apiPageSize
        };
        
        // 判断是否有选中的分类
        if (selectedCat || selectedBrand) {
          console.log('根据分类获取商品:', selectedCat, '页码:', this.apiCurrentPage);
          console.log('根据品牌获取商品:', selectedBrand);
          
          const categoryParams = {
            minPrice: this.priceRange.min,
            maxPrice: this.priceRange.max,
            ...paginationParams
          };
          
          response = await productsApi.getProductsByCategory(selectedCat, selectedBrand, categoryParams);
        } else {
          // 搜索商品或获取所有商品，使用相同的API
          const params = {
            ...paginationParams
          };
          
          // 只在有搜索关键词时添加keyword参数
          if (this.searchQuery && this.searchQuery.trim()) {
            console.log('根据关键词搜索商品:', this.searchQuery, '页码:', this.apiCurrentPage);
            params.keyword = this.searchQuery.trim();
          } else {
            console.log('获取所有商品，页码:', this.apiCurrentPage);
          }
          
          // 添加价格范围参数(如果有)
          if (this.priceRange.min !== null) {
            params.minPrice = this.priceRange.min;
          }
          if (this.priceRange.max !== null) {
            params.maxPrice = this.priceRange.max;
          }
          
          response = await productsApi.searchProducts(params);
        }
        
        console.log('API响应:', response);
        
        if (response.code === 200 && response.data) {
          // 处理新的数据格式，数据在 data.list 中
          const dataList = response.data.list || response.data; // 兼容两种数据格式
          const totalCount = response.data.total || 0; // 获取总条数
          
          newProducts = dataList.map(item => ({
            ...item,
            id: item.productId || item.id,
            mainImage: item.mainImage || item.image,
            image: item.mainImage || item.image,
            comments: item.comments || [],
            likes: item.likes || 0,
            views: item.views || 0,
            isCollected: false
          }));
          
          // 检查数据结构，确保排序所需的字段存在
          if (newProducts.length > 0) {
            console.log('商品数据结构检查:', {
              firstProduct: newProducts[0],
              hasPrice: 'price' in newProducts[0],
              hasViews: 'views' in newProducts[0],
              priceValue: newProducts[0].price,
              viewsValue: newProducts[0].views
            });
          }
          
          if (!isLoadMore) {
            // 首次加载，替换产品列表
            this.products = newProducts;
            this.filteredProducts = [...this.products];
            this.totalProducts = totalCount; // 使用服务器返回的总条数
            
            // 应用当前的排序状态（如果有）
            this.applySortingIfNeeded();
            
            // 重置分页并加载第一页
            this.currentPage = 1;
            this.loadInitialProducts();
          } else {
            // 加载更多，追加到现有产品列表
            this.products = [...this.products, ...newProducts];
            this.filteredProducts = [...this.products];
            // totalProducts 保持为服务器返回的总条数，不累加
            
            // 应用当前的排序状态（如果有）
            this.applySortingIfNeeded();
            
            // 重新计算需要显示的产品数量（保持之前显示的数量 + 新的一页）
            const newDisplayCount = this.displayedProducts.length + this.itemsPerPage;
            const actualDisplayCount = Math.min(newDisplayCount, this.filteredProducts.length);
            this.displayedProducts = this.filteredProducts.slice(0, actualDisplayCount);
            
            console.log(`加载更多后重新计算显示产品，当前显示: ${this.displayedProducts.length} 个`);
          }
          
          // 根据总条数和当前已加载的数据量判断是否还有更多页
          const currentLoadedCount = this.products.length;
          const hasMoreData = currentLoadedCount < totalCount;
          
          if (!hasMoreData || newProducts.length < this.apiPageSize) {
            this.hasMorePages = false;
            this.noMoreProducts = true;
          } else {
            this.hasMorePages = true;
            this.apiCurrentPage++; // 为下次加载更多准备
          }
          
          console.log(`数据加载完成: 当前${currentLoadedCount}条，总共${totalCount}条，还有更多: ${this.hasMorePages}`);
        } else {
          console.error('获取产品错误:', response.msg);
          if (!isLoadMore) {
            this.products = [];
            this.filteredProducts = [];
            this.displayedProducts = [];
            this.totalProducts = 0; // 重置总条数
          }
          this.hasMorePages = false;
          this.noMoreProducts = true;
        }

        // 检查每个商品的收藏状态
        if (localStorage.getItem('isLoggedIn')) {
          // 只检查新加载的商品
          const productsToCheck = isLoadMore ? newProducts : this.products;
          await this.checkCollectionStatus(productsToCheck);
        }

        // 检查每个商品的点赞状态
        if (localStorage.getItem('isLoggedIn')) {
          // 只检查新加载的商品
          const productsToCheck = isLoadMore ? newProducts : this.products;
          await this.checkLikeStatus(productsToCheck);
        }

      } catch (error) {
        console.error('获取产品错误:', error);
        if (!isLoadMore) {
          this.products = [];
          this.filteredProducts = [];
          this.displayedProducts = [];
          this.totalProducts = 0; // 重置总条数
        }
        this.hasMorePages = false;
        this.noMoreProducts = true;
      } finally {
        if (!isLoadMore) {
          this.isLoading = false;
        } else {
          this.isLoadingMore = false;
        }
      }
    },


    async handleCollect(product) {
      // 如果用户未登录，提示登录
      if (!localStorage.getItem('isLoggedIn')) {
        this.showToast('请先登录再收藏', 'warning');
        this.$router.push('/login');
        return;
      }
      
      try {
        console.log(`处理商品 ${product.id} 的收藏操作，当前状态:`, product.isCollected);
        
        if (product.isCollected) {
          // 取消收藏
          console.log(`取消收藏商品 ${product.id}`);
          await removeFavorite(product.id);
          product.isCollected = false;
          this.showToast('Removed from favorites');
        } else {
          // 添加收藏
          console.log(`添加收藏商品 ${product.id}`);
          await addCollection(product.id);
          product.isCollected = true;
          this.showToast('Added to favorites');
        }
        
        // 强制更新视图
        this.$forceUpdate();
        console.log(`商品 ${product.id} 收藏状态更新为:`, product.isCollected);
      } catch (error) {
        console.error('收藏操作失败:', error);
        this.showToast(product.isCollected ? 'Failed to remove from favorites' : 'Failed to add to favorites', 'error');
      }
    },
    
    // 检查商品是否被收藏
    async checkCollectionStatus(products) {
      if (!localStorage.getItem('isLoggedIn')) return;
      
      try {
        // 获取所有商品ID
        const productIds = products.map(p => p.id);
        
        // 批量检查收藏状态
        const response = await apiClient.post(`/omg/collect/isCollected`, {
          userId: JSON.parse(localStorage.getItem('userInfo')).userId,
          productIds: productIds
        });
        
        if (response.code === 200 && response.data) {
          // 更新每个商品的收藏状态
          products.forEach(product => {
            product.isCollected = response.data[product.id] || false;
          });
          
          // 强制更新视图
          this.$forceUpdate();
        }
      } catch (error) {
        console.error('批量检查收藏状态失败:', error);
      }
    },
    
    // 检查商品是否被点赞
    async checkLikeStatus(products) {
      if (!localStorage.getItem('isLoggedIn')) return;
      
      try {
        // 获取用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        if (!userId || products.length === 0) return;
        
        console.log(`开始批量检查${products.length}个产品的点赞状态`);
        
        // 获取所有产品ID
        const productIds = products.map(p => p.id);
        
        // 使用修改后的API批量检查
        const response = await productsApi.checkLikeToday(productIds);
        
        if (response && response.code === 200 && response.data) {
          // 更新每个产品的点赞状态
          products.forEach(product => {
            product.isLiked = response.data[product.id] || false;
          });
          
          // 强制更新视图
          this.$forceUpdate();
          console.log('批量点赞状态检查完成');
        } else {
          console.warn('批量检查未返回有效数据，回退到单个检查');
          await this.checkLikeStatusFallback(products);
        }
      } catch (error) {
        console.error('批量检查点赞状态失败:', error);
        // 回退到单个检查
        try {
          await this.checkLikeStatusFallback(products);
        } catch (err) {
          console.error('单个检查也失败了:', err);
        }
      }
    },
    
    // 逐个检查点赞状态的回退方法
    async checkLikeStatusFallback(products) {
      console.log('使用回退方法逐个检查点赞状态');
      
      // 分批处理，每批最多5个产品
      const batchSize = 5;
      
      for (let i = 0; i < products.length; i += batchSize) {
        const batch = products.slice(i, i + batchSize);
        
        // 并行发起请求以提高效率
        await Promise.all(batch.map(async (product) => {
          try {
            const checkResponse = await productsApi.checkLikeToday(product.id);
            if (checkResponse && checkResponse.code === 200) {
              product.isLiked = checkResponse.data && (checkResponse.data.isLiked || checkResponse.data.isLikedToday) || false;
            }
          } catch (err) {
            console.error(`检查产品 ${product.id} 点赞状态失败:`, err);
          }
        }));
      }
      
      // 强制更新视图
      this.$forceUpdate();
    },


    async applyFilters() {
      console.log('applyFilters被调用');
      // 重置页码
      this.currentPage = 1;
      
      // 获取产品
      await this.fetchProducts();
    },
    async viewDetails(productId) {
      this.$router.push(`/product/${productId}`)
    },
    async addToCart(productId) {
      if (this.loadingStates.addingToCart.has(productId)) return

      try {
        this.loadingStates.addingToCart.add(productId)
        // await cartApi.addToCart(productId, 1)
        this.$toast.success(this.$t('productsView.addToCartSuccess'))
      } catch (error) {
        this.$toast.error(this.$t('productsView.addToCartError') + (error.response?.data?.message || error.message))
        console.error('Error adding to cart:', error)
      } finally {
        this.loadingStates.addingToCart.delete(productId)
      }
    },
    // 防抖函数
    debounce(func, wait) {
      let timeout
      return (...args) => {
        clearTimeout(timeout)
        timeout = setTimeout(() => func.apply(this, args), wait)
      }
    },
    // 清除筛选条件
    clearFilters() {
      // 如果已有请求在进行中，忽略
      if (this.isRequestPending) {
        console.log('请求正在处理中，忽略清除筛选');
        return;
      }
      
      console.log('清除所有筛选条件');
      
      // 设置请求标志
      this.isRequestPending = true;
      
      // 重置所有筛选条件
      this.searchQuery = '';
      this.selectedCategories = [];
      this.selectedBrands = [];
      this.priceRange.min = null;
      this.priceRange.max = null;
      
      // 重置API分页状态
      this.apiCurrentPage = 1;
      this.hasMorePages = true;
      this.totalProducts = 0; // 重置总条数
      
      // 如果有保存的原始商品列表，恢复它
      if (this.cachedProducts) {
        this.filteredProducts = [...this.cachedProducts];
        this.cachedProducts = null;
      }
      
      // 获取所有品牌
      this.fetchBrands().then(() => {
        // 应用筛选 - 这会使用searchProducts但不带任何参数，相当于获取所有商品
        this.applyFilters().finally(() => {
          this.isRequestPending = false;
        });
      }).catch(() => {
        this.isRequestPending = false;
      });
    },
    async toggleCategory(categoryId) {
      // 如果已经有请求在进行中，则不处理
      if (this.isRequestPending) {
        console.log('请求正在处理中，忽略点击');
        return;
      }
      
      console.log('toggleCategory被调用，分类ID:', categoryId);
      
      // 检查当前分类是否已选中
      const isSelected = this.selectedCategories.includes(categoryId);
      
      // 设置请求标志，防止重复请求
      this.isRequestPending = true;
      
      // 重置API分页状态
      this.apiCurrentPage = 1;
      this.hasMorePages = true;
      
      try {
        // 重置价格区间筛选的原始列表
        this.cachedProducts = null;
        
        if (isSelected) {
          // 如果已选中，则取消选择
          this.selectedCategories = [];
          // 获取所有品牌
          await this.fetchBrands();
        } else {
          // 否则，设置为唯一选中项
          this.selectedCategories = [categoryId];
          
          // 获取该分类下的品牌列表
          const response = await productsApi.getBrandsByCategory(categoryId);
          if (response.code === 200 && response.data) {
            // 更新品牌列表
            this.brands = response.data.map(brand => ({
              id: brand.brandId || brand.id,
              name: brand.name,
              logoUrl: brand.logoUrl
            }));
            
            // 移除强制重置品牌显示状态的代码，让品牌可以正常显示多行
            // this.showAllBrands = false;
            // this.currentDisplayCount = this.initialDisplayCount;
          } else {
            console.error('获取品牌列表失败:', response);
            this.brands = [];
          }
        }
        
        // 重置已选中的品牌
        this.selectedBrands = [];
        
        // 应用筛选
        await this.applyFilters();
      } catch (error) {
        console.error('获取品牌列表或应用筛选时出错:', error);
        this.brands = [];
      } finally {
        // 请求完成后重置标志
        this.isRequestPending = false;
        // 品牌加载完成，重置加载状态
        this.isLoadingBrands = false;
      }
    },
    toggleBrand(brandId) {
      if (this.isRequestPending) {
        console.log('请求正在处理中，忽略点击');
        return;
      }
      
      console.log('toggleBrand被调用，品牌ID:', brandId);
      
      const isSelected = this.selectedBrands.includes(brandId);
      
      this.isRequestPending = true;
      
      // 重置API分页状态
      this.apiCurrentPage = 1;
      this.hasMorePages = true;
      
      // 重置价格区间筛选的原始列表
      this.cachedProducts = null;
      
      if (isSelected) {
        this.selectedBrands = [];
      } else {
        this.selectedBrands = [brandId];
      }
      
      this.applyFilters().finally(() => {
        this.isRequestPending = false;
      });
    },
    handlePriceSearch() {
      // 如果已有请求在进行中，忽略
      if (this.isRequestPending) {
        console.log('请求正在处理中，忽略价格搜索');
        return;
      }
      
      // 调用价格筛选方法
      if (this.filterProductsByPriceRange()) {
        // 关闭价格区间下拉菜单
        this.isPriceRangeDropdownOpen = false;
      }
    },
    async handleLike(product) {
      // 如果用户未登录，提示登录
      if (!localStorage.getItem('isLoggedIn')) {
        this.showToast('Please log in before liking', 'warning');
        this.$router.push('/login');
        return;
      }
      
      if (product.isLiked) {
        try {
          await productsApi.unlikeProduct(product.id)
          product.likes--
          product.isLiked = false
          this.showToast('Unlike successful')
        } catch (error) {
          this.showToast('Unlike failed', 'error')
          console.error('Unlike error:', error)
        }
      } else {
        try {
          // 检查用户今天是否已经点赞过该商品
          const checkResponse = await productsApi.checkLikeToday(product.id);
          
          if (checkResponse.code === 200 && checkResponse.data && checkResponse.data.isLikedToday) {
            this.showToast('You have already liked this product today', 'warning');
            return;
          }
          
          await productsApi.likeProduct(product.id)
          product.likes++
          product.isLiked = true
          this.showToast('Like successful')
        } catch (error) {
          this.showToast('Like failed', 'error')
          console.error('Like error:', error)
        }
      }
    },


    goToProductDetail(productId, tab = null) {
      if (tab === 'views') {
        const product = this.products.find(p => p.id === productId)
        if (product) {
          product.views++
        }
      }
      
      const query = tab ? { tab } : {}
      this.$router.push({
        path: `/product/${productId}`,
        query
      })
    },
    showToast(message, type = 'success') {
      if (message === 'Unlike successful') {
        message = 'Unlike successful';
      } else if (message === 'Like successful') {
        message = 'Like successful';
      }
      
      ElMessage({
        message,
        type,
        duration: 2000
      });
    },
    // 格式化价格
    formatPrice(price) {
      // 转换货币并保留2位小数
      return (price * this.exchangeRate).toFixed(2);
    },
    
    // 货币变更时重置价格范围
    updateCurrency(currencyInfo) {
      // 如果有当前价格范围且货币发生变化
      if (this.priceRange.min !== null && this.priceRange.max !== null && this.currencySymbol !== currencyInfo.symbol) {
        // 转换价格范围到新货币
        this.priceRange.min = Math.round((this.priceRange.min / this.exchangeRate) * currencyInfo.rate * 100) / 100;
        this.priceRange.max = Math.round((this.priceRange.max / this.exchangeRate) * currencyInfo.rate * 100) / 100;
      }
      
      this.currency = currencyInfo.currency;
      this.currencySymbol = currencyInfo.symbol;
      this.exchangeRate = currencyInfo.rate;
    },
    
    // 初始化货币信息
    initCurrency() {
      // 尝试从provide/inject获取货币信息
      try {
        const getCurrencyInfo = this.$root.$parent && this.$root.$parent.getCurrencyInfo;
        if (getCurrencyInfo) {
          const currencyInfo = getCurrencyInfo();
          this.updateCurrency(currencyInfo);
        } else {
          // 回退到本地存储
          const savedCurrency = localStorage.getItem('preferredCurrency');
          if (savedCurrency) {
            const currencySymbols = {
              'USD': '$',
              'CNY': '¥',
              'EUR': '€',
              'GBP': '£',
              'JPY': '¥'
            };
            const exchangeRates = {
              'USD': 1,
              'CNY': 7.2,
              'EUR': 0.93,
              'GBP': 0.79,
              'JPY': 150.5
            };
            
            if (currencySymbols[savedCurrency]) {
              this.currency = savedCurrency;
              this.currencySymbol = currencySymbols[savedCurrency];
              this.exchangeRate = exchangeRates[savedCurrency];
            }
          }
        }
      } catch (e) {
        console.error('Failed to get currency info:', e);
      }
    },
    // 添加搜索按钮处理方法
    handleSearch() {
      // 如果已有请求在进行中，忽略
      if (this.isRequestPending) {
        console.log('请求正在处理中，忽略搜索');
        return;
      }
      
      console.log('执行搜索，关键词:', this.searchQuery);
      
      // 清空所有查找商品的参数，以便纯搜索
      this.selectedCategories = [];
      this.selectedBrands = [];
      this.priceRange = {
        min: null,
        max: null
      };
      this.sortOption = null;
      this.priceSortDirection = null;
      
      // 重置API分页状态
      this.apiCurrentPage = 1;
      this.hasMorePages = true;
      
      // 设置请求标志
      this.isRequestPending = true;
      
      // 应用筛选
      this.applyFilters().finally(() => {
        this.isRequestPending = false;
      });
    },
    // 添加新方法来加载初始产品
    loadInitialProducts() {
      console.log('=== loadInitialProducts开始执行 ===');
      console.log('当前filteredProducts长度:', this.filteredProducts.length);
      console.log('当前排序状态:', this.sortOption, this.priceSortDirection);
      
      if (this.filteredProducts.length > 0) {
        console.log('filteredProducts前5个商品:', this.filteredProducts.slice(0, 5).map((p, i) => 
          `${i+1}. ${p.name} - 价格: $${p.price}, 浏览: ${p.views}`
        ));
      }
      
      // 重置显示的产品
      this.displayedProducts = [];
      
      // 直接加载第一页产品，不需要延迟和转圈效果
      const startIndex = 0;
      const endIndex = this.itemsPerPage;
      const firstBatch = this.filteredProducts.slice(startIndex, endIndex);
      
      this.displayedProducts = firstBatch;
      this.currentPage = 2; // 设置为第2页，因为第1页已经加载
      
      // 判断是否还有更多产品
      this.noMoreProducts = this.displayedProducts.length >= this.filteredProducts.length;
      
      console.log(`初始加载产品: ${this.displayedProducts.length}/${this.filteredProducts.length}`);
      console.log('displayedProducts前4个产品详情:', this.displayedProducts.slice(0, 4).map((p, i) => 
        `${i+1}. ${p.name} - 浏览: ${p.views}, 价格: $${p.price}`
      ));
      console.log('=== loadInitialProducts执行完毕 ===');
    },
    // 添加新方法用于加载更多产品
    loadMoreProducts() {
      if (this.isLoadingMore || this.noMoreProducts) return;
      
      // 立即显示加载动画
      this.isLoadingMore = true;
      
      // 计算当前应该加载的产品范围
      const startIndex = (this.currentPage - 1) * this.itemsPerPage;
      const endIndex = startIndex + this.itemsPerPage;
      
      console.log(`加载更多产品: 第${this.currentPage}页, 索引范围 ${startIndex}-${endIndex}`);
      
      // 从过滤后的产品中获取下一批
      const nextBatch = this.filteredProducts.slice(startIndex, endIndex);
      
      // 检查是否需要从后端获取更多数据
      const remainingProducts = this.filteredProducts.length - endIndex;
      const shouldFetchMore = remainingProducts <= this.itemsPerPage && this.hasMorePages;
      
      if (shouldFetchMore) {
        console.log('需要从后端获取更多数据');
        // 异步获取更多数据
        this.fetchProducts(true).then(() => {
          // 获取完成后，重新计算要显示的产品（因为数据可能已经重新排序）
          const newNextBatch = this.filteredProducts.slice(startIndex, endIndex);
          this.finishLoadingMore(newNextBatch);
        }).catch(error => {
          console.error('获取更多数据失败:', error);
          this.finishLoadingMore(nextBatch);
        });
      } else {
        // 直接使用现有数据
        this.finishLoadingMore(nextBatch);
      }
    },
    
    // 完成加载更多的逻辑
    finishLoadingMore(nextBatch) {
      // 如果没有更多产品，标记为结束
      if (nextBatch.length === 0) {
        this.noMoreProducts = true;
        this.isLoadingMore = false;
        return;
      }
      
      // 延迟一点时间后再显示新商品，提供更好的用户体验
      setTimeout(() => {
        try {
          // 将新产品添加到显示列表
          this.displayedProducts = [...this.displayedProducts, ...nextBatch];
          
          // 更新页码
          this.currentPage++;
          
          // 判断是否还有更多产品（考虑后端分页）
          const hasMoreInCurrentData = this.displayedProducts.length < this.filteredProducts.length;
          this.noMoreProducts = !hasMoreInCurrentData && !this.hasMorePages;
          
          console.log(`已加载产品: ${this.displayedProducts.length}/${this.filteredProducts.length}, 后端还有更多: ${this.hasMorePages}`);
        } catch (error) {
          console.error('加载更多产品时出错:', error);
        } finally {
          // 关闭加载动画
          this.isLoadingMore = false;
        }
      }, 300); // 延迟300ms提供更好的用户体验
    },
    // 添加初始化滚动观察器的方法
    initScrollObserver() {
      // 如果已经初始化过，先移除
      if (this.scrollObserver) {
        this.scrollObserver.disconnect();
      }
      
      // 创建一个占位元素用于观察
      const loadMoreTrigger = document.createElement('div');
      loadMoreTrigger.className = 'load-more-trigger';
      document.querySelector('.products').appendChild(loadMoreTrigger);
      
      // 创建观察器
      this.scrollObserver = new IntersectionObserver((entries) => {
        // 如果触发元素进入视口，加载更多产品
        if (entries[0].isIntersecting && !this.isLoadingMore && !this.noMoreProducts) {
          this.loadMoreProducts();
        }
      }, {
        root: null, // 使用视口作为根元素
        rootMargin: '0px 0px 200px 0px', // 在视口底部200px处开始触发
        threshold: 0.1 // 当目标元素有10%进入视口时触发
      });
      
      // 开始观察
      this.scrollObserver.observe(loadMoreTrigger);
    },
    sortByViews() {
      console.log('sortByViews被调用，当前状态:', this.sortOption);
      
      if (this.sortOption === 'views') {
        // 如果当前已经是按浏览量排序，则取消排序
        this.sortOption = null;
        this.priceSortDirection = null;
        this.isPriceDropdownOpen = false;
        
        // 重置产品列表到原始顺序
        this.filteredProducts = [...this.products];
        console.log('取消浏览量排序，恢复原始顺序');
      } else {
        // 否则按浏览量排序
        this.sortOption = 'views';
        this.priceSortDirection = null;
        this.isPriceDropdownOpen = false;
        
        // 按浏览量排序
        this.filteredProducts.sort((a, b) => b.views - a.views);
        console.log('开始按浏览量排序，排序后前3个商品:', this.filteredProducts.slice(0, 3).map(p => `${p.name}: ${p.views}浏览`));
      }
      
      // 重置分页并重新加载产品
      this.currentPage = 1;
      this.loadInitialProducts();
      
      // 强制更新视图
      this.$forceUpdate();
      console.log('sortByViews执行完毕，强制更新视图');
    },
    
    sortByPrice(direction) {
      console.log('sortByPrice被调用，方向:', direction);
      
      this.sortOption = 'price';
      this.priceSortDirection = direction;
      this.isPriceDropdownOpen = false;
      
      // Sort products by price
      this.filteredProducts.sort((a, b) => {
        if (direction === 'asc') {
          return a.price - b.price;
        } else {
          return b.price - a.price;
        }
      });
      
      console.log(`按价格${direction === 'asc' ? '升序' : '降序'}排序，排序后前3个商品:`, 
        this.filteredProducts.slice(0, 3).map(p => `${p.name}: $${p.price}`));
      
      // Reset pagination and reload products
      this.currentPage = 1;
      this.loadInitialProducts();
      
      // 强制更新视图
      this.$forceUpdate();
      console.log('sortByPrice执行完毕，强制更新视图');
    },
    
    togglePriceSort() {
      // 如果已经是价格排序，则检查当前方向
      if (this.sortOption === 'price') {
        // 如果是从高到低，第三次点击时取消排序
        if (this.priceSortDirection === 'desc') {
          // 取消价格排序，恢复默认排序
          this.sortOption = null;
          this.priceSortDirection = null;
          
          // 重新加载产品列表（恢复到初始顺序）
          this.applyFilters();
          this.currentPage = 1;
          this.loadInitialProducts();
        } else {
          // 从低到高切换到高到低（第二次点击）
          this.sortByPrice('desc');
        }
      } else {
        // 第一次点击，默认为价格从低到高排序
        this.sortByPrice('asc');
      }
    },
    
    togglePriceRangeDropdown() {
      this.isPriceRangeDropdownOpen = !this.isPriceRangeDropdownOpen;
      if (this.isPriceRangeDropdownOpen) {
        this.isPriceDropdownOpen = false;
      }
    },
    
    // Close dropdown when clicking outside
    handleClickOutside(event) {
      const dropdown = document.querySelector('.price-sort-dropdown');
      const priceRangeDropdown = document.querySelector('.price-range-dropdown');
      if (dropdown && !dropdown.contains(event.target)) {
        this.isPriceDropdownOpen = false;
      }
      if (priceRangeDropdown && !priceRangeDropdown.contains(event.target)) {
        this.isPriceRangeDropdownOpen = false;
      }
    },
    /**
     * 获取轮播图数据
     */
    async fetchBanners() {
      try {
        this.isLoadingBanners = true;
        const response = await productsApi.getBanners();
        
        if (response.code === 200 && response.data) {
          this.banners = response.data.map(banner => ({
            id: banner.id,
            title: banner.title,
            description: banner.description,
            imageUrl: banner.imageUrl,
            linkUrl: banner.linkUrl,
            type: banner.type
          }));
          
          if (this.banners.length > 0) {
            this.startBannerAutoplay();
          }
        } else {
          console.error('Error getting banners:', response);
          this.banners = [];
        }
      } catch (error) {
        console.error('Error fetching banners:', error);
        this.banners = [];
      } finally {
        this.isLoadingBanners = false;
      }
    },
    
    /**
     * 自动轮播
     */
    startBannerAutoplay() {
      // 清除之前的定时器
      if (this.bannerInterval) {
        clearInterval(this.bannerInterval);
      }
      
      // 设置新的定时器，每5秒切换一次
      this.bannerInterval = setInterval(() => {
        this.nextBanner();
      }, 5000);
    },
    
    /**
     * 前往上一个轮播图
     */
    prevBanner() {
      if (this.currentBannerIndex === 0) {
        this.currentBannerIndex = this.banners.length - 1;
      } else {
        this.currentBannerIndex--;
      }
      this.restartBannerAutoplay();
    },
    
    /**
     * 前往下一个轮播图
     */
    nextBanner() {
      if (this.currentBannerIndex === this.banners.length - 1) {
        this.currentBannerIndex = 0;
      } else {
        this.currentBannerIndex++;
      }
      this.restartBannerAutoplay();
    },
    
    /**
     * 前往指定轮播图
     */
    goToBanner(index) {
      this.currentBannerIndex = index;
      this.restartBannerAutoplay();
    },
    
    /**
     * 重新开始自动轮播
     */
    restartBannerAutoplay() {
      this.startBannerAutoplay();
    },
    
    /**
     * 处理轮播图点击
     */
    handleBannerClick(banner) {
      if (banner.linkUrl) {
        // 如果是内部链接，使用router导航
        if (banner.linkUrl.startsWith('/')) {
          this.$router.push(banner.linkUrl);
        } else {
          // 外部链接，在新窗口打开
          window.open(banner.linkUrl, '_blank');
        }
      }
    },
    /**
     * 切换显示更多品牌 - 现在始终显示所有品牌
     */
    toggleShowAllBrands() {
      // 函数保留但不再需要切换显示，始终显示所有品牌
        this.showAllBrands = true;
    },
    // 处理价格区间筛选
    filterProductsByPriceRange() {
      // 检查价格是否有效
      if (!this.priceRange.min || !this.priceRange.max) {
        ElMessage({
          message: 'Please enter a complete price range',
          type: 'warning',
          duration: 2000,
          grouping: true,
          showClose: true
        });
        return false;
      }
      
      if (this.priceRange.min > this.priceRange.max) {
        ElMessage({
          message: 'Minimum price cannot be higher than maximum price',
          type: 'error',
          duration: 2000,
          grouping: true,
          showClose: true
        });
        return false;
      }
      
      const minPrice = this.priceRange.min / this.exchangeRate; // 转换为基础货币
      const maxPrice = this.priceRange.max / this.exchangeRate;
      
      console.log('Applying price filter:', this.priceRange.min, '-', this.priceRange.max);
      
      // 首次价格筛选时保存原始列表
      if (!this.cachedProducts) {
        this.cachedProducts = [...this.filteredProducts];
      }
      
      // 对当前筛选结果应用价格过滤
      this.filteredProducts = this.cachedProducts.filter(product => {
        const productPrice = product.price || 0;
        return productPrice >= minPrice && productPrice <= maxPrice;
      });
      
      console.log(`Products after price filter: ${this.filteredProducts.length}/${this.cachedProducts.length}`);
      
      // 重置分页并重新加载产品
      this.currentPage = 1;
      this.loadInitialProducts();
      this.noMoreProducts = this.displayedProducts.length >= this.filteredProducts.length;
      
      // 如果没有匹配的商品，显示提示
      if (this.filteredProducts.length === 0) {
        ElMessage({
          message: 'No products match the selected price range',
          type: 'info',
          duration: 2000,
          grouping: true,
          showClose: true
        });
      }
      
      return true;
    },
    /**
     * 处理触摸开始事件
     */
    handleTouchStart(event) {
      this.touchStartX = event.touches[0].clientX;
      
      // 暂停自动播放
      if (this.bannerInterval) {
        clearInterval(this.bannerInterval);
      }
    },
    /**
     * 处理触摸移动事件
     */
    handleTouchMove(event) {
      this.touchEndX = event.touches[0].clientX;
    },
    /**
     * 处理触摸结束事件
     */
    handleTouchEnd() {
      const swipeDistance = this.touchEndX - this.touchStartX;
      
      // 判断滑动方向和距离
      if (Math.abs(swipeDistance) >= this.minSwipeDistance) {
        if (swipeDistance > 0) {
          // 向右滑动，切换到上一张
          this.prevBanner();
        } else {
          // 向左滑动，切换到下一张
          this.nextBanner();
        }
      }
      
      // 重新开始自动播放
      this.restartBannerAutoplay();
    },
    /**
     * 切换显示所有分类
     */
    toggleShowAllCategories() {
      this.showAllCategories = !this.showAllCategories;
      // 使用nextTick确保DOM更新后再调整布局
      this.$nextTick(() => {
        this.adjustLayoutAfterToggle();
      });
    },

    /**
     * 调整Load more后的布局，防止重叠
     */
    adjustLayoutAfterToggle() {
      // 仅在移动设备上执行
      if (window.innerWidth <= 768) {
        const brandsSection = document.querySelector('.filter-group.full-width.fix-mobile-overlap');
        if (brandsSection) {
          if (this.showAllCategories && this.categories.length > this.visibleCategoriesCount) {
            // 根据分类数量动态调整间距
            const extraCategories = this.categories.length - this.visibleCategoriesCount;
            const extraMargin = Math.min(extraCategories * 1, 10); // 最多增加10rem
            brandsSection.style.marginTop = `${8 + extraMargin}rem`;
          } else {
            // 恢复默认间距
            brandsSection.style.marginTop = '8rem';
          }
        }
      }
    },

    /**
     * 处理窗口尺寸变化事件
     */
    handleResize() {
      // 防抖处理
      if (this.resizeTimer) {
        clearTimeout(this.resizeTimer);
      }
      
      this.resizeTimer = setTimeout(() => {
        this.adjustLayoutAfterToggle();
      }, 200);
    },
    // 添加打字效果方法
    typePlaceholder() {
      const currentText = this.placeholderTexts[this.currentPlaceholderIndex];
      
      // 如果正在输入中
      if (this.isTyping) {
        if (this.typingPlaceholder.length < currentText.length) {
          // 继续输入
          this.typingPlaceholder = currentText.substring(0, this.typingPlaceholder.length + 1);
          this.typingTimeout = setTimeout(this.typePlaceholder, this.typingSpeed);
        } else {
          // 输入完成，暂停一会儿
          this.isTyping = false;
          this.typingTimeout = setTimeout(this.typePlaceholder, this.pauseDelay);
        }
      } else {
        // 删除阶段
        if (this.typingPlaceholder.length > 0) {
          // 继续删除
          this.typingPlaceholder = this.typingPlaceholder.substring(0, this.typingPlaceholder.length - 1);
          this.typingTimeout = setTimeout(this.typePlaceholder, this.deletingSpeed);
        } else {
          // 删除完成，切换到下一个文本
          this.isTyping = true;
          this.currentPlaceholderIndex = (this.currentPlaceholderIndex + 1) % this.placeholderTexts.length;
          this.typingTimeout = setTimeout(this.typePlaceholder, this.typingSpeed);
        }
      }
    },
    
    // 更新显示的产品列表（用于加载更多）
    updateDisplayedProducts() {
      const startIndex = this.displayedProducts.length;
      const endIndex = Math.min(startIndex + this.itemsPerPage, this.filteredProducts.length);
      const newProducts = this.filteredProducts.slice(startIndex, endIndex);
      this.displayedProducts = [...this.displayedProducts, ...newProducts];
    },
    
    // 根据当前排序状态应用排序
    applySortingIfNeeded() {
      console.log('🎯 applySortingIfNeeded被调用，当前排序状态:', this.sortOption, this.priceSortDirection);
      console.log('🎯 filteredProducts长度:', this.filteredProducts.length);
      
      if (this.sortOption === 'views') {
        // 按浏览量排序
        this.filteredProducts.sort((a, b) => b.views - a.views);
        console.log('✅ 应用浏览量排序完成，排序后的前3个商品:', this.filteredProducts.slice(0, 3).map(p => `${p.name}: ${p.views}浏览`));
      } else if (this.sortOption === 'price' && this.priceSortDirection) {
        // 按价格排序
        if (this.priceSortDirection === 'asc') {
          this.filteredProducts.sort((a, b) => a.price - b.price);
          console.log('✅ 应用价格升序排序完成，排序后的前3个商品:', this.filteredProducts.slice(0, 3).map(p => `${p.name}: $${p.price}`));
        } else {
          this.filteredProducts.sort((a, b) => b.price - a.price);
          console.log('✅ 应用价格降序排序完成，排序后的前3个商品:', this.filteredProducts.slice(0, 3).map(p => `${p.name}: $${p.price}`));
        }
      } else {
        console.log('❌ 无需排序或排序状态为空');
      }
    },
    
    // 处理主题变化
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode !== 'undefined') {
        // 获取当前主题
        const isDarkMode = data.isDarkMode;
        
        // 手动应用主题到关键元素
        const productsElement = document.querySelector('.products');
        if (productsElement) {
          productsElement.style.background = isDarkMode ? 
            'linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%)' : 
            'linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%)';
        }
        
        // 应用到产品卡片
        const productCards = document.querySelectorAll('.product-card');
        productCards.forEach(card => {
          card.style.background = isDarkMode ? 'rgba(30, 9, 64, 0.8)' : '#ffffff';
          card.style.color = isDarkMode ? '#ffffff' : '#333333';
          card.style.borderColor = isDarkMode ? 'rgba(147, 51, 234, 0.2)' : 'rgba(76, 175, 80, 0.15)';
        });
        
        // 应用到筛选区域
        const filterSection = document.querySelector('.filters-section');
        if (filterSection) {
          filterSection.style.background = isDarkMode ? 'rgba(30, 9, 64, 0.8)' : '#ffffff';
          filterSection.style.color = isDarkMode ? '#ffffff' : '#333333';
        }
      }
    },
  },
  watch: {
    // 移除searchQuery的监听，改为只在点击搜索按钮时搜索
  },
  async created() {
    console.log('Component created');
    try {
      // 检查URL参数中是否有categoryId
      if (this.$route.query.categoryId) {
        const categoryId = parseInt(this.$route.query.categoryId);
        if (!isNaN(categoryId)) {
          this.selectedCategories = [categoryId];
          console.log('从URL参数设置分类ID:', categoryId);
          
          // 设置品牌加载状态为true，避免在加载过程中显示全部品牌
          this.isLoadingBrands = true;
        }
      }
      
      // 获取分类数据
      await this.fetchCategories();
      
      // 根据是否有选中的分类决定如何获取品牌
      if (this.selectedCategories.length > 0) {
        // 获取特定分类下的品牌
        try {
          const categoryId = this.selectedCategories[0];
          const response = await productsApi.getBrandsByCategory(categoryId);
          if (response.code === 200 && response.data) {
            this.brands = response.data.map(brand => ({
              id: brand.brandId || brand.id,
              name: brand.name,
              logoUrl: brand.logoUrl
            }));
          } else {
            console.error('获取品牌列表失败:', response);
            this.brands = [];
          }
        } catch (error) {
          console.error('获取分类品牌时出错:', error);
          this.brands = [];
        } finally {
          // 品牌加载完成
          this.isLoadingBrands = false;
        }
      } else {
        // 获取所有品牌数据
        await this.fetchBrands();
      }
      
      // 获取轮播图数据
      await this.fetchBanners();
      
      // 获取产品数据
      await this.fetchProducts();
      
      // 监听货币变化事件
      emitter.on('currency-changed', this.updateCurrency);
      
      // 初始化货币信息
      this.initCurrency();
      
      // 监听主题变化
      emitter.on('theme-changed', this.handleThemeChange);
      emitter.on('apply-theme-to-page', this.handleThemeChange);
    } catch (error) {
      console.error('Error during initialization:', error);
      // 确保品牌加载状态重置
      this.isLoadingBrands = false;
    }
  },
  beforeUnmount() {
    // 移除事件监听 - 使用 mitt 替代 Vue 2 事件总线
    emitter.off('currency-changed', this.updateCurrency);
    
    // 移除滚动观察器
    if (this.scrollObserver) {
      this.scrollObserver.disconnect();
      this.scrollObserver = null;
    }
    
    // 移除创建的加载触发元素
    const trigger = document.querySelector('.load-more-trigger');
    if (trigger) {
      trigger.remove();
    }
    
    // Remove click outside listener
    document.removeEventListener('click', this.handleClickOutside);
    
    // 移除窗口尺寸变化监听
    window.removeEventListener('resize', this.handleResize);
    
    // 清除轮播图定时器
    if (this.bannerInterval) {
      clearInterval(this.bannerInterval);
      this.bannerInterval = null;
    }
    // 清除打字效果定时器
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
    
    // 移除主题变化监听
    emitter.off('theme-changed', this.handleThemeChange);
    emitter.off('apply-theme-to-page', this.handleThemeChange);
  },
  mounted() {
    console.log('Component mounted');
    
    // 初始化滚动观察器
    this.$nextTick(() => {
      this.initScrollObserver();
      // 开始打字效果
      this.typePlaceholder();
    });
    
    // Add click outside listener
    document.addEventListener('click', this.handleClickOutside);
    
    // 添加窗口尺寸变化监听，确保布局正确
    window.addEventListener('resize', this.handleResize);
    
    // 初始调用一次布局调整
    this.adjustLayoutAfterToggle();
  }
}
</script>

<style scoped>
/* 新的分类标题按钮样式 */
.category-title-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin: 20px 0;
}

.category-button-title {
  background-color: #3f51b5;  /* 紫色背景 */
  color: white;
  font-weight: 600;
  padding: 8px 40px;
  border-radius: 20px;  /* 圆角效果 */
  display: inline-block;
  letter-spacing: 1px;
  text-transform: uppercase;
  font-size: 1rem;
  box-shadow: 0 2px 10px rgba(63, 81, 181, 0.3);
  position: relative;
  overflow: hidden;
}

/* 添加微光效果 */
.category-button-title::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.category-button-title:hover::after {
  left: 100%;
}

.products {
  padding: 2rem 5rem;
  min-height: 100vh;
  color: #e0e0e0;
  /* 移除背景色和渐变设置，让StarryBackground生效 */
  background: transparent;
  background-image: none;
  position: relative;
  z-index: 1; /* 确保内容在星空背景之上 */
}

/* 设置内容最大宽度并居中 */
.search-section, .filters-section, .products-grid {
  max-width: 1400px; /* 限制最大宽度 */
  margin-left: auto;
  margin-right: auto;
}

/* Search Section */
.search-section {
  margin-bottom: 2.5rem;
  position: relative;
  z-index: 1;
}

.search-container {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  gap: 2rem;
  position: relative;
}

.search-wrapper {
  flex: 1;
  display: flex;
  position: relative;
  z-index: 15;
  background: rgba(30, 30, 40, 0.7);
  border-radius: 50px;
  padding: 0.5rem;
  box-shadow: 0 0 30px rgba(120, 70, 200, 0.4), 
              inset 0 0 10px rgba(0, 0, 0, 0.3),
              0 5px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(120, 70, 200, 0.5);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

/* 添加发光边缘效果 */
.search-wrapper::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, 
    rgba(138, 43, 226, 0) 0%, 
    rgba(138, 43, 226, 0.8) 50%, 
    rgba(138, 43, 226, 0) 100%
  );
  border-radius: 52px;
  z-index: -1;
  opacity: 0.6;
  animation: borderGlow 3s infinite alternate;
  filter: blur(3px);
}

@keyframes borderGlow {
  0% {
    opacity: 0.4;
    transform: rotate(0deg);
  }
  100% {
    opacity: 0.8;
    transform: rotate(360deg);
  }
}

.search-wrapper:hover, .search-wrapper:focus-within {
  box-shadow: 0 0 40px rgba(138, 43, 226, 0.6), 
              inset 0 0 15px rgba(0, 0, 0, 0.2),
              0 8px 20px rgba(0, 0, 0, 0.3);
  transform: translateY(-2px);
  border-color: rgba(138, 43, 226, 0.7);
}

/* 搜索框聚焦时的发光效果增强 */
.search-wrapper:focus-within::before {
  opacity: 1;
  animation: borderGlow 1.5s infinite alternate;
  filter: blur(4px);
}

.search-icon-left {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
  color: rgba(195, 163, 255, 0.7);
  font-size: 1.2rem;
  animation: pulseIcon 2s infinite alternate;
}

@keyframes pulseIcon {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.1);
    opacity: 1;
  }
}

.search-input {
  flex: 1;
  padding: 1rem 0.5rem;
  border: none;
  background: transparent;
  color: #e0e0e0;
  font-size: 1.1rem;
  width: 100% !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.search-input::placeholder {
  color: rgba(195, 163, 255, 0.6);
  opacity: 0.8;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
}

.search-input:focus::placeholder {
  opacity: 0.5;
  transform: translateX(5px);
}

.search-button {
  padding: 0.8rem 1.5rem;
  border: none;
  border-radius: 25px;
  background: linear-gradient(135deg, #8a2be2, #4b0082);
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
  display: flex !important;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

/* 搜索按钮发光效果 */
.search-button::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #8a2be2, #4b0082, #9b4dff, #5b0096);
  border-radius: 27px;
  z-index: -1;
  filter: blur(8px);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.search-button:hover::after {
  opacity: 1;
}

.search-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: all 0.6s ease;
}

.search-button:hover {
  background: linear-gradient(135deg, #9b4dff, #5b0096);
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(120, 70, 200, 0.5);
}

.search-button:hover::before {
  left: 100%;
}

.search-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.search-button-text {
  font-size: 0.95rem;
}

.search-button i {
  transition: transform 0.3s ease;
}

.search-button:hover i {
  transform: translateX(3px);
}

/* 添加粒子效果背景 */
.search-container::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-image: 
    radial-gradient(circle at 20% 35%, rgba(138, 43, 226, 0.15) 0%, transparent 20%),
    radial-gradient(circle at 75% 44%, rgba(75, 0, 130, 0.15) 0%, transparent 20%),
    radial-gradient(circle at 46% 52%, rgba(138, 43, 226, 0.12) 0%, transparent 30%),
    radial-gradient(circle at 11% 80%, rgba(75, 0, 130, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 80% 15%, rgba(138, 43, 226, 0.1) 0%, transparent 30%);
  opacity: 0.8;
  z-index: -1;
  filter: blur(3px);
  animation: particleShift 15s infinite alternate ease-in-out;
}

@keyframes particleShift {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 100% 100%;
  }
}

/* 移动端搜索框样式 */
@media (max-width: 768px) {
  .search-section {
    margin: 0;
    padding: 0.8rem 1rem;
    position: sticky;
    top: 60px;
    z-index: 20;
    width: 100%;
    left: 0;
    right: 0;
    background: rgba(20, 20, 30, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(120, 70, 200, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .search-container {
    flex-direction: row;
    gap: 0.5rem;
    width: 100%;
    align-items: center;
    max-width: 100%;
  }

  .search-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0.3rem;
  }
  
  /* 调整移动端的发光效果 */
  .search-wrapper::before {
    animation: borderGlow 4s infinite alternate;
    opacity: 0.4;
  }

  .search-icon-left {
    padding: 0 0.5rem;
    font-size: 1rem;
    animation: none;
  }

  .search-input {
    padding: 0.7rem 0.5rem;
    font-size: 0.95rem;
    height: 40px;
    flex: 1;
    min-width: 0;
  }
  
  .search-button {
    min-width: 40px;
    height: 40px;
    border-radius: 20px;
    padding: 0 1rem;
    margin-left: 4px;
    flex-shrink: 0;
  }

  .search-button-text {
    display: none;
  }
  
  .typing-placeholder {
    left: 3.2rem;
    font-size: 0.95rem;
  }
  
  /* 较小屏幕的进一步优化 */
  @media (max-width: 375px) {
    .search-input {
      font-size: 0.9rem;
      padding: 0.6rem 0.5rem;
    }
    
    .search-button {
      min-width: 36px;
      width: 36px;
      height: 36px;
    }
  }
}

.stat-item.collected {
  color: #ff71ce !important;
}

.stat-item.collected i {
  color: #ff71ce !important;
}

@media (max-width: 768px) {
  .search-container {
    flex-direction: column;
    gap: 1rem;
  }

  .company-logo {
    height: 40px;
    margin: 0 auto;
  }

  .search-wrapper {
    width: 100%;
  }

  .search-input {
    padding: 0.8rem;
    font-size: 1rem;
  }

  .filters-section {
    padding: 1rem;
    margin-bottom: 1rem;
  }

  .filter-container {
    gap: 1rem;
  }

  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    width: 100%;
  }

  .product-card {
    border-radius: 12px; /* 调整移动端圆角 */
  }

  .store-logo {
    padding: 5px;
  }

  .store-logo img {
    width: 20px;
    height: 20px;
    margin-right: 5px;
  }

  .store-name {
    font-size: 0.8rem;
  }

  .product-image {
    height: 160px; /* 从140px增加到160px */
    padding: 3px 2px; /* 小屏幕上下3px，左右2px */
  }

  .product-info {
    padding: 5px 6px; /* 小屏幕进一步压缩信息区域 */
    background: rgba(35, 35, 40, 0.9);
    flex: 1;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
  }

  .product-name {
    color: #e0e0e0;
    font-size: 0.8rem; /* 小屏幕字体进一步缩小 */
    margin-bottom: 3px; /* 更小的间距 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    max-width: 100%;
    line-height: 1.1; /* 紧凑行高 */
    transition: all 0.3s ease;
  }

  .price-container {
    margin-bottom: 3px; /* 更小的间距 */
  }

  .current-price {
    font-size: 0.85rem; /* 小屏幕价格字体 */
  }

  .original-price {
    font-size: 0.65rem; /* 小屏幕原价字体 */
  }

  .product-stats {
    font-size: 0.65rem; /* 小屏幕统计字体 */
    padding-top: 3px; /* 更小的顶部间距 */
  }

  .stat-item i {
    font-size: 0.75rem; /* 更小屏幕图标大小 */
  }

  .filter-group h3 {
    font-size: 1rem;
  }
  
  /* 调整移动端品牌区域与产品分类的间距 */
  .filter-group[v-if="selectedCategories.length > 0"] {
    margin-top: -2.8rem !important;
    padding-top: 0 !important;
  }
  
  .brand-buttons {
    margin-top: 0 !important;
    margin-bottom: 0.5rem !important;
  }
  
  .brand-more-container {
    margin-top: 0.5rem !important;
  }

  .pagination .page-number {
    width: 30px;
    height: 30px;
    font-size: 0.9rem;
  }

  .pagination .page-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  /* 增强移动端可点击区域 */
  .stat-item {
    padding: 8px;
  }
  
  .product-card {
    border-radius: 10px; /* 调整超小屏幕圆角 */
  }
}

/* 添加触摸优化 */
@media (hover: none) {
  /* 确保在触摸设备上产品卡片始终是两列 */
  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr) !important;
    width: 100%;
  }
  
  .product-card {
    /* 在触摸设备上始终显示清晰 */
    transform: none !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
    background: linear-gradient(135deg, #242428, #1a1a20) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(80, 80, 100, 0.3) !important;
    width: 100%;
  }

  .product-stats .stat-item {
    /* 增强点击区域 */
    min-width: 35px;
  }
}

/* 增加移动端底部空间，避免底部菜单遮挡 */
@media (max-width: 768px) {
  .pagination {
    margin-bottom: 2rem;
  }
  
  /* 确保所有移动设备上都保持两列产品布局 */
  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.8rem;
    width: 100%;
  }
}

/* Filters Section */
.filters-section {
  margin-bottom: 1.5rem;
  padding: 1rem 0;
  /* 移除以下属性:
  background: rgba(30, 30, 35, 0.9);
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(80, 80, 100, 0.3);
  */
}

.filter-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.filter-group {
  width: 100%;
  margin-bottom: 2rem; /* 增加底部间距，防止分类重叠 */
}

.filter-group h3 {
  color: #c3a3ff; /* Purple accent */
  margin-bottom: 1rem;
  font-size: 1.2rem;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
}

/* 覆盖默认h3样式，优先使用section-title样式 */
.filter-group h3.section-title {
  color: white;
  margin-bottom: 0;
  text-shadow: none;
}

.category-header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1.5rem;
  position: relative;
}

.section-title {
  color: white;
  text-align: center;
  font-size: 1.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin: 0;
  padding: 0.5rem 2rem;
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border-radius: 50px;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.5);
  position: relative;
  z-index: -1;
  border: 2px solid rgba(195, 163, 255, 0.5);
  animation: titleGlow 3s infinite alternate;
}

@keyframes titleGlow {
  0% {
    box-shadow: 0 4px 15px rgba(120, 70, 200, 0.5);
    border-color: rgba(195, 163, 255, 0.5);
  }
  100% {
    box-shadow: 0 4px 25px rgba(120, 70, 200, 0.8);
    border-color: rgba(255, 255, 255, 0.7);
  }
}

.section-title::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(135deg, rgba(120, 70, 200, 0.7), rgba(187, 134, 252, 0.7));
  border-radius: 50px;
  z-index: -1;
  opacity: 0.5;
  filter: blur(8px);
}

.expand-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 1rem;
  width: 100%;
  max-width: 200px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #323232, #252525);
  color: #c3a3ff;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.expand-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(70, 20, 100, 0.3);
  background: linear-gradient(135deg, #404040, #303030);
}

.expand-icon {
  transition: transform 0.3s ease;
  font-size: 1rem;
  width: 1rem;
  height: 1rem;
  color: #c3a3ff;
}

.expand-icon.is-expand {
  transform: rotate(180deg);
}

.category-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 1rem;
  overflow: visible; /* 修改为visible，确保按钮可见 */
  transition: all 0.3s ease;
  padding-top: 2px;
  justify-content: center; /* Center the category buttons horizontally */
  position: relative; /* 添加定位上下文 */
  z-index: 2; /* 确保在合适的层级 */
}

.category-buttons.expanded {
  max-height: none;
  overflow: visible;
  padding-bottom: 10px;
}

.category-btn {
  padding: 0.6rem 1.2rem;
  border: 2px solid #c3a3ff;
  border-radius: 50px;
  background: transparent;
  color: #c3a3ff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  z-index: 8; /* 添加z-index确保按钮在正确层级 */
  margin: 0.3rem 0.2rem; /* 增加按钮间距 */
}

.category-btn:hover {
  background: rgba(120, 70, 200, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(120, 70, 200, 0.3);
}

.category-btn.active {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border-color: #c3a3ff;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(120, 70, 200, 0.5), 0 0 20px rgba(120, 70, 200, 0.3);
  transform: translateY(-2px);
}

.price-range {
  width: 100%;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 1rem;
  max-width: 500px;
}

.price-input {
  flex: 1;
  padding: 0.8rem;
  border: none;
  border-radius: 5px;
  background: rgba(40, 40, 45, 0.8);
  color: #e0e0e0;
  font-size: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1), inset 0 1px 3px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

.price-separator {
  color: #c3a3ff;
  font-size: 1.2rem;
  font-weight: bold;
}

/* 增强Clear All Filters按钮样式 */
.clear-filters {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #ffffff;
  border: 2px solid rgba(180, 120, 255, 0.5);
  padding: 1rem 2rem;
  border-radius: 50px;
  cursor: pointer;
  width: 100%;
  max-width: 500px;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 1px;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(120, 70, 200, 0.3),
    inset 0 0 10px rgba(120, 70, 200, 0.2);
  margin: 2rem auto 0;
  display: block;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
}

/* 按钮悬停效果 */
.clear-filters:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  transform: translateY(-3px) scale(1.02);
  box-shadow: 
    0 8px 20px rgba(0, 0, 0, 0.4),
    0 0 30px rgba(120, 70, 200, 0.5),
    inset 0 0 15px rgba(120, 70, 200, 0.3);
  border-color: rgba(200, 150, 255, 0.7);
}

/* 按钮点击效果 */
.clear-filters:active {
  transform: translateY(1px) scale(0.98);
  box-shadow: 
    0 2px 10px rgba(0, 0, 0, 0.3),
    0 0 15px rgba(120, 70, 200, 0.3),
    inset 0 0 8px rgba(120, 70, 200, 0.2);
  transition: all 0.1s ease;
}

/* 添加发光边框效果 */
.clear-filters::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50px;
  background: linear-gradient(135deg, 
    rgba(180, 120, 255, 0.8), 
    rgba(120, 70, 200, 0.8), 
    rgba(180, 120, 255, 0.8));
  z-index: -1;
  opacity: 0.5;
  filter: blur(5px);
  transition: all 0.3s ease;
}

/* 悬停时增强发光边框 */
.clear-filters:hover::before {
  opacity: 0.8;
  filter: blur(8px);
}

/* 添加闪光效果 */
.clear-filters::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: buttonShine 4s linear infinite;
}

@keyframes buttonShine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(100%) rotate(30deg);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .clear-filters {
    padding: 0.9rem 1.5rem;
    font-size: 1rem;
    max-width: 90%;
    margin-top: 2.5rem;
    margin-bottom: 1rem;
  }
  
  .filter-group.full-width.fix-mobile-overlap .clear-filters {
    background: linear-gradient(135deg, #513599, #3d2872);
    box-shadow: none; /* 移除阴影 */
  }
}

@media (max-width: 480px) {
  .clear-filters {
    padding: 0.8rem 1.2rem;
    font-size: 0.95rem;
    border-width: 1px;
  }
  
  /* 减少小屏幕上的动画效果 */
  .clear-filters::after {
    animation: buttonShine 6s linear infinite;
  }
}

/* 宇宙主题轮播图样式 */
.banner-carousel-section {
  margin: 1rem auto 2.5rem;
  position: relative;
  max-width: 1400px;
  width: 100%;
  overflow: hidden;
  z-index: 1;
}

.loading-spinner-container {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(20, 15, 40, 0.7);
  border-radius: 16px;
  overflow: hidden;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(123, 136, 255, 0.3);
  border-radius: 50%;
  border-top-color: #7b88ff;
  animation: spin 1s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(123, 136, 255, 0.5));
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.banner-carousel {
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 30px rgba(123, 136, 255, 0.3);
  border: 1px solid rgba(123, 136, 255, 0.3);
}

.banner-carousel::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #7b88ff, #a080ff);
  z-index: -1;
  border-radius: 18px;
  filter: blur(12px);
  opacity: 0.6;
  animation: glow 3s infinite alternate;
}

@keyframes glow {
  0% { opacity: 0.5; filter: blur(12px); }
  100% { opacity: 0.8; filter: blur(16px); }
}

.carousel-container {
  position: relative;
  overflow: hidden;
  height: 100%;
  border-radius: 16px;
}

.carousel-track {
  display: flex;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  height: 100%;
}

.carousel-slide {
  flex: 0 0 100%;
  position: relative;
  overflow: hidden;
  height: 400px;
  transition: transform 0.3s ease;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s ease;
}

.carousel-slide:hover .banner-image {
  transform: scale(1.05);
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  background: linear-gradient(to top, rgba(10, 5, 20, 0.9), rgba(20, 15, 35, 0.5) 70%, transparent);
  color: white;
  transform: translateY(0);
  transition: transform 0.4s ease;
}

.carousel-slide:hover .banner-content {
  transform: translateY(-5px);
}

.banner-content h3 {
  margin: 0 0 10px;
  font-size: 1.8rem;
  font-weight: 600;
  background: linear-gradient(135deg, #a080ff 10%, #7b88ff 45%, #c4a4ff 90%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 15px rgba(160, 128, 255, 0.3);
  transition: all 0.3s ease;
}

.banner-content p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.85;
  max-width: 80%;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(25, 20, 50, 0.7);
  border: 1px solid rgba(123, 136, 255, 0.4);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 10px rgba(123, 136, 255, 0.2);
  backdrop-filter: blur(5px);
}

.carousel-control.prev {
  left: 20px;
}

.carousel-control.next {
  right: 20px;
}

.banner-carousel:hover .carousel-control {
  opacity: 0.8;
}

.carousel-control:hover {
  background: rgba(40, 30, 80, 0.9);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4), 0 0 15px rgba(123, 136, 255, 0.4);
  border-color: rgba(123, 136, 255, 0.7);
  opacity: 1 !important;
}

.carousel-control::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(123, 136, 255, 0.6), rgba(160, 128, 255, 0.6));
  z-index: -1;
  opacity: 0;
  filter: blur(5px);
  transition: all 0.3s ease;
}

.carousel-control:hover::before {
  opacity: 1;
  filter: blur(8px);
}

.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

.indicator.active {
  background: linear-gradient(135deg, #7b88ff, #a080ff);
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(123, 136, 255, 0.7);
}

.indicator:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

/* 为轮播图添加星星装饰 */
.banner-carousel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background-image: radial-gradient(1px 1px at 50px 30px, #fff 100%, transparent),
                    radial-gradient(1px 1px at 100px 80px, #fff 100%, transparent),
                    radial-gradient(2px 2px at 150px 15px, #fff 100%, transparent),
                    radial-gradient(1px 1px at 200px 50px, #fff 100%, transparent),
                    radial-gradient(1px 1px at 250px 90px, #fff 100%, transparent),
                    radial-gradient(2px 2px at 300px 25px, #fff 100%, transparent),
                    radial-gradient(1px 1px at 350px 75px, #fff 100%, transparent);
  background-size: 100% 100%;
  mix-blend-mode: screen;
  opacity: 0.2;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .carousel-slide {
    height: 250px;
  }
  
  .banner-content {
    padding: 1.2rem;
  }
  
  .banner-content h3 {
    font-size: 1.4rem;
  }
  
  .banner-content p {
    font-size: 0.95rem;
    max-width: 100%;
  }
  
  .carousel-control {
    width: 40px;
    height: 40px;
    opacity: 0.6;
  }
  
  .carousel-control.prev {
    left: 10px;
  }
  
  .carousel-control.next {
    right: 10px;
  }
  
  .carousel-indicators {
    bottom: 10px;
  }
  
  .indicator {
    width: 8px;
    height: 8px;
  }
}

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
  margin: 2rem auto;
  width: 100%;
  max-width: 1400px;
  position: relative;
  z-index: 0; /* 将产品网格z-index降到最低 */
}

.product-card {
  background: #1a1a20;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  width: 100%;
  max-width: 100%;
  height: auto;
  cursor: pointer;
  border: 3px solid #8a2be2 !important;
  transform-origin: center;
  z-index: 0; /* 将产品卡片z-index降到最低 */
}

/* 添加紫色光晕效果 */
.product-card::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(135deg, #8a2be2, #4b0082);
  border-radius: 18px;
  z-index: -5; /* 进一步降低z-index值 */
  opacity: 0.8;
  filter: blur(8px);
  transition: all 0.4s ease;
}

.product-card:hover::before {
  opacity: 1;
  filter: blur(12px);
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
}

.product-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.6), 0 0 30px rgba(255, 255, 255, 0.5);
  border-color: white !important;
  background: linear-gradient(135deg, #2d2d35, #22222a);
  z-index: 0; /* 将产品卡片悬停时的z-index也设为最低 */
  animation: float 3s ease-in-out infinite;
}

/* 悬停时光晕变成白色 */
.product-card:hover::before {
  opacity: 1;
  filter: blur(12px);
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
}

/* 添加卡片悬浮动画 */
@keyframes float {
  0% {
    transform: translateY(-10px) scale(1.05);
  }
  50% {
    transform: translateY(-12px) scale(1.05);
  }
  100% {
    transform: translateY(-10px) scale(1.05);
  }

}

/* 添加点击效果 */
.product-card:active {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(120, 70, 200, 0.4);
  transition: all 0.1s ease;
  animation: none;
}

/* 增强价格显示效果 */
@keyframes priceGlow {
  0% {
    text-shadow: 0 0 12px rgba(195, 163, 255, 0.6);
  }
  50% {
    text-shadow: 0 0 18px rgba(195, 163, 255, 0.8), 0 0 30px rgba(195, 163, 255, 0.4);
  }
  100% {
    text-shadow: 0 0 12px rgba(195, 163, 255, 0.6);
  }
}

.product-card:hover .current-price {
  color: #d4b5ff;
  text-shadow: 0 0 12px rgba(195, 163, 255, 0.6);
  transform: scale(1.05);
  animation: priceGlow 2s infinite;
}

/* 为统计图标添加动画效果 */
@keyframes iconPulse {
  0% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1.1);
  }
}

.stat-item:hover i {
  animation: iconPulse 1s infinite;
  color: #d4b5ff;
}

/* 收藏按钮特殊效果 */
@keyframes heartBeat {
  0% {
    transform: scale(1.1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1.1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1.1);
  }
}

.stat-item.collected:hover i {
  animation: heartBeat 1.5s infinite;
  color: #ff8dd8 !important;
}

/* Mystical glow effect */
.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, rgba(120, 70, 200, 0.15), transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  border-radius: 15px;
  z-index: -5; /* 进一步降低z-index值 */
}

.product-card:hover::before {
  opacity: 1;
}

/* 添加卡片边缘发光效果 */
.product-card::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, rgba(120, 70, 200, 0), rgba(120, 70, 200, 0));
  border-radius: 15px;
  z-index: -5; /* 进一步降低z-index值 */
  transition: all 0.4s ease;
  opacity: 0;
}

.product-card:hover::after {
  background: linear-gradient(135deg, rgba(120, 70, 200, 0.7), rgba(187, 134, 252, 0.7), rgba(120, 70, 200, 0.7));
  box-shadow: 0 0 15px rgba(120, 70, 200, 0.5);
  opacity: 1;
  animation: borderGlow 2s infinite alternate;
}

@keyframes borderGlow {
  0% {
    box-shadow: 0 0 15px rgba(120, 70, 200, 0.5);
  }
  100% {
    box-shadow: 0 0 25px rgba(187, 134, 252, 0.8);
  }
}

.product-image {
  height: 250px; /* 从220px增加到250px，让图片区域更大 */
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #1a1a1a;
  position: relative;
  padding: 6px 4px; /* 上下6px，左右4px，让两边缝隙更小，上下对称 */
}

/* 右上角半球形紫色光晕效果 */
.product-image::after {
  content: '';
  position: absolute;
  top: -100px;
  right: -70px;
  width: 230px;
  height: 230px;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.8) 0%, rgba(138, 43, 226, 0.4) 50%, rgba(138, 43, 226, 0) 80%);
  border-radius: 50%;
  filter: blur(18px);
  opacity: 0.7;
  z-index: 1;
  pointer-events: none;
  transition: all 0.5s ease;
  mix-blend-mode: screen; /* 确保光晕与背景混合，不会完全覆盖图片 */
}

/* 鼠标悬停时光晕变成白色 */
.product-card:hover .product-image::after {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 80%);
  opacity: 0.8;
  filter: blur(20px);
}

.product-image img {
  max-width: 95%; /* 从90%增加到95%，让图片更大 */
  max-height: 95%; /* 从90%增加到95%，让图片更大 */
  width: auto;
  height: auto;
  object-fit: contain;
  transition: transform 0.5s ease, filter 0.5s ease;
  filter: drop-shadow(0 0 8px rgba(120, 70, 200, 0.3));
}

.product-card:hover .product-image img {
  transform: scale(1.1);
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.7));
}

.product-info {
  padding: 8px 12px; /* 从12px减少到8px上下，12px左右，压缩文字区域 */
  background: rgba(35, 35, 40, 0.9);
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.product-name {
  color: #e0e0e0;
  font-size: 0.9rem; /* 从1rem减小到0.9rem */
  margin-bottom: 6px; /* 从10px减少到6px，压缩间距 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 100%;
  line-height: 1.1; /* 从1.2减少行高，更紧凑 */
  transition: all 0.3s ease;
}

.product-card:hover .product-name {
  color: #ffffff;
}

.price-container {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 6px; /* 从10px减少到6px，压缩间距 */
  transition: all 0.3s ease;
}

.product-card:hover .price-container {
  transform: translateY(-2px);
}

.current-price {
  color: #c3a3ff;
  font-size: 1.1rem; /* 从1.2rem减小到1.1rem */
  font-weight: bold;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
  transition: all 0.3s ease;
}

.product-card:hover .current-price {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.original-price {
  color: #888;
  font-size: 0.8rem; /* 从0.9rem减小到0.8rem */
  text-decoration: line-through;
  transition: all 0.3s ease;
}

.product-card:hover .original-price {
  color: #aaa;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  color: #a9a9a9;
  font-size: 0.75rem; /* 从0.85rem减小到0.75rem */
  margin-top: auto;
  padding-top: 6px; /* 从10px减少到6px，压缩顶部间距 */
  border-top: 1px solid rgba(80, 80, 100, 0.3);
  transition: all 0.3s ease;
}

.product-card:hover .product-stats {
  background: rgba(40, 40, 50, 0.9);
  border-top: 1px solid rgba(120, 70, 200, 0.3);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background-color: rgba(120, 70, 200, 0.3);
  color: #d4b5ff;
  transform: translateY(-2px);
}

.product-card:hover .stat-item {
  color: #c0c0c0;
}

.stat-item.liked {
  color: #c3a3ff;
}

.product-card:hover .stat-item.liked {
  color: #d4b5ff;
}

.stat-item.collected {
  color: #ff71ce !important;
}

.stat-item.collected i {
  color: #ff71ce !important;
}

.product-card:hover .stat-item.collected {
  color: #ff8dd8 !important;
}

.product-card:hover .stat-item.collected i {
  color: #ff8dd8 !important;
}

.stat-item i {
  font-size: 1rem;
  transition: all 0.3s ease;
}

.product-card:hover .stat-item i {
  transform: scale(1.1);
}

/* Loading and error states */
.loading-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(10, 10, 15, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #c3a3ff;
  max-width: 1400px;
}

.loading-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #c3a3ff;
  margin: 1rem auto;
  max-width: 1400px;
}

.loading-spinner-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
  background: rgba(30, 30, 35, 0.6);
  padding: 15px;
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 30px rgba(120, 70, 200, 0.3);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(120, 70, 200, 0.2);
  border-top: 4px solid #c3a3ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 0 20px rgba(120, 70, 200, 0.5);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.no-more-products {
  text-align: center;
  padding: 1.5rem;
  color: #c3a3ff;
  font-style: italic;
  background: rgba(30, 30, 35, 0.7);
  border-radius: 15px;
  margin: 1rem auto;
  max-width: 400px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

.error-message {
  background: rgba(150, 0, 0, 0.2);
  color: #ff6b6b;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 5px;
  text-align: center;
  border: 1px solid rgba(150, 0, 0, 0.3);
  box-shadow: 0 0 20px rgba(150, 0, 0, 0.2);
}

.error-message button {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.3);
  padding: 0.5rem 1rem;
  border-radius: 5px;
  margin-left: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.error-message button:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4);
  border-color: rgba(120, 70, 200, 0.6);
}

.no-products {
  text-align: center;
  padding: 3rem;
  color: #c3a3ff;
}

.no-products i {
  color: #c3a3ff;
  margin-bottom: 1rem;
  opacity: 0.7;
}

/* Brand buttons layout */
.brand-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8rem;
  margin-bottom: 1rem;
  overflow: visible;
  transition: all 0.3s ease;
  padding-top: 2px;
  justify-content: center; /* 改回原来的居中对齐 */
  position: relative;
  z-index: 2;
  max-width: 100%; /* 确保容器不会超出父元素 */
  width: 100%; /* 占满容器宽度 */
}

.brand-circle-btn {
  background: rgba(255, 255, 255, 0.15);
  border: none;
  outline: none;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 30px 10px rgba(120, 70, 200, 0.2);
  position: relative;
  transition: box-shadow 0.3s, transform 0.2s;
  cursor: pointer;
  overflow: hidden;
  margin: 0 auto 1rem;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.brand-circle-btn img {
  width: 70px;
  height: 70px;
  object-fit: contain;
  z-index: 2;
  filter: brightness(1) contrast(1.1) drop-shadow(0 0 5px rgba(120, 70, 200, 0.5));
}

.brand-circle-btn.active,
.brand-circle-btn:hover {
  box-shadow: 0 0 50px 20px rgba(120, 70, 200, 0.5), 0 0 0 4px rgba(255, 255, 255, 0.3);
  transform: scale(1.08);
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
}

.inline-brand-btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  margin: 0.25rem;
  border: 2px solid #c3a3ff;
  border-radius: 50px;
  background: transparent;
  color: #c3a3ff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  white-space: nowrap;
  position: relative;
  z-index: 8;
}

.inline-brand-btn:hover {
  background: rgba(120, 70, 200, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(120, 70, 200, 0.3);
}

.inline-brand-btn.active {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border-color: #c3a3ff;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(120, 70, 200, 0.5), 
              0 0 20px rgba(120, 70, 200, 0.3),
              inset 0 1px 3px rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.brand-circle-btn.more-btn {
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  font-size: 1rem;
  font-weight: bold;
  justify-content: center;
  letter-spacing: 1px;
}

.brand-circle-btn.more-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  color: #fff;
  box-shadow: 0 0 30px 10px rgba(255, 255, 255, 0.3);
}

.brand-more-container {
  width: 100%;
  text-align: center;
  margin: 1rem 0 0.5rem;
}

.brand-more-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border: 2px solid rgba(123, 136, 255, 0.5);
  border-radius: 50px;
  padding: 0.5rem 1.5rem;
  font-size: 0.95rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.3);
  position: relative;
  overflow: hidden;
}

.brand-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(120, 70, 200, 0.5);
}

.brand-more-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 10px rgba(120, 70, 200, 0.4);
}

.brand-more-btn i {
  margin-left: 0.5rem;
  transition: transform 0.3s ease;
}

/* Media query for smaller screens */
@media (max-width: 992px) {
  .brands-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .brands-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .brand-circle-btn {
    width: 70px;
    height: 70px;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    border: 1px solid rgba(255, 255, 255, 0.15);
  }
  
  .brand-circle-btn img {
    width: 40px;
    height: 40px;
  }

  .brand-circle-btn.more-btn {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .brands-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
  }
  
  .brand-circle-btn {
    width: 60px;
    height: 60px;
  }
  
  .brand-circle-btn img {
    width: 35px;
    height: 35px;
  }

  .brand-circle-btn.more-btn {
    font-size: 0.7rem;
  }
}

/* Adapt for smaller screens */
@media (max-width: 1400px) {
  .products {
    padding: 2rem 3rem;
  }
  .products-grid {
    grid-template-columns: repeat(4, 1fr); /* 大屏幕保持4列 */
  }
}

@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
  .product-image {
    height: 220px; /* 中等屏幕图片高度增加 */
  }
  .product-info {
    padding: 7px 10px; /* 压缩信息区域 */
  }
}

@media (max-width: 900px) {
  .products {
    padding: 2rem;
  }
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.8rem; /* 保持紧凑的间距 */
  }
  
  .product-card {
    border-radius: 12px;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
  }
  
  .product-image {
    height: 200px; /* 从180px增加到200px */
    padding: 5px 3px; /* 上下5px，左右3px，保持对称且紧凑 */
  }
  
  .product-info {
    padding: 6px 8px; /* 进一步压缩信息区域 */
  }
  
  .product-name {
    font-size: 0.85rem; /* 中等屏幕进一步缩小字体 */
    margin-bottom: 4px; /* 减少间距 */
    line-height: 1.1;
  }
  
  .price-container {
    margin-bottom: 4px; /* 减少间距 */
  }
  
  .current-price {
    font-size: 1rem; /* 中等屏幕价格字体 */
  }
  
  .product-stats {
    padding-top: 4px; /* 减少顶部间距 */
  }
}

@media (max-width: 600px) {
  .products {
    padding: 1rem;
  }
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.6rem;
  }
  
  .product-card {
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }
  
  .product-image {
    height: 160px; /* 从140px增加到160px */
    padding: 3px 2px; /* 小屏幕上下3px，左右2px */
  }
}

/* For touch devices */
@media (hover: none) {
  .product-card {
    transform: none !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
    background: linear-gradient(135deg, #242428, #1a1a20) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(80, 80, 100, 0.3) !important;
  }

  .product-stats .stat-item {
    min-width: 35px;
  }
}

/* Sorting Section */
.sorting-section {
  margin-bottom: 0rem;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
  /* padding: 0.5rem; */
  background: rgba(25, 25, 35, 0.4);
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(120, 70, 200, 0.15);
  backdrop-filter: blur(5px);
}

.sorting-container {
  display: flex;
  gap: 1.2rem;
  align-items: center;
  padding: 0.5rem;
}

.sort-btn {
  padding: 0.8rem 1.4rem;
  background: linear-gradient(135deg, rgba(60, 40, 120, 0.7), rgba(30, 20, 60, 0.7));
  color: #d4b5ff;
  border: 1px solid rgba(138, 43, 226, 0.4);
  border-radius: 30px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  display: flex;
  align-items: center;
  gap: 0.7rem;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2), 
              inset 0 1px 2px rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.sort-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(255, 255, 255, 0.2), 
    transparent
  );
  transition: all 0.6s ease;
}

.sort-btn:hover {
  background: linear-gradient(135deg, rgba(80, 50, 150, 0.8), rgba(40, 25, 80, 0.8));
  transform: translateY(-3px);
  box-shadow: 0 7px 15px rgba(120, 70, 200, 0.4),
              inset 0 1px 3px rgba(255, 255, 255, 0.2);
  border-color: rgba(138, 43, 226, 0.7);
  color: #ffffff;
}

.sort-btn:hover::before {
  left: 100%;
}

.sort-btn.active {
  background: linear-gradient(135deg, rgba(138, 43, 226, 0.8), rgba(90, 30, 160, 0.8));
  color: #ffffff;
  border-color: rgba(160, 90, 230, 0.9);
  box-shadow: 0 4px 12px rgba(120, 70, 200, 0.5), 
              0 0 20px rgba(120, 70, 200, 0.3),
              inset 0 1px 3px rgba(255, 255, 255, 0.3);
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.sort-btn i {
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.sort-btn:hover i {
  transform: scale(1.2);
  animation: pulse 1s infinite alternate;
}

@keyframes pulse {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

.dropdown-trigger {
  position: relative;
  padding-right: 2.8rem;
}

.dropdown-trigger .fa-chevron-down {
  position: absolute;
  right: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
  transition: transform 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55);
  opacity: 0.8;
}

.dropdown-trigger .fa-chevron-down.is-open {
  transform: translateY(-50%) rotate(180deg);
  opacity: 1;
}

.price-sort-dropdown {
  position: relative;
  z-index: 10000; /* 增加z-index，确保下拉框容器有更高优先级 */
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: linear-gradient(135deg, rgba(35, 35, 45, 0.95), rgba(25, 25, 35, 0.95));
  border: 1px solid rgba(138, 43, 226, 0.4);
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.4),
              0 0 20px rgba(120, 70, 200, 0.3),
              inset 0 1px 1px rgba(255, 255, 255, 0.1);
  z-index: 10000; /* 增加z-index值，确保下拉菜单显示在产品卡片之上 */
  min-width: 200px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  pointer-events: auto; /* 确保下拉菜单可以接收鼠标事件 */
  transform-origin: top center;
  animation: dropdownAppear 0.3s forwards;
}

@keyframes dropdownAppear {
  from { opacity: 0; transform: translateY(-10px) scale(0.95); }
  to { opacity: 1; transform: translateY(0) scale(1); }
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  padding: 1rem 1.4rem;
  width: 100%;
  text-align: left;
  background: transparent;
  color: #e0e0e0;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(138, 43, 226, 0.15);
  z-index: 100;
}

.dropdown-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(to bottom, #8a2be2, #4b0082);
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.dropdown-item:hover {
  background: rgba(50, 40, 80, 0.6);
  color: #ffffff;
  padding-left: 1.8rem;
}

.dropdown-item:hover::before {
  transform: scaleY(1);
}

.dropdown-item.active {
  background: rgba(138, 43, 226, 0.3);
  color: #ffffff;
  padding-left: 1.8rem;
}

.dropdown-item.active::before {
  transform: scaleY(1);
}

.dropdown-item i {
  font-size: 1rem;
  width: 1.2rem;
  text-align: center;
  transition: transform 0.3s ease;
}

.dropdown-item:hover i {
  transform: scale(1.2);
}

.sort-direction-icon {
  margin-left: 8px;
  font-size: 0.85rem;
  transition: transform 0.3s ease;
}

.sort-btn:hover .sort-direction-icon {
  transform: scale(1.2);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sorting-container {
    flex-direction: row; /* 改为行排列 */
    align-items: center;
    justify-content: space-between; /* 在行中均匀分布 */
    flex-wrap: wrap; /* 允许在需要时换行 */
    gap: 10px; /* 按钮之间的间距 */
  }
  
  .sort-btn {
    width: calc(50% - 5px); /* 每个按钮占50%宽度减去间距的一半 */
    justify-content: center;
    padding: 10px 5px; /* 减小内边距使内容更紧凑 */
    font-size: 0.9rem; /* 减小字体大小 */
  }
  
  .sort-btn i {
    margin-right: 4px; /* 减小图标和文字间距 */
  }
  
  .dropdown-menu {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .sort-btn {
    font-size: 0.8rem; /* 在更小的屏幕上进一步减小字体 */
    padding: 8px 4px; /* 减小内边距 */
  }
  
  .sort-btn i {
    margin-right: 2px; /* 进一步减小图标和文字间距 */
  }
}

/* Price Range Dropdown Styles */
.price-range-dropdown {
  position: relative;
}

.price-range-menu {
  min-width: 300px;
  padding: 1rem;
}

.price-range-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.price-input {
  flex: 1;
  padding: 0.6rem;
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 6px;
  background: rgba(40, 40, 45, 0.8);
  color: #e0e0e0;
  font-size: 0.95rem;
}

.price-input:focus {
  border-color: #c3a3ff;
  box-shadow: 0 0 0 2px rgba(195, 163, 255, 0.2);
  outline: none;
}

.price-separator {
  color: #c3a3ff;
  font-weight: bold;
}

.apply-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.3);
  padding: 0.8rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  font-size: 0.95rem;
}

.apply-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #42306a, #352458);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4);
  border-color: rgba(120, 70, 200, 0.6);
}

.apply-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: linear-gradient(135deg, #323232, #252525);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .price-range-menu {
    position: fixed;
    top: auto;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    min-width: 100%;
    border-radius: 20px 20px 0 0;
    padding: 1.5rem;
    box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.5);
    z-index: 100;
    max-height: 80vh;
    overflow-y: auto;
    animation: slide-up 0.3s ease;
    background: linear-gradient(to bottom, #2a2a35, #1a1a25);
    border-top: 2px solid rgba(120, 70, 200, 0.5);
  }
  
  @keyframes slide-up {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
  }
  
  .price-range-content {
    gap: 1.5rem;
  }
  
  .price-inputs {
    flex-direction: column;
    width: 100%;
  }
  
  .price-input {
    width: 100%;
    padding: 1rem;
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    border-radius: 10px;
  }
  
  .price-separator {
    margin: 0.5rem 0;
    text-align: center;
    width: 100%;
    font-size: 1.2rem;
  }
  
  .apply-btn {
    padding: 1rem;
    font-size: 1.1rem;
    border-radius: 10px;
    font-weight: bold;
  }
  
  /* 添加关闭按钮样式 */
  .price-range-content::before {
    content: "Price Range";
    display: block;
    text-align: center;
    font-size: 1.2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #c3a3ff;
  }
  
  .price-range-content::after {
    content: "";
    display: block;
    width: 40px;
    height: 5px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
  }
}

/* Banner Carousel Styles */
.banner-carousel-section {
  margin: 0rem auto;
  max-width: 1400px;
}

.banner-carousel {
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 30px rgba(120, 70, 200, 0.3);
  background: rgba(25, 25, 30, 0.8);
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 300px;
  overflow: hidden;
}

.carousel-track {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.carousel-slide {
  min-width: 100%;
  height: 100%;
  position: relative;
  cursor: pointer;
}

.banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8);
  transition: filter 0.3s ease;
}

.carousel-slide:hover .banner-image {
  filter: brightness(1);
}

.banner-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
  color: #fff;
}

.banner-content h3 {
  font-size: 1.5rem;
  margin: 0 0 0.5rem;
  color: #c3a3ff;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.banner-content p {
  font-size: 1rem;
  margin: 0;
  opacity: 0.9;
}

.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(50, 50, 55, 0.7);
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #c3a3ff;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
  z-index: 2;
}

.carousel-control:hover {
  background: rgba(120, 70, 200, 0.5);
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 0 15px rgba(120, 70, 200, 0.5);
}

.carousel-control.prev {
  left: 15px;
}

.carousel-control.next {
  right: 15px;
}

.carousel-indicators {
  position: absolute;
  bottom: 15px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 2;
  padding: 5px 10px;
  border-radius: 15px;
  background-color: rgba(0, 0, 0, 0.3);
}

.indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  margin: 0;
}

.indicator.active {
  background: #c3a3ff;
  transform: scale(1.2);
  box-shadow: 0 0 8px rgba(120, 70, 200, 0.6);
}

@media (max-width: 768px) {
  .carousel-container {
    height: 200px;
  }
  
  .banner-content h3 {
    font-size: 1.2rem;
  }
  
  .banner-content p {
    font-size: 0.9rem;
  }
  
  .carousel-control {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .carousel-indicators {
    display: none; /* 在移动端隐藏轮播指示器 */
  }
}

@media (max-width: 480px) {
  .carousel-container {
    height: 150px;
  }
  
  .banner-content {
    padding: 1rem;
  }
  
  .banner-content h3 {
    font-size: 1rem;
  }
  
  .banner-content p {
    font-size: 0.8rem;
  }
  
  .carousel-control {
    width: 30px;
    height: 30px;
  }
  
  .carousel-indicators {
    display: none; /* 确保在更小的屏幕上也隐藏 */
  }
}

/* Loading spinner for banners */
.loading-spinner-container {
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(25, 25, 30, 0.8);
  border-radius: 15px;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

/* 星星样式增强 */
.star {
  position: absolute;
  width: 2px;
  height: 2px;
  background-color: #fff;
  border-radius: 50%;
  opacity: 0;
  animation: twinkle 5s infinite;
}

/* 微小星星样式 */
.tiny-star {
  width: 1px;
  height: 1px;
  animation: twinkle 4s infinite;
}

/* 大星星样式 */
.large-star {
  width: 3px;
  height: 3px;
  background-color: rgba(187, 134, 252, 0.9);
  animation: twinkle-large 6s infinite;
}

/* 新增的星星位置 */
.star-21 { top: 8%; left: 35%; animation-delay: 0.1s; }
.star-22 { top: 12%; left: 55%; animation-delay: 0.7s; }
.star-23 { top: 17%; left: 40%; animation-delay: 1.1s; }
.star-24 { top: 22%; left: 65%; animation-delay: 1.5s; }
.star-25 { top: 27%; left: 45%; animation-delay: 1.9s; }
.star-26 { top: 32%; left: 60%; animation-delay: 2.3s; }
.star-27 { top: 37%; left: 30%; animation-delay: 2.7s; }
.star-28 { top: 42%; left: 50%; animation-delay: 3.1s; }
.star-29 { top: 47%; left: 70%; animation-delay: 3.5s; }
.star-30 { top: 52%; left: 40%; animation-delay: 3.9s; }
.star-31 { top: 57%; left: 60%; animation-delay: 0.3s; }
.star-32 { top: 62%; left: 35%; animation-delay: 0.9s; }
.star-33 { top: 67%; left: 45%; animation-delay: 1.3s; }
.star-34 { top: 72%; left: 65%; animation-delay: 1.7s; }
.star-35 { top: 77%; left: 30%; animation-delay: 2.1s; }
.star-36 { top: 82%; left: 55%; animation-delay: 2.5s; }
.star-37 { top: 87%; left: 40%; animation-delay: 2.9s; }
.star-38 { top: 92%; left: 50%; animation-delay: 3.3s; }
.star-39 { top: 6%; left: 43%; animation-delay: 3.7s; }
.star-40 { top: 13%; left: 66%; animation-delay: 4.1s; }
.star-41 { top: 19%; left: 47%; animation-delay: 0.2s; }
.star-42 { top: 24%; left: 33%; animation-delay: 0.8s; }
.star-43 { top: 29%; left: 57%; animation-delay: 1.2s; }
.star-44 { top: 33%; left: 37%; animation-delay: 1.6s; }
.star-45 { top: 38%; left: 67%; animation-delay: 2s; }
.star-46 { top: 44%; left: 27%; animation-delay: 2.4s; }
.star-47 { top: 51%; left: 53%; animation-delay: 2.8s; }
.star-48 { top: 56%; left: 32%; animation-delay: 3.2s; }
.star-49 { top: 61%; left: 58%; animation-delay: 3.6s; }
.star-50 { top: 68%; left: 48%; animation-delay: 4s; }

/* 微小星星位置 */
.tiny-star-1 { top: 5%; left: 8%; animation-delay: 0.2s; }
.tiny-star-2 { top: 14%; left: 17%; animation-delay: 0.5s; }
.tiny-star-3 { top: 23%; left: 22%; animation-delay: 0.9s; }
.tiny-star-4 { top: 32%; left: 7%; animation-delay: 1.4s; }
.tiny-star-5 { top: 41%; left: 18%; animation-delay: 1.8s; }
.tiny-star-6 { top: 50%; left: 12%; animation-delay: 2.2s; }
.tiny-star-7 { top: 59%; left: 19%; animation-delay: 2.6s; }
.tiny-star-8 { top: 68%; left: 9%; animation-delay: 3s; }
.tiny-star-9 { top: 77%; left: 16%; animation-delay: 3.4s; }
.tiny-star-10 { top: 86%; left: 21%; animation-delay: 3.8s; }
.tiny-star-11 { top: 4%; left: 73%; animation-delay: 0.3s; }
.tiny-star-12 { top: 13%; left: 83%; animation-delay: 0.7s; }
.tiny-star-13 { top: 22%; left: 78%; animation-delay: 1.1s; }
.tiny-star-14 { top: 31%; left: 88%; animation-delay: 1.5s; }
.tiny-star-15 { top: 40%; left: 82%; animation-delay: 1.9s; }
.tiny-star-16 { top: 49%; left: 87%; animation-delay: 2.3s; }
.tiny-star-17 { top: 58%; left: 81%; animation-delay: 2.7s; }
.tiny-star-18 { top: 67%; left: 89%; animation-delay: 3.1s; }
.tiny-star-19 { top: 76%; left: 84%; animation-delay: 3.5s; }
.tiny-star-20 { top: 85%; left: 79%; animation-delay: 3.9s; }

/* 大星星位置 */
.large-star-1 { top: 20%; left: 72%; animation-delay: 0.5s; }
.large-star-2 { top: 45%; left: 23%; animation-delay: 1.2s; }
.large-star-3 { top: 70%; left: 68%; animation-delay: 1.8s; }
.large-star-4 { top: 35%; left: 78%; animation-delay: 2.5s; }
.large-star-5 { top: 60%; left: 15%; animation-delay: 3.2s; }

/* 大星星特殊闪烁效果 */
@keyframes twinkle-large {
  0%, 100% { opacity: 0.3; transform: scale(1); filter: drop-shadow(0 0 2px rgba(187, 134, 252, 0.5)); }
  50% { opacity: 1; transform: scale(1.4); filter: drop-shadow(0 0 6px rgba(187, 134, 252, 0.9)) drop-shadow(0 0 12px rgba(187, 134, 252, 0.4)); }
}

/* 普通星星闪烁效果优化 */
@keyframes twinkle {
  0%, 100% { opacity: 0.2; transform: scale(1); }
  50% { opacity: 0.8; transform: scale(1.2); filter: drop-shadow(0 0 3px #fff); }
}

/* 调整移动端指示器样式 */
@media (max-width: 768px) {
  /* ... existing code ... */
  
  .carousel-indicators {
    bottom: 10px;
    gap: 6px;
    padding: 4px 8px;
    background-color: rgba(0, 0, 0, 0.4);
  }
  
  .indicator {
    width: 6px;
    height: 6px;
  }
  
  .indicator.active {
    transform: scale(1.1);
    box-shadow: 0 0 5px rgba(120, 70, 200, 0.5);
  }
}

@media (max-width: 480px) {
  /* ... existing code ... */
  
  .carousel-indicators {
    bottom: 5px;
    gap: 4px;
    padding: 3px 6px;
  }
  
  .indicator {
    width: 4px;
    height: 4px;
  }
  
  .indicator.active {
    transform: scale(1.1);
    box-shadow: 0 0 3px rgba(120, 70, 200, 0.4);
  }
}

/* 调整移动端指示器样式 */
@media (max-width: 768px) {
  /* ... existing code ... */
  
  .carousel-indicators {
    display: none; /* 在移动端隐藏轮播指示器 */
  }
}

@media (max-width: 480px) {
  /* ... existing code ... */
  
  .carousel-indicators {
    display: none; /* 确保在更小的屏幕上也隐藏 */
  }
}

/* 移动端隐藏轮播指示器 - 这个样式优先级更高 */
@media (max-width: 768px) {
  .carousel-indicators {
    display: none !important;
  }
}

/* 移动端隐藏轮播控制按钮 */
@media (max-width: 768px) {
  .carousel-control {
    display: none !important;
  }
}

/* 修复重复的样式定义 */
.product-card:hover .current-price {
  color: #d4b5ff;
  text-shadow: 0 0 12px rgba(195, 163, 255, 0.6);
  transform: scale(1.05);
  animation: priceGlow 2s infinite;
}

.product-card:hover .stat-item i {
  transform: none; /* 移除之前的transform，避免与动画冲突 */
}

/* 移动端适配 */
@media (max-width: 768px) {
  .product-card:hover {
    transform: translateY(-5px) scale(1.03);
    animation: float 3s ease-in-out infinite;
  }
  
  @keyframes float {
    0% {
      transform: translateY(-5px) scale(1.03);
    }
    50% {
      transform: translateY(-7px) scale(1.03);
    }
    100% {
      transform: translateY(-5px) scale(1.03);
    }
  }
  
  .product-card:active {
    transform: translateY(-2px) scale(1.01);
  }
  
  .product-card::after {
    display: none; /* 移动端隐藏边缘发光效果，提高性能 */
  }
}

/* 触摸设备优化 */
@media (hover: none) {
  .product-card {
    transform: none !important;
    /* 删除边框样式，使用主要的紫u8272边框 */
    border-radius: 15px !important;
    animation: none !important;
  }
  
  .product-card::before {
    opacity: 0.6;
    filter: blur(6px);
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    display: block !important;
    background: linear-gradient(135deg, #8a2be2, #4b0082) !important;
  }
  
  .product-card:active {
    transform: scale(0.98) !important;
    transition: transform 0.1s ease !important;
  }
  
  .product-image img {
    transform: none !important;
  }
  
  .product-card:hover .current-price,
  .stat-item:hover i,
  .stat-item.collected:hover i {
    animation: none !important;
    transform: none !important;
  }
}

/* 页面标题 */
.page-heading {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  text-align: center;
  margin: 2rem 0;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* 搜索输入框样式 */
.search-input {
  background: rgba(30, 30, 35, 0.8);
  border: 2px solid rgba(120, 70, 200, 0.5);
  color: white;
  border-radius: 50px;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  box-shadow: 0 0 15px rgba(120, 70, 200, 0.3);
  transition: all 0.3s ease;
}

.search-input:focus {
  border-color: rgba(120, 70, 200, 0.8);
  box-shadow: 0 0 20px rgba(120, 70, 200, 0.5);
  outline: none;
}

/* 负责设置媒体查询 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.8rem;
    margin: 1rem auto;
  }
}

@media (max-width: 400px) {
  .products-grid {
    grid-template-columns: repeat(1, 1fr);
    gap: 0.6rem;
  }
  
  /* 在最小屏幕上提升单列商品卡片的视觉效果 */
  .product-card {
    max-width: 280px;
    margin: 0 auto;
  }
  
  .product-image {
    height: 150px;
  }
}

/* 分类按钮和加载更多按钮样式 */
.category-btn, 
.load-more-btn {
  padding: 0.8rem 1.5rem;
  border: 2px solid #8a2be2;
  border-radius: 50px;
  background: transparent;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.4);
}

.category-btn:hover, 
.load-more-btn:hover {
  background: rgba(138, 43, 226, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(138, 43, 226, 0.6);
}

.category-btn.active {
  background: #8a2be2;
  color: white;
  font-weight: 600;
  box-shadow: 0 0 25px rgba(138, 43, 226, 0.7);
}

.load-more-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 1rem;
  margin-bottom: 1.5rem; /* 增加底部间距，防止与下方元素重叠 */
  clear: both;
  position: relative; /* 添加定位上下文 */
  z-index: 5; /* 确保不被其他元素覆盖 */
}

/* 查看更多按钮样式 */
.view-more-btn {
  padding: 0.7rem 1.5rem;
  background: linear-gradient(135deg, #8a2be2, #4b0082);
  color: white;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.95rem;
  font-weight: 500;
  letter-spacing: 0.5px;
  box-shadow: 0 0 15px rgba(138, 43, 226, 0.5);
  width: 180px;
  text-align: center;
  position: relative; /* 添加定位 */
  z-index: 100; /* 确保在最上层 */
}

.view-more-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 25px rgba(138, 43, 226, 0.7);
  background: linear-gradient(135deg, #9b4dff, #5b0096);
}

.view-more-container {
  display: flex;
  justify-content: center;
  width: 100%;
  margin-top: 1rem;
  margin-bottom: 1rem;
  clear: both;
}

/* 移动设备上的标题样式 */
@media (max-width: 768px) {
  .section-title {
    font-size: 1.4rem;
    padding: 0.4rem 1.5rem;
    letter-spacing: 1px;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 1.2rem;
    padding: 0.3rem 1.2rem;
  }
}

/* 移动端适配增强 */
@media (max-width: 768px) {
  .filters-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }
  
  /* 增加这部分解决分类与品牌分区的重叠问题 */
  .filter-group {
    margin-bottom: 1.5rem;
  }
  
  .filter-group.full-width + .filter-group.full-width {
    margin-top: 3rem; /* 增加上边距，使品牌标题与分类按钮有足够间距 */
    padding-top: 1.5rem;
    border-top: 1px solid rgba(120, 70, 200, 0.3);
    position: relative; /* 添加定位上下文 */
  }
  
  /* 添加Clear Filters按钮的视觉分隔 */
  .filter-group:last-child {
    position: relative;
    margin-top: 2.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(120, 70, 200, 0.3);
  }
  
  .clear-filters {
    margin-top: 0.5rem;
    max-width: 90%;
    font-size: 0.95rem;
    padding: 0.8rem;
  }
  
  .category-header {
    margin-bottom: 1rem;
  }
  /* 分类解决部分结束 */
  
  .category-buttons {
    gap: 0.6rem;
  }
  
  .category-btn {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    border-width: 1px;
  }
  
  .brand-circle-btn {
    width: 60px;
    height: 60px;
  }
  
  .brand-circle-btn img {
    width: 35px;
    height: 35px;
  }
  
  .brands-grid {
    gap: 0.8rem;
    margin-top: 1rem; /* 增加顶部间距，让brands与标题有更明显的关联 */
  }
  
  .view-more-btn {
    width: 150px;
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
  
  .view-more-container {
    margin-top: 0.8rem;
    margin-bottom: 0.8rem;
  }
}

@media (max-width: 480px) {
  .filters-section {
    padding: 0rem;
  }
  
  /* 小屏幕上增强品牌和分类的分隔 */
  .filter-group.full-width + .filter-group.full-width {
    margin-top: 3rem !important; /* 强制增加上边距，防止重叠 */
    padding-top: 1.2rem;
  }
  
  /* 增加分类与Load more按钮的间距 */
  .category-buttons {
    margin-bottom: 2.5rem !important;
  }
  
  /* 增加Load more按钮与品牌标题的间距 */
  .view-more-container {
    margin-bottom: 2.5rem !important;
  }
  
  /* 确保标题正确显示 */
  .category-header {
    min-height: 60px;
    margin-bottom: 1.2rem;
  }
}

/* 新增移动端重叠问题修复样式 */
@media (max-width: 768px) {
  /* 品牌与分类的间距 */
  .filter-group.full-width + .filter-group.full-width {
    margin-top: 1.5rem !important;
    border-top: 1px solid rgba(120, 70, 200, 0.5);
    padding-top: 1rem;
    position: relative;
  }
  
  /* 品牌网格样式 */
  .brands-grid {
    margin-top: 1.5rem !important;
    position: relative;
    z-index: 5;
  }
  
  /* 标题样式增强 */
  .category-header {
    margin-bottom: 1.5rem !important;
    position: relative;
    z-index: -1;
  }
  
  /* 分类按钮容器 */
  .category-buttons {
    margin-bottom: 1.5rem !important;
    position: relative;
    z-index: 6;
  }
  
  /* Load more按钮容器 */
  .view-more-container {
    margin-bottom: 1.5rem !important;
    position: relative;
    z-index: 11;
  }
  
  /* Load more按钮 */
  .view-more-btn {
    position: relative;
    z-index: 12;
  }
}

/* 超小屏幕设备额外修复 */
@media (max-width: 480px) {
  /* 减小品牌与分类的间距 */
  .filter-group.full-width + .filter-group.full-width {
    margin-top: 1.8rem !important;
    padding-top: 1.2rem !important;
  }
  
  /* 调整品牌标题间距 */
  .category-header {
    margin-bottom: 1rem !important;
  }
  
  /* 减小Load more按钮的间距 */
  .view-more-container {
    margin-bottom: 1.5rem !important;
  }
}

@media (max-width: 380px) {
  /* 最小屏幕的间距 */
  .filter-group.full-width + .filter-group.full-width {
    margin-top: 2rem !important;
    padding-top: 1.5rem !important;
  }
  
  /* 调整按钮容器间距 */
  .category-buttons {
    margin-bottom: 1.8rem !important;
  }
  
  /* 减小品牌和分类的间距 */
  .view-more-container {
    margin-bottom: 1.8rem !important;
  }
}

/* 修复产品分类按钮重叠问题 */
.category-btn {
  padding: 0.6rem 1.2rem;
  border: 2px solid #c3a3ff;
  border-radius: 50px;
  background: transparent;
  color: #c3a3ff;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  z-index: 8; /* 添加z-index确保按钮在正确层级 */
  margin: 0.3rem 0.2rem; /* 增加按钮间距 */
}

/* 移动端分类按钮布局 */
@media (max-width: 768px) {
  .category-buttons {
    gap: 0.5rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
    padding: 0.5rem 0;
    position: relative;
    z-index: 8;
    margin-bottom: 1.5rem !important; /* 减小底部边距，缩小模块间隔 */
    overflow: visible; /* 确保内容不会被裁剪 */
  }
  
  .category-btn {
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
    margin: 0.25rem;
    z-index: 9;
  }
}

/* 小屏幕上的产品分类按钮修复 */
@media (max-width: 480px) {
  .category-buttons {
    gap: 0.4rem;
    margin-bottom: 4rem !important; /* 在小屏幕上额外增加边距 */
    padding-bottom: 1.5rem; /* 增加内边距 */
  }
  
  .category-btn {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-width: 1px;
    margin: 0.2rem;
  }
}

/* 超小屏幕上的产品分类按钮修复 */
@media (max-width: 380px) {
  .category-buttons {
    gap: 0.3rem;
    margin-bottom: 5rem !important; /* 在超小屏幕上进一步增加边距 */
    padding-bottom: 2rem; /* 增加内边距 */
  }
  
  .category-btn {
    font-size: 0.7rem;
    padding: 0.35rem 0.6rem;
    margin: 0.15rem;
    min-height: 30px; /* 确保按钮高度一致 */
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  /* 确保"加载更多"按钮不与分类按钮重叠 */
  .view-more-container {
    margin-top: 1.5rem !important;
    position: relative;
    z-index: 100;
    width: 100%;
    display: flex;
    justify-content: center;
  }
}

/* 将视觉分隔线添加到分类下方 */
@media (max-width: 768px) {
  .category-buttons {
    border-bottom: 1px dashed rgba(120, 70, 200, 0.4);
    padding-bottom: 2rem;
    margin-bottom: 0rem !important;
    margin: -23px 0px 0px 0px;
  }
  
  /* 加入背景色轻微区分不同区域 */
  .filter-group.full-width {
    padding: 1rem;
    background: rgba(30, 25, 50, 0.4);
    border-radius: 15px;
    margin-bottom: -4.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  /* 品牌区域特别样式 */
  .filter-group.full-width + .filter-group.full-width {
    background: rgba(40, 30, 60, 0.4);
    margin-top: 4rem !important;
  }
}

/* 超小屏幕上的特别优化 */
@media (max-width: 380px) {
  /* 增强产品分类按钮周围的空间 */
  .category-buttons {
    padding: 0.5rem;
    background: rgba(40, 30, 70, 0.3);
    border-radius: 10px;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
  }
  
  /* 确保最小屏幕上也保持两列产品布局 */
  .products-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.5rem;
    width: 100%;
  }
  
  /* 超小屏幕上品牌按钮保持较大尺寸 */
  .brand-circle-btn {
    width: 90px;
    height: 90px;
  }
  
  .brand-circle-btn img {
    width: 55px;
    height: 55px;
  }
  
  /* 加强Load more按钮的视觉区分 */
  .view-more-btn {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

.fix-mobile-overlap {
  margin-top: 8rem !important;
  position: relative;
  z-index: 1;
}

@media (max-width: 768px) {
  .filter-group.full-width + .filter-group.full-width {
    margin-top: 0rem !important;
    margin: -55px auto;
  }
  
  .section-title {
    position: relative;
    z-index: -1;
  }
  
  .brand-circle-btn {
    position: relative;
    z-index: 5;
  }
}


/* 增强分类按钮的样式 */
@media (max-width: 768px) {
  .category-btn {
    padding: 0.6rem 1rem;
    margin: 0.25rem;
    min-height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(30, 25, 50, 0.7);
    border: 1px solid rgba(120, 70, 200, 0.5);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
  }
  
  .category-btn.active {
    background: linear-gradient(135deg, #513599, #3d2872);
    border-color: rgba(255, 255, 255, 0.8);
    box-shadow: 0 0 15px rgba(120, 70, 200, 0.5);
    transform: translateY(-1px);
  }
  
  /* 为品牌区域添加更明显的分隔 */
  .filter-group.full-width.fix-mobile-overlap {
    position: relative;
    z-index: 2;
    margin-top: 8rem !important;
    background: rgba(35, 30, 55, 0.5);
    padding: 1.5rem 1rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }
  
  /* 为品牌区域添加顶部渐变分隔线 */
  .filter-group.full-width.fix-mobile-overlap::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 5%;
    width: 90%;
    height: 4px;
    background: linear-gradient(to right, 
      rgba(120, 70, 200, 0), 
      rgba(120, 70, 200, 0.8), 
      rgba(120, 70, 200, 0)
    );
    border-radius: 2px;
  }
}

/* 修复分类按钮显示/隐藏切换问题 */
@media (max-width: 768px) {
  /* 当显示全部分类时，增加额外的底部间距 */
  .category-buttons.expanded {
    margin-bottom: 8rem !important;
    padding-bottom: 5rem !important;
  }
  
  /* 为Load more按钮下方添加更多空间 */
  .view-more-container {
    margin-top: 1rem;
    margin-bottom: 3rem !important;
    position: relative;
    z-index: 20; /* 提高z-index确保在顶层 */
  }
  
  /* 点击Load more后的状态 */
  .category-buttons.all-categories-visible {
    height: auto;
    padding-bottom: 6rem !important;
  }
}

/* 强化移动端Load more后的分类按钮样式 */
@media (max-width: 768px) {
  /* 当加载更多分类后的容器样式 */
  .category-buttons.all-categories-visible {
    position: relative;
    padding-bottom: 2rem !important;
    margin-bottom: 0 !important;
    z-index: 3;
  }
  
  /* 添加视觉分隔元素 */
  .category-buttons.all-categories-visible::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 5%;
    width: 90%;
    height: 2px;
    background: linear-gradient(to right, rgba(120, 70, 200, 0.2), rgba(120, 70, 200, 0.8), rgba(120, 70, 200, 0.2));
    border-radius: 2px;
  }
  
  /* "Load more"按钮点击后的样式 */
  .category-buttons.all-categories-visible .view-more-container {
    margin-top: 2rem;
    margin-bottom: 0 !important;
  }
  
  /* 点击Load more后显示的按钮 */
  .category-buttons.all-categories-visible .category-btn:nth-child(n+9) {
    /* 为新显示的按钮添加特殊样式 */
    animation: fadeIn 0.3s ease-in-out;
    box-shadow: 0 0 10px rgba(120, 70, 200, 0.4);
  }
  
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
}

/* 超小屏幕设备的额外优化 */
@media (max-width: 480px) {
  .category-buttons.all-categories-visible {
    padding-bottom: 3rem !important;
  }
  
  /* 增加品牌区域的间距 */
  .category-buttons.all-categories-visible + .filter-group.full-width {
    margin-top: 1rem;
  }
}

/* 增强品牌模块的视觉效果 */
.brands-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.2rem;
  padding: 1.5rem 0.5rem;
  background: rgba(25, 20, 40, 0.6);
  border-radius: 18px;
  box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3), 0 5px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(120, 70, 200, 0.3);
  position: relative;
  overflow: hidden;
}

/* 添加品牌网格背景效果 */
.brands-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(120, 70, 200, 0.15), transparent 70%);
  z-index: 1;
  pointer-events: none;
}

.brand-circle-btn {
  background: rgba(20, 15, 35, 0.7);
  border: 2px solid rgba(120, 70, 200, 0.4);
  outline: none;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  position: relative;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  overflow: hidden;
  margin: 0 auto 1rem;
  z-index: 2;
}

.brand-circle-btn::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(120, 70, 200, 0.8), rgba(187, 134, 252, 0.8));
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.brand-circle-btn img {
  width: 70px;
  height: 70px;
  object-fit: contain;
  filter: brightness(0.9) contrast(1.2) drop-shadow(0 0 5px rgba(120, 70, 200, 0.5));
  transition: all 0.3s ease;
}

.brand-circle-btn:hover {
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 30px rgba(120, 70, 200, 0.4);
  border-color: rgba(150, 100, 230, 0.7);
}

.brand-circle-btn:hover::before {
  opacity: 0.3;
}

.brand-circle-btn:hover img {
  filter: brightness(1.1) contrast(1.3) drop-shadow(0 0 8px rgba(120, 70, 200, 0.8));
  transform: scale(1.1);
}

.brand-circle-btn.active {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 30px rgba(120, 70, 200, 0.7);
  transform: translateY(-5px) scale(1.05);
}

.brand-circle-btn.active img {
  filter: brightness(1.2) contrast(1.3) drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
}

.brand-circle-btn.more-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  font-weight: bold;
  letter-spacing: 1px;
}

.brand-circle-btn.more-btn:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  color: white;
}

.brand-circle-btn.more-btn i {
  margin-left: 5px;
  transition: transform 0.3s ease;
}

.brand-circle-btn.more-btn:hover i {
  transform: translateY(3px);
}

/* 移动设备上的品牌模块样式 */
@media (max-width: 992px) {
  .brands-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    padding: 1.2rem 0.4rem;
  }
  
  .brand-circle-btn {
    width: 100px;
    height: 100px;
  }
  
  .brand-circle-btn img {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 768px) {
  .brands-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    padding: 1.2rem 0.4rem;
    border-radius: 15px;
    background: rgba(25, 20, 40, 0.7);
  }
  
  .brand-circle-btn {
    width: 100px; /* Increased from 80px */
    height: 100px; /* Increased from 80px */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.3);
    border-width: 2px;
  }
  
  .brand-circle-btn img {
    width: 60px; /* Increased from 45px */
    height: 60px; /* Increased from 45px */
  }
  
  .brand-circle-btn:hover {
    transform: translateY(-3px) scale(1.03);
  }
  
  .brand-circle-btn.active {
    transform: translateY(-3px) scale(1.03);
  }
  
  .brand-circle-btn.more-btn {
    font-size: 0.9rem;
  }
  
  /* 增强品牌区域标题 */
  .filter-group.full-width.fix-mobile-overlap .category-header {
    margin-bottom: 1.5rem;
  }
  
  .filter-group.full-width.fix-mobile-overlap .section-title {
    background: linear-gradient(135deg, #513599, #3d2872);
    box-shadow: 0 4px 15px rgba(120, 70, 200, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.5);
  }
  
  /* 为品牌区域添加动画效果 */
  .filter-group.full-width.fix-mobile-overlap {
    animation: fadeInUp 0.5s ease-out;
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0.5;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}

@media (max-width: 480px) {
  .brands-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1.2rem 0.3rem;
  }
  
  .brand-circle-btn {
    width: 100px; /* Increased from 70px */
    height: 100px; /* Increased from 70px */
    margin-bottom: 1rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.3);
  }
  
  .brand-circle-btn img {
    width: 60px; /* Increased from 40px */
    height: 60px; /* Increased from 40px */
  }
  
  .brand-circle-btn.more-btn {
    font-size: 0.8rem;
  }
  
  .brand-circle-btn.more-btn span {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .brand-circle-btn.more-btn i {
    margin-left: 0;
    margin-top: 3px;
  }
}

/* 品牌区域标题的强化样式 */
.filter-group.full-width .category-header {
  position: relative;
}

.filter-group.full-width .category-header::before,
.filter-group.full-width .category-header::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 100px;
  height: 2px;
  background: linear-gradient(to right, rgba(120, 70, 200, 0.8), rgba(120, 70, 200, 0));
  transform: translateY(-50%);
}

.filter-group.full-width .category-header::before {
  left: -80px;
}

.filter-group.full-width .category-header::after {
  right: -80px;
  background: linear-gradient(to left, rgba(120, 70, 200, 0.8), rgba(120, 70, 200, 0));
}

@media (max-width: 768px) {
  .filter-group.full-width .category-header::before,
  .filter-group.full-width .category-header::after {
    width: 60px;
  }
  
  .filter-group.full-width .category-header::before {
    left: -40px;
  }
  
  .filter-group.full-width .category-header::after {
    right: -40px;
  }
}

/* 品牌区域强调效果 */
.filter-group.full-width.fix-mobile-overlap {
  position: relative;
  transition: all 0.4s ease;
  /* 移除以下属性 */
  background: transparent;
  box-shadow: none;
  border: none;
  padding: 0;
}

@media (max-width: 768px) {
  /* 为品牌区域添加更明显的分隔 */
  .filter-group.full-width.fix-mobile-overlap {
    position: relative;
    z-index: 2;
    margin-top: 8rem !important;
    /* 移除以下属性 */
    background: transparent;
    box-shadow: none;
    border: none;
    padding: 1.5rem 0;
  }
  
  /* 移除品牌区域的顶部渐变分隔线 */
  .filter-group.full-width.fix-mobile-overlap::before {
    display: none;
  }
}

/* 移除品牌区域背景渐变增强 */
.filter-group.full-width.fix-mobile-overlap {
  background: transparent;
}

/* 品牌按钮动画效果 */
.brand-circle-btn {
  animation: brandAppear 0.6s cubic-bezier(0.26, 0.53, 0.74, 1.48) backwards;
}

/* 错开品牌按钮的动画延迟，创造级联效果 */
.brands-grid .brand-circle-btn:nth-child(1) { animation-delay: 0.1s; }
.brands-grid .brand-circle-btn:nth-child(2) { animation-delay: 0.15s; }
.brands-grid .brand-circle-btn:nth-child(3) { animation-delay: 0.2s; }
.brands-grid .brand-circle-btn:nth-child(4) { animation-delay: 0.25s; }
.brands-grid .brand-circle-btn:nth-child(5) { animation-delay: 0.3s; }
.brands-grid .brand-circle-btn:nth-child(6) { animation-delay: 0.35s; }
.brands-grid .brand-circle-btn:nth-child(7) { animation-delay: 0.4s; }
.brands-grid .brand-circle-btn:nth-child(8) { animation-delay: 0.45s; }
.brands-grid .brand-circle-btn:nth-child(9) { animation-delay: 0.5s; }
.brands-grid .brand-circle-btn:nth-child(10) { animation-delay: 0.55s; }
.brands-grid .brand-circle-btn.more-btn { animation-delay: 0.6s; }

@keyframes brandAppear {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(20px);
  }
  70% {
    opacity: 1;
    transform: scale(1.1) translateY(-5px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 品牌按钮悬停光晕效果 */
.brand-circle-btn::after {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(120, 70, 200, 0.4) 0%, rgba(120, 70, 200, 0) 70%);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
}

.brand-circle-btn:hover::after,
.brand-circle-btn.active::after {
  opacity: 1;
}

/* 品牌区域背景渐变增强 */
.filter-group.full-width.fix-mobile-overlap {
  background: linear-gradient(145deg, rgba(30, 25, 45, 0.7), rgba(45, 35, 65, 0.7));
}

/* 品牌标题样式增强 */
.filter-group.full-width.fix-mobile-overlap .section-title {
  text-shadow: 0 0 10px rgba(120, 70, 200, 0.8);
  position: relative;
  overflow: hidden;
}

/* 标题闪光动画 */
.filter-group.full-width.fix-mobile-overlap .section-title::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(30deg);
  animation: titleShine 4s linear infinite;
}

@keyframes titleShine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  100% {
    transform: translateX(100%) rotate(30deg);
  }
}

/* 增强品牌圆圈的光晕效果 */
.brand-circle-btn {
  position: relative;
  z-index: 2;
}

/* 外部光晕效果 */
.brand-circle-btn::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(120, 70, 200, 0.8), rgba(187, 134, 252, 0.8));
  z-index: -1;
  opacity: 0.3;
  filter: blur(6px);
  transition: all 0.4s ease;
  animation: pulseGlow 3s infinite alternate;
}

/* 激活状态的圆圈光晕 */
.brand-circle-btn.active::before {
  opacity: 0.6;
  filter: blur(10px);
  animation: activePulseGlow 2s infinite alternate;
}

/* 悬停状态的圆圈光晕 */
.brand-circle-btn:hover::before {
  opacity: 0.5;
  filter: blur(8px);
  animation: hoverPulseGlow 1.5s infinite alternate;
}

/* 额外的内部发光效果 */
.brand-circle-btn::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: transparent;
  box-shadow: inset 0 0 15px rgba(187, 134, 252, 0.3);
  z-index: 1;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.brand-circle-btn:hover::after,
.brand-circle-btn.active::after {
  box-shadow: inset 0 0 20px rgba(187, 134, 252, 0.7);
  opacity: 1;
}

/* 图片保持在光晕上层 */
.brand-circle-btn img {
  position: relative;
  z-index: 2;
}

/* 光晕脉动动画 */
@keyframes pulseGlow {
  0% {
    opacity: 0.2;
    filter: blur(5px);
  }
  100% {
    opacity: 0.4;
    filter: blur(8px);
  }
}

@keyframes hoverPulseGlow {
  0% {
    opacity: 0.4;
    filter: blur(6px);
    transform: scale(1);
  }
  100% {
    opacity: 0.6;
    filter: blur(10px);
    transform: scale(1.05);
  }
}

@keyframes activePulseGlow {
  0% {
    opacity: 0.5;
    filter: blur(8px);
    transform: scale(1);
  }
  100% {
    opacity: 0.8;
    filter: blur(12px);
    transform: scale(1.08);
  }
}

/* 更多按钮特殊光晕 */
.brand-circle-btn.more-btn::before {
  background: linear-gradient(135deg, rgba(180, 120, 255, 0.8), rgba(220, 180, 255, 0.8));
}

/* 移动端光晕适配 */
@media (max-width: 768px) {
  .brand-circle-btn::before {
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    filter: blur(4px);
  }
  
  .brand-circle-btn:hover::before,
  .brand-circle-btn.active::before {
    filter: blur(6px);
  }
  
  .brand-circle-btn::after {
    box-shadow: inset 0 0 10px rgba(187, 134, 252, 0.3);
  }
  
  .brand-circle-btn:hover::after,
  .brand-circle-btn.active::after {
    box-shadow: inset 0 0 15px rgba(187, 134, 252, 0.6);
  }
}

/* 添加额外的外围光晕 */
.brand-circle-btn {
  position: relative;
}

/* 第三层光晕 - 最外层的柔和发光 */
.brand-circle-btn::before {
  content: '';
  position: absolute;
  top: -8px;
  left: -8px;
  right: -8px;
  bottom: -8px;
  border-radius: 50%;
  background: radial-gradient(circle, 
    rgba(187, 134, 252, 0.5) 0%, 
    rgba(120, 70, 200, 0.3) 30%, 
    rgba(120, 70, 200, 0) 70%);
  z-index: -2;
  opacity: 0.4;
  filter: blur(8px);
  animation: outerGlowPulse 4s infinite alternate;
}

/* 第二层光晕 - 中间层的锐利发光 */
.brand-circle-btn::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: radial-gradient(circle, 
    rgba(220, 180, 255, 0.7) 0%, 
    rgba(180, 120, 255, 0.4) 40%, 
    rgba(120, 70, 200, 0) 70%);
  z-index: -1;
  opacity: 0.4;
  filter: blur(4px);
  animation: midGlowPulse 3s infinite alternate;
}

/* 光斑闪烁动画 */
@keyframes outerGlowPulse {
  0% {
    opacity: 0.3;
    filter: blur(8px);
  }
  100% {
    opacity: 0.6;
    filter: blur(12px);
  }
}

@keyframes midGlowPulse {
  0% {
    opacity: 0.3;
    filter: blur(4px);
  }
  100% {
    opacity: 0.5;
    filter: blur(6px);
  }
}

/* 激活状态的增强光晕 */
.brand-circle-btn.active::before {
  opacity: 0.6;
  background: radial-gradient(circle, 
    rgba(215, 180, 255, 0.8) 0%, 
    rgba(150, 100, 230, 0.5) 30%, 
    rgba(120, 70, 200, 0) 70%);
  animation: activeOuterGlow 2s infinite alternate;
}

.brand-circle-btn.active::after {
  opacity: 0.7;
  background: radial-gradient(circle, 
    rgba(255, 240, 255, 0.9) 0%, 
    rgba(200, 150, 255, 0.6) 40%, 
    rgba(150, 100, 230, 0) 70%);
  animation: activeMidGlow 2s infinite alternate;
}

@keyframes activeOuterGlow {
  0% {
    opacity: 0.5;
    filter: blur(8px);
  }
  100% {
    opacity: 0.8;
    filter: blur(15px);
  }
}

@keyframes activeMidGlow {
  0% {
    opacity: 0.6;
    filter: blur(4px);
  }
  100% {
    opacity: 0.9;
    filter: blur(8px);
  }
}

/* 悬停状态的光晕增强 */
.brand-circle-btn:hover::before {
  opacity: 0.5;
  filter: blur(10px);
  transform: scale(1.05);
  transition: all 0.3s ease;
}

.brand-circle-btn:hover::after {
  opacity: 0.6;
  filter: blur(5px);
  transform: scale(1.03);
  transition: all 0.3s ease;
}

/* 强化品牌按钮主体 */
.brand-circle-btn {
  border: 2px solid rgba(180, 120, 255, 0.6);
  box-shadow: 
    0 5px 15px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(120, 70, 200, 0.3),
    inset 0 0 15px rgba(120, 70, 200, 0.2);
  background: radial-gradient(circle at center,
    rgba(40, 30, 60, 0.9),
    rgba(30, 20, 45, 0.9));
  overflow: visible; /* 确保光晕可见 */
}

/* 调整图片样式以适应新的光晕效果 */
.brand-circle-btn img {
  position: relative;
  z-index: 3;
  filter: drop-shadow(0 0 8px rgba(180, 120, 255, 0.6));
}

/* 移动端光晕效果优化 */
@media (max-width: 768px) {
  .brand-circle-btn::before {
    top: -6px;
    left: -6px;
    right: -6px;
    bottom: -6px;
    filter: blur(6px);
    opacity: 0.3;
  }
  
  .brand-circle-btn::after {
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    filter: blur(3px);
  }
  
  .brand-circle-btn.active::before {
    filter: blur(8px);
  }
  
  .brand-circle-btn.active::after {
    filter: blur(4px);
  }
  
  /* 减少动画复杂度以提高移动端性能 */
  @keyframes outerGlowPulse {
    0%, 100% {
      opacity: 0.3;
      filter: blur(6px);
    }
    50% {
      opacity: 0.5;
      filter: blur(8px);
    }
  }
  
  @keyframes midGlowPulse {
    0%, 100% {
      opacity: 0.3;
      filter: blur(3px);
    }
    50% {
      opacity: 0.5;
      filter: blur(4px);
    }
  }
}

/* 小屏幕设备进一步优化 */
@media (max-width: 480px) {
  .brand-circle-btn::before {
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    filter: blur(4px);
  }
  
  .brand-circle-btn::after {
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    filter: blur(2px);
  }
  
  /* 减少小屏幕上的动画效果 */
  .brand-circle-btn {
    animation: none; /* 移除复杂动画以提高性能 */
  }
  
  .brand-circle-btn::before,
  .brand-circle-btn::after {
    animation-duration: 5s; /* 降低动画频率 */
  }
}

/* 添加特殊的交互效果 */
.brand-circle-btn:active {
  transform: scale(0.95);
  transition: transform 0.2s ease;
}

.brand-circle-btn:active::before {
  opacity: 0.8;
  filter: blur(12px);
  transition: all 0.2s ease;
}

.brand-circle-btn:active::after {
  opacity: 0.9;
  filter: blur(6px);
  transition: all 0.2s ease;
}

/* 添加"更多"按钮的特殊光晕 */
.brand-circle-btn.more-btn::before {
  background: radial-gradient(circle, 
    rgba(220, 180, 255, 0.6) 0%, 
    rgba(150, 100, 230, 0.4) 30%, 
    rgba(120, 70, 200, 0) 70%);
}

.brand-circle-btn.more-btn::after {
  background: radial-gradient(circle, 
    rgba(255, 220, 255, 0.8) 0%, 
    rgba(180, 120, 255, 0.5) 40%, 
    rgba(150, 100, 230, 0) 70%);
}

/* 为品牌图标添加闪光效果 */
.brand-circle-btn:hover img {
  animation: iconGlow 2s infinite alternate;
}

@keyframes iconGlow {
  0% {
    filter: drop-shadow(0 0 8px rgba(180, 120, 255, 0.6));
  }
  100% {
    filter: drop-shadow(0 0 12px rgba(220, 180, 255, 0.8));
  }
}

/* 打字效果样式 */
.typing-placeholder {
  position: absolute;
  left: 4.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(195, 163, 255, 0.6);
  font-size: 1.1rem;
  pointer-events: none;
  z-index: 1;
  display: flex;
  align-items: center;
}

.typing-cursor {
  display: inline-block;
  margin-left: 2px;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

/* 移动端搜索框样式 */
@media (max-width: 768px) {
  .search-section {
    margin: 0;
    padding: 0.8rem 1rem;
    position: sticky;
    top: 60px;
    z-index: 20;
    width: 100%;
    left: 0;
    right: 0;
    background: rgba(20, 20, 30, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(120, 70, 200, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  }

  .search-container {
    flex-direction: row;
    gap: 0.5rem;
    width: 100%;
    align-items: center;
    max-width: 100%;
  }

  .search-wrapper {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 0.3rem;
  }
  
  /* 调整移动端的发光效果 */
  .search-wrapper::before {
    animation: borderGlow 4s infinite alternate;
    opacity: 0.4;
  }

  .search-icon-left {
    padding: 0 0.5rem;
    font-size: 1rem;
    animation: none;
  }

  .search-input {
    padding: 0.7rem 0.5rem;
    font-size: 0.95rem;
    height: 40px;
    flex: 1;
    min-width: 0;
  }
  
  .search-button {
    min-width: 40px;
    height: 40px;
    border-radius: 20px;
    padding: 0 1rem;
    margin-left: 4px;
    flex-shrink: 0;
  }

  .search-button-text {
    display: none;
  }
  
  .typing-placeholder {
    left: 3.2rem;
    font-size: 0.95rem;
  }
  
  /* 较小屏幕的进一步优化 */
  @media (max-width: 375px) {
    .search-input {
      font-size: 0.9rem;
      padding: 0.6rem 0.5rem;
    }
    
    .search-button {
      min-width: 36px;
      width: 36px;
      height: 36px;
    }
  }
}

@keyframes whiteGradientMove {
  0% { 
    background-position: 0% 50%; 
  }
  50% { 
    background-position: 100% 50%; 
  }
  100% { 
    background-position: 0% 50%; 
  }
}
/* Loading & No More Data Styles */
.load-more-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 0;
  min-height: 100px;
  width: 100%;
}

/* Cosmic Loader */
.cosmic-loader {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cosmic-loader-ring {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #9e49ff;
  border-left-color: #4cebff;
  border-right-color: #ff9be7;
  animation: cosmicSpin 1.5s linear infinite;
  box-shadow: 
    0 0 15px rgba(156, 73, 255, 0.6),
    0 0 30px rgba(76, 235, 255, 0.4),
    0 0 45px rgba(255, 155, 231, 0.3);
}

.cosmic-loader-stars {
  position: absolute;
  width: 100%;
  height: 100%;
  animation: cosmicRotate 7s linear infinite;
}

.cosmic-star {
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: #ffffff;
  border-radius: 50%;
  opacity: 0.7;
  filter: blur(1px);
  animation: cosmicTwinkle 2s infinite alternate;
}

.cosmic-star:nth-child(1) { top: 10%; left: 50%; animation-delay: 0s; }
.cosmic-star:nth-child(2) { top: 50%; left: 10%; animation-delay: 0.3s; }
.cosmic-star:nth-child(3) { top: 90%; left: 50%; animation-delay: 0.6s; }
.cosmic-star:nth-child(4) { top: 50%; left: 90%; animation-delay: 0.9s; }
.cosmic-star:nth-child(5) { top: 25%; left: 25%; animation-delay: 1.2s; }
.cosmic-star:nth-child(6) { top: 75%; left: 25%; animation-delay: 1.5s; }
.cosmic-star:nth-child(7) { top: 75%; left: 75%; animation-delay: 1.8s; }
.cosmic-star:nth-child(8) { top: 25%; left: 75%; animation-delay: 2.1s; }

@keyframes cosmicSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes cosmicRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes cosmicTwinkle {
  0% { opacity: 0.2; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1.2); }
}

/* No More Products Styles */
.no-more-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #9e9eaf;
  text-align: center;
  padding: 1rem;
  animation: fadeIn 0.8s ease-in-out;
}

.no-more-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #9e49ff;
  animation: floatIcon 4s ease-in-out infinite;
}

.cosmic-line {
  width: 80px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #9e49ff, #4cebff, #ff9be7, transparent);
  margin-top: 0.8rem;
  animation: linePulse 2s ease-in-out infinite;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes floatIcon {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-8px); }
}

@keyframes linePulse {
  0%, 100% { opacity: 0.3; width: 40px; }
  50% { opacity: 1; width: 80px; }
}

/* 产品分类标题样式 - 与品牌分类标题相同 */
.product-section-title {
  color: white;
  text-align: center;
  font-size: 1.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin: 0;
  padding: 0.5rem 2rem;
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border-radius: 50px;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.5);
  position: relative;
  z-index: 1;
  border: 2px solid rgba(195, 163, 255, 0.5);
  animation: titleGlow 3s infinite alternate;
}

.product-section-title::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(135deg, rgba(120, 70, 200, 0.7), rgba(187, 134, 252, 0.7));
  border-radius: 50px;
  z-index: -1;
  opacity: 0.5;
  filter: blur(8px);
}

/* 确保在移动设备上也显示产品分类标题 */
@media (max-width: 768px) {
  .product-section-title {
    display: inline-block !important;
    font-size: 1.5rem;
    padding: 0.4rem 1.5rem;
    visibility: visible !important;
    opacity: 1 !important;
  }
  
  /* 确保分类标题容器在移动端正确显示 */
  .category-header {
    display: flex !important;
    justify-content: center !important;
    margin-bottom: 1.5rem !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: relative !important;
    z-index: 10 !important;
  }
  
  /* 移动端下产品分类标题的特殊样式 */
  .mobile-device .product-section-title,
  .mobile-device .category-header {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* 移动端下产品分类标题的覆盖样式 - 使用更高优先级选择器 */
body .mobile-device .filters-section .filter-group .category-header .product-section-title,
html .mobile-device .filters-section .filter-group .category-header .product-section-title,
#app .mobile-device .filters-section .filter-group .category-header .product-section-title,
.products .filters-section .filter-group .category-header .product-section-title {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 特别覆盖App.vue中的样式 */
.product-category-header {
  display: flex !important;
  justify-content: center !important;
  margin-bottom: 1.5rem !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 确保在任何设备上都显示产品分类标题 */
.product-section-title {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* 品牌和分类标题样式 - 移动端 */
@media (max-width: 768px) {
  .brands-header, 
  .category-header {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    margin-bottom: 1.5rem !important;
    position: relative !important;
    z-index: 5 !important;
  }
  
  .section-title,
  .product-section-title {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: white;
    text-align: center;
    font-size: 1.5rem !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin: 0 auto !important;
    padding: 0.5rem 2rem !important;
    background: linear-gradient(135deg, #352458, #2d1e4a);
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(120, 70, 200, 0.5);
    position: relative;
    z-index: 1;
    border: 2px solid rgba(195, 163, 255, 0.5);
  }
  
  .section-title::before,
  .product-section-title::before {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(135deg, rgba(120, 70, 200, 0.7), rgba(187, 134, 252, 0.7));
    border-radius: 50px;
    z-index: -1;
    opacity: 0.5;
    filter: blur(8px);
  }
}

/* ProductsView页面专用移动端产品卡片图片修复 - 避免与其他页面冲突 */
@media (max-width: 767px) {
  /* 减少产品卡片高度 */
  .products .products-grid .product-card {
    height: 240px !important; /* 原来是290px，减少到240px */
    min-height: 240px !important;
    max-height: 240px !important;
  }
  
  /* 特定于ProductsView的高优先级选择器 */
  .products .products-grid .product-card .product-image {
    height: 140px !important; /* 原来是150px，减少高度 */
    width: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: #121217 !important;
    padding: 8px !important;
    position: relative !important;
    overflow: hidden !important;
  }
  
  /* 调整信息区域高度和内边距 */
  .products .products-grid .product-card .product-info {
    padding: 8px !important; /* 减小内边距 */
    max-height: 100px !important; /* 限制高度 */
    min-height: 80px !important;
  }
  
  /* 减小标题和价格的间距 */
  .products .products-grid .product-card .product-name {
    margin-bottom: 4px !important;
    font-size: 0.9rem !important;
  }
  
  .products .products-grid .product-card .price-container {
    margin-bottom: 4px !important;
  }
  
  .products .products-grid .product-card .product-image img {
    position: relative !important; /* 修改为relative, 避免absolute定位导致偏左偏上 */
    top: auto !important;
    left: auto !important;
    max-width: 90% !important;
    max-height: 90% !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    margin: 0 auto !important; /* 确保水平居中 */
    display: block !important;
  }
}

/* 较小屏幕的特殊优化 */
@media (max-width: 480px) {
  .products .products-grid .product-card {
    height: 220px !important; /* 更小屏幕上进一步减小高度 */
    min-height: 220px !important;
    max-height: 220px !important;
  }
  
  .products .products-grid .product-card .product-image {
    height: 120px !important;
    padding: 5px !important;
  }
}

/* 超小屏幕的特殊优化 */
@media (max-width: 374px) {
  .products .products-grid .product-card {
    height: 200px !important; /* 最小屏幕下的最小高度 */
    min-height: 200px !important;
    max-height: 200px !important;
  }
  
  .products .products-grid .product-card .product-image {
    height: 110px !important;
  }
}

/* ======================== 产品网格样式 ======================== */
.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1.5rem;
  padding: 2rem 0;
  width: 100%;
  margin: 0 auto;
  box-sizing: border-box;
}

/* ======================== 产品卡片样式 ======================== */
.product-card {
  background: linear-gradient(135deg, #2a1f3b, #1e1a2e);
  border-radius: 15px;
  overflow: hidden;
  border: 2px solid rgba(195, 163, 255, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

.product-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.8),
    0 0 30px 5px rgba(255, 255, 255, 0.8),
    0 0 50px 10px rgba(255, 255, 255, 0.6),
    0 0 70px 15px rgba(255, 255, 255, 0.4),
    0 0 90px 20px rgba(128, 102, 204, 0.3);
  z-index: 100;
}

/* 产品卡片发光效果 */
.product-card::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 20px;
  background: transparent;
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 
    0 0 20px 5px rgba(255, 255, 255, 0.8),
    0 0 40px 10px rgba(128, 102, 204, 0.7),
    0 0 60px 15px rgba(128, 102, 204, 0.5),
    0 0 80px 20px rgba(128, 102, 204, 0.3);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.product-card:hover::after {
  opacity: 1;
  animation: strongGlow 1.5s infinite alternate;
}

@keyframes strongGlow {
  0% {
    box-shadow: 
      0 0 20px 5px rgba(255, 255, 255, 0.8),
      0 0 40px 10px rgba(128, 102, 204, 0.7),
      0 0 60px 15px rgba(128, 102, 204, 0.5),
      0 0 80px 20px rgba(128, 102, 204, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.8) !important;
  }
  100% {
    box-shadow: 
      0 0 30px 10px rgba(255, 255, 255, 0.9),
      0 0 60px 15px rgba(128, 102, 204, 0.8),
      0 0 90px 20px rgba(128, 102, 204, 0.6),
      0 0 120px 25px rgba(128, 102, 204, 0.4) !important;
    border-color: rgba(255, 255, 255, 1) !important;
  }
}

/* 产品图片样式 */
.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;
  background: linear-gradient(135deg, #2a1f3b, #1e1a2e);
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.1);
}

/* 产品信息样式 */
.product-info {
  padding: 1.2rem;
  background: linear-gradient(135deg, #2a1f3b, #1e1a2e);
}

.product-name {
  color: white;
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.8rem 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 价格容器样式 */
.price-container {
  margin-bottom: 1rem;
}

.current-price {
  color: #bb86fc;
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0;
  text-shadow: 0 0 10px rgba(187, 134, 252, 0.8);
}

.original-price {
  color: #888;
  font-size: 1rem;
  text-decoration: line-through;
  margin: 0.2rem 0 0 0;
}

/* 产品统计样式 */
.product-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #c3a3ff;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0.2rem 0.4rem;
  border-radius: 15px;
}

.stat-item:hover {
  background: rgba(195, 163, 255, 0.2);
  transform: scale(1.1);
}

.stat-item i {
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.stat-item.liked,
.stat-item.collected {
  color: #ff6b6b;
  text-shadow: 0 0 8px rgba(255, 107, 107, 0.8);
}

.stat-item.liked i,
.stat-item.collected i {
  color: #ff6b6b;
  transform: scale(1.2);
}

/* 悬浮动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.product-card:nth-child(2n) {
  animation-delay: -2s;
}

.product-card:nth-child(3n) {
  animation-delay: -4s;
}

/* ======================== 响应式设计 ======================== */

/* 大屏幕 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.2rem;
  }
  
  .product-image {
    height: 220px;
  }
}

/* 平板 */
@media (max-width: 992px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
  
  .product-image {
    height: 200px;
  }
  
  .product-info {
    padding: 1rem;
  }
}

/* 手机横屏/小平板 */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1rem 0.5rem;
  }
  
  .product-card {
    border-radius: 12px;
  }
  
  .product-image {
    height: 180px;
  }
  
  .product-info {
    padding: 0.8rem;
  }
  
  .product-name {
    font-size: 1rem;
  }
  
  .current-price {
    font-size: 1.1rem;
  }
  
  .stat-item {
    font-size: 0.8rem;
    gap: 0.2rem;
  }
  
  .stat-item i {
    font-size: 0.7rem;
  }
}

/* 手机竖屏 */
@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    padding: 0 10px;
  }
  
  .product-card {
    border-radius: 8px;
    background: #1c1c22;
  }
  
  .product-image {
    height: 0;
    padding-bottom: 100%;
    position: relative;
  }
  
  .product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .product-info {
    padding: 0.5rem;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    background: #1c1c22;
  }
  
  .product-name {
    font-size: 0.9rem;
    margin: 0.3rem 0;
    line-height: 1.3;
    max-height: 2.6rem;
    overflow: hidden;
  }
  
  .current-price {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .product-stats {
    gap: 0.3rem;
    margin-top: auto;
  }
  
  .stat-item {
    font-size: 0.7rem;
    padding: 0.1rem 0.2rem;
    flex: 1;
    justify-content: center;
    text-align: center;
  }
  
  .stat-item i {
    font-size: 0.6rem;
  }
  
  /* 移动端发光效果调整 */
  .product-card:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 
      0 10px 20px rgba(0, 0, 0, 0.8),
      0 0 20px 5px rgba(255, 255, 255, 0.8),
      0 0 40px 10px rgba(255, 255, 255, 0.6),
      0 0 60px 15px rgba(128, 102, 204, 0.4);
  }
}

/* 超小屏幕 */
@media (max-width: 374px) {
  .product-card {
    margin: 0 2px;
  }
  
  .product-info {
    padding: 0.4rem;
    min-height: 80px;
  }
  
  .product-name {
    font-size: 0.85rem;
    margin: 0.2rem 0;
  }
  
  .current-price {
    font-size: 0.9rem;
  }
  
  .stat-item {
    font-size: 0.65rem;
    gap: 0.1rem;
  }
  
  .stat-item i {
    font-size: 0.55rem;
  }
}

/* ======================== 商品卡片强力发光效果 (来自card-glow.css) ======================== */
.product-card {
  position: relative !important;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
  z-index: 1 !important;
}

/* 悬停时提升卡片层级，确保光晕不被其他元素遮挡 */
.product-card:hover {
  z-index: 100 !important;
  transform: translateY(-10px) scale(1.05) !important;
}

/* 强力光晕外框 */
.product-card::after {
  content: '' !important;
  position: absolute !important;
  top: -5px !important;
  left: -5px !important;
  right: -5px !important;
  bottom: -5px !important;
  border-radius: 20px !important;
  background: transparent !important;
  border: 3px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: 
    0 0 20px 5px rgba(255, 255, 255, 0.8),
    0 0 40px 10px rgba(128, 102, 204, 0.7),
    0 0 60px 15px rgba(128, 102, 204, 0.5),
    0 0 80px 20px rgba(128, 102, 204, 0.3) !important;
  opacity: 0 !important;
  z-index: -1 !important;
  transition: opacity 0.3s ease !important;
  pointer-events: none !important;
}

/* 悬停时显示强力光晕 */
.product-card:hover::after {
  opacity: 1 !important;
  animation: strongGlow 1.5s infinite alternate !important;
}

/* 卡片底部强力阴影 */
.product-card:hover {
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.8),
    0 0 30px 5px rgba(255, 255, 255, 0.8),
    0 0 50px 10px rgba(255, 255, 255, 0.6),
    0 0 70px 15px rgba(255, 255, 255, 0.4),
    0 0 90px 20px rgba(128, 102, 204, 0.3) !important;
}

/* 强力光晕动画 */
@keyframes strongGlow {
  0% {
    box-shadow: 
      0 0 20px 5px rgba(255, 255, 255, 0.8),
      0 0 40px 10px rgba(128, 102, 204, 0.7),
      0 0 60px 15px rgba(128, 102, 204, 0.5),
      0 0 80px 20px rgba(128, 102, 204, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.8) !important;
  }
  100% {
    box-shadow: 
      0 0 30px 10px rgba(255, 255, 255, 0.9),
      0 0 60px 15px rgba(128, 102, 204, 0.8),
      0 0 90px 20px rgba(128, 102, 204, 0.6),
      0 0 120px 25px rgba(128, 102, 204, 0.4) !important;
    border-color: rgba(255, 255, 255, 1) !important;
  }
}

/* 确保移动端也有效果 */
@media (max-width: 767px) {
  .product-card:hover::after {
    opacity: 1 !important;
    animation: strongGlow 1.5s infinite alternate !important;
  }
  
  .product-card:hover {
    transform: translateY(-5px) scale(1.03) !important;
    box-shadow: 
      0 10px 20px rgba(0, 0, 0, 0.8),
      0 0 20px 5px rgba(255, 255, 255, 0.8),
      0 0 40px 10px rgba(255, 255, 255, 0.6),
      0 0 60px 15px rgba(128, 102, 204, 0.4) !important;
  }
}

/* 主题响应样式 - 确保产品页面能够正确应用主题 */
[data-theme="dark"] .products {
  color: var(--text-color) !important;
}

[data-theme="light"] .products {
  color: var(--text-color) !important;
}

/* 确保所有产品卡片响应主题 */
[data-theme="dark"] .product-card {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-theme="light"] .product-card {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

/* 产品信息区域 */
[data-theme="dark"] .product-info {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
}

[data-theme="light"] .product-info {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
}

/* 筛选区域 */
[data-theme="dark"] .filters-section {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
}

[data-theme="light"] .filters-section {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
}

/* 产品名称 */
[data-theme="dark"] .product-name {
  color: var(--heading-color) !important;
}

[data-theme="light"] .product-name {
  color: var(--heading-color) !important;
}

/* 禁用产品卡片的特殊悬停效果，使用主题的悬停效果 */
[data-theme="dark"] .product-card::after,
[data-theme="light"] .product-card::after {
  display: none !important;
}

[data-theme="dark"] .product-card:hover::after,
[data-theme="light"] .product-card:hover::after {
  display: none !important;
}

/* 确保所有重要的产品页面元素能够响应主题变化 */
[data-theme="dark"] .search-input,
[data-theme="light"] .search-input {
  background: var(--search-bg) !important;
  color: var(--text-color) !important;
}

[data-theme="dark"] .category-btn,
[data-theme="light"] .category-btn {
  background: var(--button-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

[data-theme="dark"] .category-btn.active,
[data-theme="light"] .category-btn.active {
  background: var(--primary-color) !important;
  color: #ffffff !important;
}

/* 覆盖重要的产品卡片效果 */
.product-card {
  animation: none !important;
}

.product-card:hover {
  transform: translateY(-5px) !important;
  box-shadow: var(--shadow) !important;
}

/* 世界地图部分的样式 */
.world-map-section {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  margin-bottom: 2rem;
}

/* 主题响应样式 - 确保产品页面能够正确应用主题 */
[data-theme="dark"] .world-map-section {
  background: var(--card-bg) !important;
}

[data-theme="light"] .world-map-section {
  background: var(--card-bg) !important;
}

/* 添加移动端世界地图容器样式 */
@media (max-width: 768px) {
  .world-map-section {
    width: 100%;
    max-width: 100%;
    padding: 0.5rem;
    margin: 0 auto 1.5rem;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
  }
}
</style>
