<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商家缩写测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .title {
            text-align: center;
            color: #230F48;
            font-size: 2rem;
            margin-bottom: 2rem;
        }
        
        .sellers-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
            padding: 2rem 0;
        }
        
        .seller-card {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border: 2px solid #9333ea;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .seller-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .seller-card.promoted {
            border-color: #8b5cf6;
        }
        
        .seller-card.trusted {
            border-color: #10b981;
        }
        
        .seller-logo {
            width: 100px;
            height: 100px;
            margin: 0 auto 1rem;
            border-radius: 50%;
            overflow: hidden;
            position: relative;
        }
        
        .initials-placeholder {
            font-size: 2.2rem;
            font-weight: 900;
            letter-spacing: 1px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            transition: all 0.3s ease;
            color: #2a2a3a;
            background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
        }
        
        .seller-card.promoted .initials-placeholder {
            background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 100%);
            color: #6b46c1;
            border: 2px solid rgba(139, 92, 246, 0.4);
        }
        
        .seller-card.trusted .initials-placeholder {
            background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
            color: #059669;
            border: 2px solid rgba(16, 185, 129, 0.4);
        }
        
        .seller-card:hover .initials-placeholder {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .seller-name {
            font-size: 1.3rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: #230F48;
        }
        
        .seller-type {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .seller-type.promoted {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            color: white;
        }
        
        .seller-type.trusted {
            background: linear-gradient(135deg, #10b981, #059669);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">商家头像缩写功能测试</h1>
        
        <div class="sellers-grid" id="sellersGrid">
            <!-- 商家卡片将通过JavaScript生成 -->
        </div>
    </div>

    <script>
        // 模拟商家数据
        const sellers = [
            { name: 'Green Garden', type: 'promoted', hasLogo: false },
            { name: 'ToyLand', type: 'promoted', hasLogo: false },
            { name: 'Best Seller', type: 'trusted', hasLogo: false },
            { name: 'Amazon Store', type: 'promoted', hasLogo: false },
            { name: 'Apple Inc', type: 'trusted', hasLogo: false },
            { name: 'Microsoft Corp', type: 'promoted', hasLogo: false },
            { name: 'Google', type: 'trusted', hasLogo: false },
            { name: 'Facebook', type: 'promoted', hasLogo: false },
            { name: '阿里巴巴', type: 'trusted', hasLogo: false },
            { name: '腾讯科技', type: 'promoted', hasLogo: false },
            { name: 'Tesla Motors', type: 'trusted', hasLogo: false },
            { name: 'Netflix', type: 'promoted', hasLogo: false }
        ];

        // 获取缩写的函数（复制自Vue组件）
        function getInitials(name) {
            if (!name || typeof name !== 'string') return 'AG';
            
            const cleanName = name.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '');
            
            if (cleanName.length === 0) return 'AG';
            
            if (/[\u4e00-\u9fa5]/.test(cleanName)) {
                return cleanName.substring(0, 2).toUpperCase();
            }
            
            const words = name.trim().split(/\s+/);
            if (words.length >= 2) {
                return (words[0].charAt(0) + words[1].charAt(0)).toUpperCase();
            } else if (words.length === 1 && words[0].length >= 2) {
                return words[0].substring(0, 2).toUpperCase();
            } else if (words.length === 1 && words[0].length === 1) {
                return (words[0] + words[0]).toUpperCase();
            }
            
            return cleanName.substring(0, 2).toUpperCase();
        }

        // 生成商家卡片
        function generateSellerCards() {
            const grid = document.getElementById('sellersGrid');
            
            sellers.forEach(seller => {
                const initials = getInitials(seller.name);
                
                const card = document.createElement('div');
                card.className = `seller-card ${seller.type}`;
                
                card.innerHTML = `
                    <div class="seller-logo">
                        <div class="initials-placeholder">${initials}</div>
                    </div>
                    <div class="seller-name">${seller.name}</div>
                    <div class="seller-type ${seller.type}">${seller.type}</div>
                `;
                
                grid.appendChild(card);
            });
        }

        // 页面加载完成后生成卡片
        document.addEventListener('DOMContentLoaded', generateSellerCards);
    </script>
</body>
</html>
