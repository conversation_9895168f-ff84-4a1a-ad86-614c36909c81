<template>
  <div class="post-detail-container">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
    
    <!-- 错误信息 -->
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="fetchPostData" class="retry-button">重试</button>
    </div>
    
    <!-- 帖子内容部分 -->
    <div v-else-if="post" class="post-content-container">
      <!-- 帖子图片 -->
      <div v-if="post.images && post.images.length > 0" class="post-image-container">
        <img :src="post.images[0].imageUrl" :alt="post.title" class="post-image">
        </div>
        
      <!-- 帖子标题 -->
      <h2 class="post-title">{{ post.title }}</h2>
      
      <!-- 帖子正文 -->
      <div class="post-text" v-html="formatContent(post.content)"></div>

      <!-- 帖子日期和标签 -->
      <div class="post-meta">
        <span class="post-date">{{ formatDate(post.createTime) }}</span>
        <div class="tags-container">
          <span v-for="tag in post.tags" :key="tag.id" class="tag" :style="{ backgroundColor: getTagColor(tag) }">{{ tag.name }}</span>
        </div>
      </div>
      
      <!-- 帖子操作按钮 -->
      <div class="post-actions">
        <button class="action-button" :class="{'liked': post.isLiked}" @click="likePost">
          <i :class="post.isLiked ? 'fas fa-heart' : 'far fa-heart'"></i>
          <span class="action-count">{{ post.likes || 0 }}</span>
            </button>
        <button class="action-button">
          <i class="far fa-comment"></i>
          <span class="action-count">{{ post.commentCount || 0 }}</span>
            </button>
        <button class="action-button">
          <i class="far fa-eye"></i>
          <span class="action-count">{{ post.views || 0 }}</span>
            </button>
        <button class="action-button" @click="sharePost">
          <i class="fas fa-share"></i>
            </button>
          </div>
            </div>
            
    <!-- 评论输入框 -->
    <div class="comment-input-container">
      <input type="text" placeholder="Type Comment" class="comment-input" v-model="commentText">
      <button class="comment-submit-button" @click="submitComment">
        <i class="fas fa-arrow-right"></i>
                </button>
                </div>
                
    <!-- 评论列表 -->
    <div v-if="comments && comments.length > 0" class="comments-container">
      <!-- 评论项 -->
      <div v-for="comment in comments" :key="comment.id" class="comment">
        <div class="comment-avatar">
          <img v-if="comment.users && comment.users.avatarUrl" :src="comment.users.avatarUrl" alt="用户头像" class="comment-avatar-img">
          <span v-else class="avatar-letter">{{ getAvatarLetter(comment.users) }}</span>
                  </div>
        <div class="comment-content">
          <div class="comment-header">
            <span class="comment-author">{{ comment.users ? comment.users.username : '匿名用户' }}</span>
            <span class="comment-time">{{ formatDate(comment.createdAt) }}</span>
                </div>
          <p class="comment-text">{{ comment.postContent }}</p>
                <div class="comment-actions">
            <button class="comment-action-button" :class="{'liked': comment.isLiked}" @click="likeComment(comment)">
              <i :class="comment.isLiked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i>
              {{ comment.likeCount || 0 }}
                  </button>
            <button class="comment-action-button" @click="replyToComment(comment)">
              <i class="far fa-comment"></i> Reply
                  </button>
            <!-- 只有用户自己发的评论才显示删除按钮 -->
            <button v-if="isUserOwnComment(comment)" class="comment-action-button delete-button" @click="confirmDeleteComment(comment)">
              <i class="far fa-trash-alt"></i> Delete
                  </button>
              </div>
              
          <!-- 回复框 -->
          <div v-if="replyingTo && replyingTo.id === comment.id" class="reply-container">
            <div class="reply-input-container">
              <input type="text" v-model="replyText" placeholder="Reply to comment..." class="reply-input" @keyup.enter="submitReply" />
              <div class="reply-buttons">
                <button class="reply-button submit-button" @click="submitReply">
                  <i class="fas fa-paper-plane"></i>
                </button>
                <button class="reply-button cancel-button" @click="cancelReply">
                  <i class="fas fa-times"></i>
                </button>
            </div>
          </div>
        </div>
        
          <!-- 调试信息 -->
          <!-- <div v-if="comment.children && comment.children.length > 0" style="color: yellow; font-size: 12px;">
            有{{ comment.children.length }}条子评论(children)
        </div>
          <div v-if="comment.replies && comment.replies.length > 0" style="color: green; font-size: 12px;">
            有{{ comment.replies.length }}条子评论(replies)
          </div> -->
        
          <!-- 显示回复数量，点击展开/收起 -->
          <div v-if="(comment.children && comment.children.length > 0) || (comment.replies && comment.replies.length > 0)" 
               class="view-replies" 
               @click="toggleReplies(comment.id)">
            <i :class="isCommentExpanded(comment.id) ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i> 
            {{ isCommentExpanded(comment.id) ? 'Hide Replies' : 'View Replies (' + ((comment.children && comment.children.length) || (comment.replies && comment.replies.length) || 0) + ')' }}
        </div>
        
          <!-- 嵌套的回复评论 -->
          <div v-if="isCommentExpanded(comment.id)" class="nested-replies">
            <!-- 使用children或replies数组，优先使用children -->
            <div v-for="reply in (comment.children || comment.replies || [])" :key="reply.id" class="nested-reply">
              <div class="nested-reply-avatar">
                <img v-if="reply.users && reply.users.avatarUrl" :src="reply.users.avatarUrl" alt="用户头像" class="nested-reply-avatar-img">
                <span v-else class="avatar-letter">{{ getAvatarLetter(reply.users) }}</span>
        </div>
              <div class="nested-reply-content">
                <div class="nested-reply-header">
                  <span class="nested-reply-author">{{ reply.users ? reply.users.username : '匿名用户' }}</span>
                  <span v-if="reply.replyToUser" class="reply-to-indicator">
                    replying to <span class="reply-to-user">@{{ reply.replyToUser.username }}</span>
                  </span>
                  <span class="nested-reply-time">{{ formatDate(reply.createdAt) }}</span>
      </div>
                <p class="nested-reply-text">{{ reply.postContent }}</p>
                <div class="nested-reply-actions">
                  <button class="nested-reply-action-button" :class="{'liked': reply.isLiked}" @click="likeComment(reply)">
                    <i :class="reply.isLiked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i>
                    {{ reply.likeCount || 0 }}
                  </button>
                  <button class="nested-reply-action-button" @click="replyToComment(reply, comment)">
                    <i class="far fa-comment"></i> Reply
                  </button>
                  <!-- 只有用户自己发的评论才显示删除按钮 -->
                  <button v-if="isUserOwnComment(reply)" class="nested-reply-action-button delete-button" @click="confirmDeleteComment(reply)">
                    <i class="far fa-trash-alt"></i> Delete
                  </button>
    </div>
    
                <!-- 二级嵌套回复 -->
                <div v-if="(reply.children && reply.children.length > 0) || (reply.replies && reply.replies.length > 0)" class="second-level-replies">
                  <div v-for="nestedReply in (reply.children || reply.replies || [])" :key="nestedReply.id" class="second-level-reply">
                    <div class="nested-reply-avatar">
                      <img v-if="nestedReply.users && nestedReply.users.avatarUrl" :src="nestedReply.users.avatarUrl" alt="用户头像" class="nested-reply-avatar-img">
                      <span v-else class="avatar-letter">{{ getAvatarLetter(nestedReply.users) }}</span>
      </div>
                    <div class="nested-reply-content">
                      <div class="nested-reply-header">
                        <span class="nested-reply-author">{{ nestedReply.users ? nestedReply.users.username : '匿名用户' }}</span>
                        <span class="reply-to-indicator">
                          replying to <span class="reply-to-user">@{{ reply.users ? reply.users.username : '匿名用户' }}</span>
                        </span>
                        <span class="nested-reply-time">{{ formatDate(nestedReply.createdAt) }}</span>
    </div>
                      <p class="nested-reply-text">{{ nestedReply.postContent }}</p>
                      <div class="nested-reply-actions">
                        <button class="nested-reply-action-button" :class="{'liked': nestedReply.isLiked}" @click="likeComment(nestedReply)">
                          <i :class="nestedReply.isLiked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i>
                          {{ nestedReply.likeCount || 0 }}
                        </button>
                        <button class="nested-reply-action-button" @click="replyToComment(nestedReply, reply)">
                          <i class="far fa-comment"></i> Reply
                        </button>
                        <!-- 只有用户自己发的评论才显示删除按钮 -->
                        <button v-if="isUserOwnComment(nestedReply)" class="nested-reply-action-button delete-button" @click="confirmDeleteComment(nestedReply)">
                          <i class="far fa-trash-alt"></i> Delete
    </button>
  </div>
      </div>
        </div>
          </div>
          </div>
                </div>
              </div>
                  </div>
            </div>
          </div>
          
    <!-- 移动端底部操作栏 -->
    <div v-if="post && isMobile" class="mobile-action-bar">
      <div class="mobile-action-item" @click="likePost">
        <i :class="post.isLiked ? 'fas fa-heart' : 'far fa-heart'" :style="{color: post.isLiked ? '#ff4757' : '#ffffff'}"></i>
        <span class="mobile-action-count">{{ post.likes || 0 }}</span>
          </div>
      <div class="mobile-action-item" @click="scrollToComments">
        <i class="far fa-comment"></i>
        <span class="mobile-action-count">{{ post.commentCount || 0 }}</span>
      </div>
      <div class="mobile-action-item">
        <i class="far fa-eye"></i>
        <span class="mobile-action-count">{{ post.views || 0 }}</span>
      </div>
      <div class="mobile-action-item" @click="sharePost">
        <i class="fas fa-share"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive } from 'vue';
import postsApi from '@/api/posts';

export default {
  name: 'PostDetailView',
  props: {
    id: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      post: null,
      comments: [],
      loading: false,
      error: null,
      commentText: '',
      isCheckingLikes: false,
      viewIncremented: false, // 标记是否已增加浏览量
      viewTimer: null, // 用于存储定时器ID
      replyingTo: null, // 当前正在回复的评论
      parentComment: null, // 如果回复的是二级评论，这里存储其父评论
      replyText: '', // 回复内容
      expandedComments: reactive({}), // 使用reactive创建响应式对象
      isMobile: false // 是否为移动设备
    };
  },
  created() {
    this.resetViewState();
    this.fetchPostData();
    this.checkMobile();
    window.addEventListener('resize', this.checkMobile);
  },
  beforeUnmount() {
    // 组件销毁前清除定时器和事件监听
    this.clearViewTimer();
    window.removeEventListener('resize', this.checkMobile);
  },
  watch: {
    // 监听评论数据变化，自动检查点赞状态
    comments: {
      handler(newComments, oldComments) {
        // 只有当评论数据实际发生变化时才检查点赞状态
        if (newComments && newComments.length > 0 && 
            (!oldComments || 
             oldComments.length !== newComments.length || 
             JSON.stringify(newComments.map(c => c.id)) !== JSON.stringify(oldComments.map(c => c.id)))) {
          this.checkCommentsLikeStatus();
        }
      },
      deep: false // 改为false，只监听数组引用变化，不监听内部属性变化
    },
    // 监听帖子浏览量变化
    'post.views': {
      handler() {
        // 监听浏览量变化，不使用参数
      }
    },
    // 监听路由参数变化
    id: {
      handler(newId, oldId) {
        if (newId !== oldId) {
          this.resetViewState();
          this.fetchPostData();
        }
      },
      immediate: false
    }
  },
  methods: {
    async fetchPostData() {
      this.loading = true;
      this.error = null;
      
      try {
        // 使用getPostDetail接口获取帖子详情
        const response = await postsApi.getPostDetail(this.id);
        
        if (response.code === 200 && response.data) {
          this.post = response.data;
          
          // 检查帖子点赞状态
          await this.checkPostLikeStatus();
          
          // 如果帖子数据中已包含评论，直接使用
          if (this.post.comments && this.post.comments.length > 0) {
            this.comments = this.post.comments;
            // 检查评论点赞状态
            await this.checkCommentsLikeStatus();
              } else {
            // 否则获取评论
            await this.fetchComments();
            // 注意：fetchComments方法内部会调用checkCommentsLikeStatus，所以这里不需要再调用
          }
          
          // 帖子数据加载成功后，开始计时增加浏览量
          this.startViewTimer();
          } else {
            this.error = '未找到该帖子';
        }
      } catch (err) {
        this.error = '加载帖子数据时出错';
      } finally {
        this.loading = false;
      }
    },
    
    async fetchComments() {
      try {
        const response = await postsApi.getPostCommentsWithPaging(parseInt(this.id, 10));
        
        if (response.code === 200 && response.data) {
          // 获取评论列表
          const commentsList = response.data.list || [];
          
          console.log('原始评论数据:', JSON.stringify(commentsList));
          
          // 更新评论列表 - 直接使用接口返回的结构，不做转换
          this.comments = commentsList;
          
          // 重置展开状态
          Object.keys(this.expandedComments).forEach(key => {
            delete this.expandedComments[key];
          });
          
          // 如果有评论且用户已登录，检查点赞状态
          if (this.comments.length > 0) {
            await this.checkCommentsLikeStatus();
          }
        }
      } catch (err) {
        console.error('获取评论失败:', err);
      }
    },
    
    async checkCommentsLikeStatus() {
      // 如果已经在检查中，则不重复调用
      if (this.isCheckingLikes) {
        return;
      }
      
      this.isCheckingLikes = true; // 设置标志位
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，不检查点赞状态
        if (!userId) {
          return;
        }
        
        // 收集所有评论ID，包括所有层级的嵌套评论
        const commentIds = [];
        
        // 递归函数收集所有评论ID
        const collectCommentIds = (comments) => {
          if (!comments || !comments.length) return;
          
          comments.forEach(comment => {
            commentIds.push(comment.id);
            
            // 收集一级回复的ID (使用children属性)
            if (comment.children && comment.children.length) {
              comment.children.forEach(reply => {
                commentIds.push(reply.id);
                
                // 收集二级回复的ID
                if (reply.children && reply.children.length) {
                  reply.children.forEach(nestedReply => {
                    commentIds.push(nestedReply.id);
                  });
                }
              });
                        }
                      });
                  };
                  
        // 收集所有评论ID
        collectCommentIds(this.comments);
        
        if (commentIds.length === 0) {
          return;
        }
        
        console.log('收集到的评论ID:', commentIds);
        
        // 批量检查点赞状态
        const response = await postsApi.checkLikedComments({
          userId: parseInt(userId, 10),
          commentIds
        });
        
        if (response.code === 200 && response.data) {
          // 递归更新评论点赞状态
          const updateLikeStatus = (comments) => {
            if (!comments || !comments.length) return;
            
          comments.forEach(comment => {
              const commentData = response.data[comment.id];
              if (commentData) {
                comment.isLiked = commentData.liked;
                comment.likeCount = commentData.likeCount || 0;
              }
              
              // 更新一级回复的点赞状态 (使用children属性)
              if (comment.children && comment.children.length) {
                comment.children.forEach(reply => {
                  const replyData = response.data[reply.id];
                  if (replyData) {
                    reply.isLiked = replyData.liked;
                    reply.likeCount = replyData.likeCount || 0;
                  }
                  
                  // 更新二级回复的点赞状态
                  if (reply.children && reply.children.length) {
                    reply.children.forEach(nestedReply => {
                      const nestedReplyData = response.data[nestedReply.id];
                      if (nestedReplyData) {
                        nestedReply.isLiked = nestedReplyData.liked;
                        nestedReply.likeCount = nestedReplyData.likeCount || 0;
                      }
                    });
                  }
                });
              }
            });
          };
          
          // 更新所有评论的点赞状态
          updateLikeStatus(this.comments);
        }
      } catch (err) {
        console.error('检查评论点赞状态失败:', err);
      } finally {
        this.isCheckingLikes = false; // 重置标志位
      }
    },
    
    async likePost() {
      if (!this.post) return;
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，提示用户登录
        if (!userId) {
          this.$message.warning('Please login to like this post');
          this.$router.push('/login');
          return;
        }
        
        // 获取当前点赞状态
        const isLiked = this.post.isLiked;
        
        // 立即更新UI状态
        this.post.isLiked = !isLiked;
        if (isLiked) {
          // 如果当前已点赞，取消点赞，点赞数减1
          this.post.likes = Math.max(0, (this.post.likes || 0) - 1);
        } else {
          // 如果当前未点赞，点赞，点赞数加1
          this.post.likes = (this.post.likes || 0) + 1;
        }
        
        // 调用API
        const params = {
          userId: parseInt(userId, 10),
          postId: this.post.id
        };
        
        console.log(`${isLiked ? 'Unlike' : 'Like'} post, params:`, params);
        
        const response = isLiked 
          ? await postsApi.unlikePostNew(params)
          : await postsApi.likePostNew(params);
        
        console.log(`${isLiked ? 'Unlike' : 'Like'} post response:`, response);
        
        if (response.code === 200 && response.data) {
          // 使用接口返回的数据更新点赞状态和点赞数
          this.post.isLiked = !isLiked;
          this.post.likes = response.data.likeCount || this.post.likes;
              } else {
          // 如果API调用失败，恢复原状态
          this.post.isLiked = isLiked;
          this.post.likes = isLiked 
            ? (this.post.likes || 0) + 1 
            : Math.max(0, (this.post.likes || 0) - 1);
          console.error('Like operation failed:', response.msg);
          this.$message.error('Like operation failed, please try again later');
        }
      } catch (err) {
        console.error('Like error:', err);
        this.$message.error('Like operation failed, please try again later');
      }
    },
    
    sharePost() {
      if (!this.post) return;
      
      const postUrl = `${window.location.origin}/client/post/${this.post.id}`;
      navigator.clipboard.writeText(postUrl).then(() => {
        this.$message.success('Link copied to clipboard!');
      }).catch(err => {
        console.error('Failed to copy link:', err);
        this.$message.error('Failed to copy link, please copy manually');
      });
    },
    
    formatDate(dateString) {
      if (!dateString) return '';
      
      const date = new Date(dateString);
      const now = new Date();
      const diff = now - date;
      
      // 小于1分钟
      if (diff < 60 * 1000) {
        return 'just now';
      }
      
      // 小于1小时
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
      }
      
      // 小于24小时
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
      }
      
      // 小于30天
      if (diff < 30 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days} ${days === 1 ? 'day' : 'days'} ago`;
      }
      
      // 格式化为年月日
      const options = { year: 'numeric', month: 'short', day: 'numeric' };
      return date.toLocaleDateString('en-US', options);
    },

    getAvatarLetter(user) {
      if (!user) return '?';
      
      const username = user.username || '';
      return username.charAt(0).toUpperCase();
    },
    
    async submitComment() {
      if (!this.commentText.trim()) {
        this.$message.warning('Comment content cannot be empty');
        return;
      }
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，提示用户登录
        if (!userId) {
          this.$message.warning('Please login to comment');
          this.$router.push('/login');
              return;
            }
            
        const commentData = {
          postId: parseInt(this.id, 10),
          userId: parseInt(userId, 10),
          postContent: this.commentText
        };
        
        const response = await postsApi.createComment(commentData);
            
            if (response.code === 200) {
          this.$message.success('Comment posted successfully');
          this.commentText = ''; // 清空输入框
          
          // 更新评论列表
          await this.fetchComments();
          
          // 更新帖子评论数
          if (this.post) {
            this.post.commentCount = (this.post.commentCount || 0) + 1;
          }
          
          // 检查新评论的点赞状态
          await this.checkCommentsLikeStatus();
            } else {
          this.$message.error('Failed to post comment: ' + response.msg);
        }
      } catch (err) {
        console.error('提交评论出错:', err);
        this.$message.error('Failed to post comment, please try again later');
      }
    },
    
    async likeComment(comment) {
      if (!comment) return;
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，提示用户登录
        if (!userId) {
          this.$message.warning('Please login to like this comment');
          this.$router.push('/login');
          return;
        }
        
        // 获取当前点赞状态
        const isLiked = comment.isLiked;
        
        // 立即更新UI状态
        comment.isLiked = !isLiked;
        if (isLiked) {
          // 如果当前已点赞，取消点赞，点赞数减1
          comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1);
          } else {
          // 如果当前未点赞，点赞，点赞数加1
          comment.likeCount = (comment.likeCount || 0) + 1;
        }
        
        // 调用API
        const params = {
          userId: parseInt(userId, 10),
          commentId: comment.id
        };
        
        console.log(`${isLiked ? 'Unlike' : 'Like'} comment, params:`, params);
        
        const response = isLiked 
          ? await postsApi.unlikeComment(params)
          : await postsApi.likeComment(params);
        
        console.log(`${isLiked ? 'Unlike' : 'Like'} comment response:`, response);
        
        if (response.code === 200 && response.data) {
          // 使用接口返回的数据更新点赞状态和点赞数
          comment.isLiked = !isLiked; // 或者使用response.data.liked如果接口返回了这个值
          comment.likeCount = response.data.likeCount || comment.likeCount;
          
          // 不需要重新检查所有评论的点赞状态，因为我们已经更新了当前评论
          } else {
          // 如果API调用失败，恢复原状态
          comment.isLiked = isLiked;
          comment.likeCount = isLiked 
            ? (comment.likeCount || 0) + 1 
            : Math.max(0, (comment.likeCount || 0) - 1);
          console.error('Like operation failed:', response.msg);
          this.$message.error('Like operation failed, please try again later');
        }
      } catch (err) {
        console.error('Like error:', err);
        this.$message.error('Like operation failed, please try again later');
      }
    },
    
    replyToComment(comment, parentComment = null) {
      // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
      // 如果未登录，提示用户登录
        if (!userId) {
        this.$message.warning('请先登录再回复评论');
        this.$router.push('/login');
          return;
        }
        
      // 设置当前正在回复的评论
      this.replyingTo = comment;
      
      // 确定父评论
      if (parentComment) {
        // 如果提供了父评论，使用它
        this.parentComment = parentComment;
      } else if (comment.parentCommentId) {
        // 如果是嵌套回复，找到其父评论
        this.parentComment = this.comments.find(c => c.id === comment.parentCommentId);
        } else {
        // 如果是主评论，父评论就是它自己
        this.parentComment = comment;
      }
      
      console.log('回复评论:', comment.id, '父评论:', this.parentComment.id);
      
      this.replyText = '';
    },

    cancelReply() {
      // 取消回复
      this.replyingTo = null;
      this.parentComment = null;
      this.replyText = '';
    },
    
    async submitReply() {
      // 如果没有正在回复的评论或回复内容为空，不执行
      if (!this.replyingTo || !this.replyText.trim()) {
        this.$message.warning('Reply content cannot be empty');
        return;
      }
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，提示用户登录
        if (!userId) {
          this.$message.warning('Please login to reply');
          this.$router.push('/login');
          return;
        }
        
        // 准备回复数据
        const replyData = {
          postId: parseInt(this.id, 10),
          userId: parseInt(userId, 10),
          postContent: this.replyText,
          parentCommentId: this.parentComment.id // 使用父评论ID作为parentCommentId
        };
        
        // 如果回复的不是主评论本身，添加replyToUserId
        if (this.replyingTo.id !== this.parentComment.id) {
          replyData.replyToUserId = this.replyingTo.userId;
        }
        
        console.log('提交回复:', replyData);
        
        // 调用回复评论接口
        const response = await postsApi.replyToComment(replyData);
        
        if (response.code === 200) {
          this.$message.success('Reply posted successfully');
          
          // 清空回复框
          this.cancelReply();
          
          // 重新获取评论列表
          await this.fetchComments();
          
          // 自动展开刚刚回复的评论
          if (this.parentComment) {
            this.expandedComments[this.parentComment.id] = true;
          }
        } else {
          this.$message.error('Failed to post reply: ' + response.msg);
        }
      } catch (err) {
        console.error('回复评论失败:', err);
        this.$message.error('Failed to post reply, please try again later');
      }
    },

    async checkPostLikeStatus() {
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录或没有帖子数据，不检查点赞状态
        if (!userId || !this.post) {
          return;
        }
        
        // 检查帖子点赞状态
        const response = await postsApi.batchCheckPostLikeStatus({
          userId: parseInt(userId, 10),
          postIds: [this.post.id]
        });
        
        if (response.code === 200 && response.data) {
          // 更新帖子的点赞状态
          const isLiked = !!response.data[this.post.id];
          this.post.isLiked = isLiked;
        }
      } catch (err) {
        // 错误处理
      }
    },

    startViewTimer() {
      // 如果已经增加过浏览量，不再重复增加
      if (this.viewIncremented) {
        return;
      }
      
      // 清除可能存在的旧定时器
      this.clearViewTimer();
      
      // 设置3秒后增加浏览量的定时器
      this.viewTimer = setTimeout(() => {
        // 确保只调用一次
        if (!this.viewIncremented) {
          this.incrementPostView();
        }
      }, 3000); // 3秒后执行
    },

    clearViewTimer() {
      // 清除定时器
      if (this.viewTimer) {
        clearTimeout(this.viewTimer);
        this.viewTimer = null;
      }
    },
    
    async incrementPostView() {
      // 如果已经增加过浏览量或没有帖子数据，不执行
      if (this.viewIncremented || !this.post || !this.post.id) {
        return;
      }
      
      // 立即标记为已增加，防止重复调用
      this.viewIncremented = true;
      
      try {
        const postId = parseInt(this.post.id, 10);
        const response = await postsApi.incrementPostView(postId);
        
        if (response.code === 200 && response.data !== undefined) {
          // 更新帖子浏览量
          this.post.views = response.data;
        } else {
          // 如果失败，重置标记，允许重试
          this.viewIncremented = false;
        }
        } catch (err) {
        // 如果出错，重置标记，允许重试
        this.viewIncremented = false;
      }
    },

    resetViewState() {
      // 清除定时器
      this.clearViewTimer();
      
      // 重置状态
      this.post = null;
      this.comments = [];
      this.loading = false;
      this.error = null;
      this.commentText = '';
      this.isCheckingLikes = false;
      this.viewIncremented = false;
      
      // 重置展开状态
      Object.keys(this.expandedComments).forEach(key => {
        delete this.expandedComments[key];
      });
    },

    isUserOwnComment(comment) {
      // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
      // 如果未登录或评论没有用户信息，返回false
      if (!userId || !comment || !comment.users) return false;
      
      // 检查评论的用户ID是否与当前登录用户ID相同
      return parseInt(comment.userId, 10) === parseInt(userId, 10);
    },
    
    confirmDeleteComment(comment) {
      // 使用Element UI的确认对话框
      this.$confirm('Are you sure you want to delete this comment?', 'Confirm', {
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        // 用户点击确定，执行删除操作
        this.deleteComment(comment);
      }).catch(() => {
        // 用户点击取消，不做任何操作
        this.$message({
          type: 'info',
          message: 'Delete canceled'
        });
      });
    },
    
    async deleteComment(comment) {
      try {
        const response = await postsApi.deleteComment(comment.id);
        
        if (response.code === 200) {
          this.$message({
            type: 'success',
            message: 'Comment deleted successfully'
          });
          
          // 检查是否为主评论还是嵌套回复
          const isMainComment = !comment.parentCommentId;
          
          if (isMainComment) {
            // 如果是主评论，从评论列表中移除
            this.comments = this.comments.filter(c => c.id !== comment.id);
        } else {
            // 如果是嵌套回复，从父评论的children中移除
            const parentComment = this.comments.find(c => c.id === comment.parentCommentId);
            
            if (parentComment && parentComment.children) {
              parentComment.children = parentComment.children.filter(r => r.id !== comment.id);
              
              // 如果是二级嵌套回复
              if (!parentComment) {
                // 查找所有一级回复
                this.comments.forEach(mainComment => {
                  if (mainComment.children) {
                    mainComment.children.forEach(reply => {
                      if (reply.children) {
                        // 从二级回复中移除
                        reply.children = reply.children.filter(r => r.id !== comment.id);
                      }
                    });
                  }
                });
              }
            }
          }
          
          // 更新帖子评论数
          if (this.post) {
            this.post.commentCount = Math.max(0, (this.post.commentCount || 0) - 1);
          }
        } else {
          this.$message({
            type: 'error',
            message: 'Failed to delete comment: ' + response.msg
          });
        }
      } catch (err) {
        this.$message({
          type: 'error',
          message: 'Failed to delete comment, please try again later'
        });
      }
    },

    async loadMoreReplies(comment) {
      try {
        // 这里应该调用获取更多回复的API
        // 由于API可能有所不同，这里只是一个示例
        // 实际实现需要根据你的API来调整
        this.$message.info(`加载评论ID ${comment.id} 的更多回复功能正在开发中`);
        
        // 示例：如果有专门的获取评论回复的接口，可以这样调用
        /*
        const response = await postsApi.getCommentReplies({
          commentId: comment.id,
          page: Math.ceil(comment.replies.length / 10) + 1,
          pageSize: 10
        });
        
        if (response.code === 200 && response.data) {
          // 将新加载的回复添加到现有回复列表中
          const newReplies = response.data.list || [];
          comment.replies = [...comment.replies, ...newReplies];
          
          // 检查新加载的回复的点赞状态
          await this.checkCommentsLikeStatus();
        }
        */
        } catch (err) {
        this.$message.error('加载更多回复失败，请稍后重试');
      }
    },

    formatContent(content) {
      if (!content) return '';
      
      // 使用组件的isMobile属性
      const isMobile = this.isMobile;
      
      // 1. 处理已有的段落（保留原有的换行）
      let formattedContent = content.replace(/\n{2,}/g, '</p><p>');
      formattedContent = formattedContent.replace(/\n/g, '<br>');
      
      // 2. 如果内容没有明显的段落分隔，根据句子进行智能分段
      if (!content.includes('\n')) {
        // 按句号、问号、感叹号分割，但保留这些标点符号
        const sentences = content.match(/[^.!?。？！]+[.!?。？！]+/g) || [content];
        
        // 如果句子数量大于1，且内容长度超过一定值，进行分段处理
        if (sentences.length > 1 && content.length > 100) {
          let paragraphs = [];
          let currentParagraph = '';
          
          // 每3-5个句子组成一个段落，移动端可以更少一些
          const sentencesPerParagraph = isMobile ? 2 : 4;
          const lengthThreshold = isMobile ? 100 : 150;
          
          sentences.forEach((sentence, index) => {
            currentParagraph += sentence;
            
            // 根据句子长度和数量决定何时分段
            if ((index + 1) % sentencesPerParagraph === 0 || 
                (currentParagraph.length > lengthThreshold && (index + 1) % 2 === 0) ||
                index === sentences.length - 1) {
              paragraphs.push(currentParagraph);
              currentParagraph = '';
            }
          });
          
          // 如果最后还有未添加的段落
          if (currentParagraph) {
            paragraphs.push(currentParagraph);
          }
          
          // 将段落连接起来
          formattedContent = paragraphs.join('</p><p>');
        }
      }
      
      // 3. 处理特殊格式（如果需要）
      // 加粗文本 **文本** -> <strong>文本</strong>
      formattedContent = formattedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      
      // 斜体文本 *文本* -> <em>文本</em>
      formattedContent = formattedContent.replace(/\*(.*?)\*/g, '<em>$1</em>');
      
      // 4. 处理链接
      formattedContent = formattedContent.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
      
      // 5. 确保内容被包裹在段落标签中
      if (!formattedContent.startsWith('<p>')) {
        formattedContent = '<p>' + formattedContent;
      }
      if (!formattedContent.endsWith('</p>')) {
        formattedContent = formattedContent + '</p>';
      }
      
      // 6. 移动端特殊处理 - 为每个段落添加一个特殊的类
      if (isMobile) {
        formattedContent = formattedContent.replace(/<p>/g, '<p class="mobile-paragraph">');
      }
      
      return formattedContent;
    },

    isCommentExpanded(commentId) {
      return !!this.expandedComments[commentId];
    },

    toggleReplies(commentId) {
      console.log('切换评论展开状态:', commentId, !this.expandedComments[commentId]);
      this.expandedComments[commentId] = !this.expandedComments[commentId];
    },

    getTagColor(tag) {
      // 预定义的颜色数组
      const colors = [
        '#3a4a9c', // 原始的蓝色
        '#6a359c', // 紫色
        '#9c3535', // 红色
        '#359c6a', // 绿色
        '#9c7635', // 橙色
        '#359c9c', // 青色
        '#9c359c', // 品红
        '#5a9c35', // 黄绿色
        '#9c5a35', // 棕色
        '#35359c'  // 深蓝色
      ];
      
      // 如果有id，使用id来选择颜色
      if (tag.id) {
        return colors[tag.id % colors.length];
      }
      
      // 如果有name但没有id，使用name的字符和来选择颜色
      if (tag.name) {
        let sum = 0;
        for (let i = 0; i < tag.name.length; i++) {
          sum += tag.name.charCodeAt(i);
        }
        return colors[sum % colors.length];
      }
      
      // 默认返回第一个颜色
      return colors[0];
    },

    checkMobile() {
      this.isMobile = window.innerWidth <= 768;
    },

    scrollToComments() {
      // 找到评论区域元素
      const commentsContainer = document.querySelector('.comments-container');
      // 如果找不到评论区域，则滚动到评论输入框
      const commentInput = document.querySelector('.comment-input-container');
      
      if (commentsContainer) {
        // 滚动到评论区域
        commentsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } else if (commentInput) {
        // 滚动到评论输入框
        commentInput.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  }
}
</script>

<style scoped>
.post-detail-container {
  width: 100%;
  max-width: 850px;
  margin: 0 auto;
  padding: 0;
  color: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #1a0b2e;
}

.post-content-container {
  background-color: #1e1033;
  border-radius: 12px;
  padding: 20px 0 24px 0;
  margin-bottom: 16px;
  overflow: hidden;
}

.post-image-container {
  width: 100%;
  margin-bottom: 16px;
}

.post-image {
  width: 100%;
  max-height: 500px;
  object-fit: contain;
  display: block;
}

.post-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 16px 24px;
  line-height: 1.3;
  color: #ffffff;
}

.post-text {
  font-size: 17px;
  line-height: 1.8;
  margin: 0 16px 24px;
  color: #ffffff;
  letter-spacing: 0.01em;
}

.post-text p {
  margin-bottom: 32px;
  padding-bottom: 8px;
  line-height: 1.8;
}

.post-text p:last-child {
  margin-bottom: 0;
}

.post-text a {
  color: #b99dff;
  text-decoration: none;
}

.post-text a:hover {
  text-decoration: underline;
}

.post-text strong {
  font-weight: 600;
  color: #ffffff;
}

.post-text em {
  font-style: italic;
  color: #d0c0e0;
}

.post-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 0 16px 16px;
}

.post-date {
  font-size: 14px;
  color: #9d8ab0;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background-color: #3a4a9c;
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  display: inline-block;
}

.post-actions {
  display: flex;
  padding: 0 16px;
  margin-top: 16px;
}

.action-button {
  background: none;
  border: none;
  color: #9d8ab0;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  margin-right: 24px;
}

.action-button.liked {
  color: #6a359c;
}

.action-button.liked i {
  color: #6a359c;
}

.action-button i {
  margin-right: 6px;
}

.action-count {
  font-size: 14px;
  color: #9d8ab0;
}

.comment-input-container {
  display: flex;
  margin-bottom: 16px;
  background-color: #1e1033;
  border-radius: 30px;
  padding: 5px;
}

.comment-input {
  flex: 1;
  background: none;
  border: none;
  color: #ffffff;
  padding: 10px 15px;
  font-size: 16px;
  outline: none;
}

.comment-input::placeholder {
  color: #9d8ab0;
}

.comment-submit-button {
  background-color: #6a359c;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
}

.comments-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comment {
  display: flex;
  gap: 12px;
  background-color: #1e1033;
  border-radius: 12px;
  padding: 12px;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #6a359c;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.avatar-letter {
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.comment-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.comment-author {
  font-weight: 600;
  color: #b99dff;
  font-size: 14px;
}

.comment-time {
  color: #9d8ab0;
  font-size: 12px;
}

.comment-text {
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 14px;
  color: #e0e0e0;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-action-button {
  background: none;
  border: none;
  color: #9d8ab0;
  font-size: 12px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.comment-action-button.liked {
  color: #6a359c;
}

.comment-action-button.liked i {
  color: #6a359c;
}

.comment-action-button i {
  font-size: 14px;
}

.comment-action-button.delete-button {
  color: #ff6b6b;
}

.comment-action-button.delete-button:hover {
  color: #ff4757;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

.loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffffff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  padding: 20px;
}

.error-message {
  color: #ff6b6b;
  margin-bottom: 20px;
  text-align: center;
}

.retry-button {
  background-color: #6a359c;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  color: white;
  font-size: 16px;
  cursor: pointer;
}

.reply-container {
  margin-top: 12px;
}

.reply-input-container {
  display: flex;
  background-color: #2a1a47;
  border-radius: 20px;
  padding: 5px;
}

.reply-input {
  flex: 1;
  background: none;
  border: none;
  color: #ffffff;
  padding: 8px 12px;
  font-size: 14px;
  outline: none;
}

.reply-input::placeholder {
  color: #9d8ab0;
}

.reply-buttons {
  display: flex;
}

.reply-button {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #9d8ab0;
}

.reply-button.submit-button {
  background-color: #6a359c;
  color: white;
  margin-right: 5px;
}

.reply-button.cancel-button {
  background-color: #3a2a55;
  color: #d0c0e0;
}

.nested-replies {
  margin-top: 8px;
  padding-left: 12px;
  border-left: 2px solid #3a2a55;
}

.nested-reply {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 8px;
  background-color: #2a1a47;
}

.nested-reply-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #6a359c;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.nested-reply-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.nested-reply-content {
  flex: 1;
}

.nested-reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.nested-reply-author {
  font-weight: 600;
  color: #b99dff;
  font-size: 13px;
  margin-right: 6px;
}

.reply-to-indicator {
  color: #9d8ab0;
  font-size: 12px;
  margin-right: 6px;
}

.reply-to-user {
  color: #b99dff;
}

.nested-reply-time {
  color: #9d8ab0;
  font-size: 12px;
  margin-left: auto;
}

.nested-reply-text {
  margin-bottom: 6px;
  line-height: 1.4;
  font-size: 13px;
  color: #e0e0e0;
}

.nested-reply-actions {
  display: flex;
  gap: 12px;
}

.nested-reply-action-button {
  background: none;
  border: none;
  color: #9d8ab0;
  font-size: 11px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 3px;
}

.nested-reply-action-button.liked {
  color: #6a359c;
}

.nested-reply-action-button.liked i {
  color: #6a359c;
}

.view-replies {
  margin-top: 12px;
  color: #b99dff;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 0;
}

.view-replies:hover {
  color: #d0c0e0;
}

.second-level-replies {
  margin-top: 10px;
  padding-left: 10px;
  border-left: 2px solid #3a2a55;
}

.second-level-reply {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px;
  border-radius: 8px;
  background-color: #251640;
}

/* 移动端样式优化 */
@media (max-width: 768px) {
  .post-detail-container {
    padding: 0 8px;
  }
  
  .post-content-container {
    padding: 15px 0 20px 0;
    margin-bottom: 12px;
    border-radius: 10px;
  }
  
  .post-text {
    font-size: 15px;
    line-height: 1.6;
    margin: 0 12px 20px;
  }
  
  .post-text p {
    margin-bottom: 28px;  /* 增加段落底部间距 */
    padding-bottom: 8px;  /* 增加内部底部间距 */
  }
  
  /* 移动端段落样式 - 模拟截图效果 */
  .post-text p.mobile-paragraph {
    padding-left: 12px;
    border-left: 4px solid #6a359c;
    margin-left: 5px;
    margin-top: 25px;  /* 增加段落顶部间距 */
    position: relative;  /* 为添加额外分隔符做准备 */
    padding-top: 10px;  /* 增加段落顶部内边距 */
  }
  
  /* 为段落之间添加额外的视觉分隔 */
  .post-text p.mobile-paragraph:not(:first-child)::before {
    content: "";
    position: absolute;
    top: -18px;
    left: -40px;
    width: 80px;
    height: 2px;
    background-color: #3a2a55;
  }
  
  /* 添加装饰点 */
  .post-text p.mobile-paragraph:not(:first-child)::after {
    content: "";
    position: absolute;
    top: -18px;
    left: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #6a359c;
  }
  
  /* 第一个段落的特殊样式 */
  .post-text p.mobile-paragraph:first-child {
    margin-top: 10px;
  }
  
  /* 最后一个段落的特殊样式 */
  .post-text p.mobile-paragraph:last-child {
    margin-bottom: 15px;
  }
  
  /* 移动端列表样式 */
  .post-text ul, .post-text ol {
    padding-left: 20px;
    margin-bottom: 28px;  /* 与段落保持一致 */
  }
  
  /* 移动端标题样式 */
  .post-title {
    font-size: 22px;
    margin: 0 12px 18px;
    line-height: 1.3;
  }
  
  /* 移动端评论样式 */
  .comment {
    padding: 10px 8px;
  }
  
  .comment-text {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 6px;
  }
  
  .comment-actions {
    flex-wrap: wrap;
  }
  
  .comment-action-button {
    font-size: 11px;
    margin-right: 12px;
    margin-bottom: 4px;
  }
  
  /* 移动端嵌套回复样式 */
  .nested-replies {
    margin-top: 6px;
    padding-left: 8px;
  }
  
  .nested-reply {
    padding: 6px;
    margin-bottom: 6px;
  }
  
  .nested-reply-text {
    font-size: 12px;
    line-height: 1.4;
  }
  
  .nested-reply-actions {
    gap: 8px;
  }
  
  /* 移动端查看回复按钮 */
  .view-replies {
    font-size: 12px;
    margin-top: 8px;
  }
  
  /* 移动端评论输入框 */
  .comment-input-container {
    margin-bottom: 12px;
    border-radius: 25px;
    padding: 3px;
  }
  
  .comment-input {
    padding: 8px 12px;
  font-size: 14px;
  }
  
  .comment-submit-button {
    width: 35px;
    height: 35px;
  }
  
  /* 移动端回复输入框 */
  .reply-input-container {
    padding: 3px;
    border-radius: 18px;
  }
  
  .reply-input {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .reply-button {
    width: 28px;
    height: 28px;
  }
  
  /* 移动端头像大小 */
  .comment-avatar {
  width: 32px;
  height: 32px;
}

  .nested-reply-avatar {
    width: 24px;
    height: 24px;
}
}

/* 移动端底部操作栏 */
.mobile-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #1a0b2e;
  padding: 12px 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  border-top: 1px solid #3a2a55;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

.mobile-action-item {
  cursor: pointer;
  color: #ffffff;
  font-size: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 15px;
}

.mobile-action-count {
  font-size: 12px;
  color: #9d8ab0;
  margin-top: 4px;
}

/* 确保底部有足够的空间，避免内容被底部栏遮挡 */
@media (max-width: 768px) {
  .post-detail-container {
    padding-bottom: 70px;
  }
}
</style> 