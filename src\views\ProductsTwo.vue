<template>
  <div class="products-two">
    <!-- 使用封装的星空背景组件 -->
    <StarryBackground />
    
    <!-- 世界地图组件 -->
    <div class="world-map-section">
      <WorldMap />
    </div>
    
    <!-- 搜索框组件 -->
    <div class="search-container" ref="searchContainerRef">
      <div class="search-icon">
        <i class="fas fa-search"></i>
      </div>
      <input 
        type="text" 
        class="search-input" 
        placeholder="Search" 
        v-model="searchQuery"
        @keyup.enter="handleSearch"
      />
      <div class="camera-icon">
        <i class="fas fa-camera"></i>
      </div>
    </div>
    
    <!-- 移动端分类和过滤按钮 -->
    <div class="mobile-filter-category" v-if="isMobile">
      <div class="filter-icon" @click="toggleFilterDropdown">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotate': showFilterDropdown }">
          <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
        </svg>
      </div>

      <button class="category-btn" @click="toggleCategoryMenu">
        CATEGORY
      </button>

      <!-- 移动端过滤下拉框 -->
      <div v-if="showFilterDropdown && isMobile" class="mobile-filter-backdrop" @click="showFilterDropdown = false"></div>
      <div v-if="showFilterDropdown && isMobile" class="mobile-filter-dropdown">
        <div class="filter-options">
          <button class="filter-option" :class="{ 'active': activeFilter === 'recommend' }" @click="setActiveFilter('recommend')">
            <span>Recommend</span>
          </button>
          <button class="filter-option" :class="{ 'active': activeFilter === 'favorite' }" @click="setActiveFilter('favorite')">
            <span>Favorite</span>
          </button>
          <button class="filter-option" :class="{ 'active': activeFilter === 'amountSold' }" @click="setActiveFilter('amountSold')">
            <span>Amount Sold</span>
          </button>
          <button class="filter-option" :class="{ 'active': activeFilter === 'price' }" @click="setActiveFilter('price')">
            <span>Price</span>
          </button>
          <div class="filter-option seller-option" :class="{ 'active': activeFilter === 'seller' }" @click="setActiveFilter('seller')">
            <span>Seller</span>
            <div class="arrow-container">
              <svg class="arrow-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('asc')">
                <polyline points="18 15 12 9 6 15"></polyline>
              </svg>
              <svg class="arrow-down" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('desc')">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </div>
          </div>
          <button class="filter-option refresh" @click="refreshFilter">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="refresh-icon">
              <path d="M23 4v6h-6"></path>
              <path d="M20.49 15a9 9 0 11-2.12-9.36L23 10"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>

    <div class="category-filter">
      <!-- 添加背景遮罩 - 只在PC端显示 -->
      <div v-if="showFilterDropdown && !isMobile" class="filter-dropdown-backdrop" @click="showFilterDropdown = false"></div>

      <!-- PC端过滤图标 -->
      <div class="filter-icon" v-if="!isMobile" @click="toggleFilterDropdown">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" :class="{ 'rotate': showFilterDropdown }">
          <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
        </svg>
      </div>

      <!-- PC端过滤下拉框 -->
      <div v-if="showFilterDropdown && !isMobile" class="filter-dropdown">
        <div class="filter-options">
          <button class="filter-option" :class="{ 'active': activeFilter === 'recommend' }" @click="setActiveFilter('recommend')">
            <span>Recommend</span>
          </button>
          <button class="filter-option" :class="{ 'active': activeFilter === 'favorite' }" @click="setActiveFilter('favorite')">
            <span>Favorite</span>
          </button>
          <button class="filter-option" :class="{ 'active': activeFilter === 'amountSold' }" @click="setActiveFilter('amountSold')">
            <span>Amount Sold</span>
          </button>
          <button class="filter-option" :class="{ 'active': activeFilter === 'price' }" @click="setActiveFilter('price')">
            <span>Price</span>
          </button>
          <div class="filter-option seller-option" :class="{ 'active': activeFilter === 'seller' }" @click="setActiveFilter('seller')">
            <span>Seller</span>
            <div class="arrow-container">
              <svg class="arrow-up" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('asc')">
                <polyline points="18 15 12 9 6 15"></polyline>
              </svg>
              <svg class="arrow-down" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" @click.stop="setSortDirection('desc')">
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </div>
          </div>
          <button class="filter-option refresh" @click="refreshFilter">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="refresh-icon">
              <path d="M23 4v6h-6"></path>
              <path d="M20.49 15a9 9 0 11-2.12-9.36L23 10"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- PC端分类标签 -->
      <div class="filter-chips-container" v-if="!isMobile">
        <div class="filter-chips">
          <div
            v-for="(category, index) in categories"
            :key="`cat-${index}`"
            class="filter-chip"
            :class="{ 'active': selectedCategory === category.id }"
            @click="toggleCategoryDropdown(category.id)"
          >
            {{ category.name }}
            <span v-if="selectedCategory === category.id" class="dropdown-arrow">▼</span>
            <span v-else class="dropdown-arrow">▼</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 背景遮罩 - 当分类下拉菜单打开时显示 -->
    <div class="dropdown-backdrop" v-if="isMobile && selectedCategory !== null" @click="selectedCategory = null"></div>
    
    <!-- 品牌下拉菜单 -->
    <div 
      class="brands-dropdown-container" 
      v-if="selectedCategory !== null" 
      :style="dropdownPosition"
      :class="{ 'mobile-dropdown': isMobile }"
    >
      <div class="brands-dropdown">
        <div class="dropdown-header">
          <span>{{ getCategoryName(selectedCategory) }}</span>
          <button class="close-btn" @click="selectedCategory = null">✕</button>
        </div>
        <div v-if="categoryBrands.length > 0">
          <div 
            v-for="(brand, bIndex) in categoryBrands" 
            :key="`brand-${bIndex}`" 
            class="brand-dropdown-item"
            :class="{ 'active': selectedBrand === brand.id }"
            @click="selectBrand(brand.id)"
          >
            {{ brand.name }}
          </div>
        </div>
        <div v-else class="empty-brands">
          <p>暂无品牌数据</p>
        </div>
      </div>
    </div>
    
    <!-- 产品卡片网格 -->
    <div class="products-grid-container">
      <!-- 加载中状态 -->
      <div v-if="isLoading" class="loading-spinner-container">
        <div class="loading-spinner"></div>
        <p>Loading products...</p>
      </div>
      
      <!-- 错误状态 -->
      <div v-else-if="error" class="error-message">
        {{ error }}
        <button @click="fetchProducts">Retry</button>
      </div>
      
      <!-- 产品网格 -->
      <div v-else class="products-grid">
        <ProductCard 
          v-for="product in products" 
          :key="product.id" 
          :product="product"
          :showTag="product.showTag"
          :showCheck="product.showCheck"
          @collect-updated="handleCollectUpdated"
          @like-updated="handleLikeUpdated"
        />
      </div>
      
      <!-- 加载更多时的宇宙风格加载器 -->
      <div v-if="isLoadingMore" class="load-more-container">
        <div class="cosmic-loader">
          <div class="cosmic-loader-ring"></div>
          <div class="cosmic-loader-stars">
            <div class="cosmic-star" v-for="n in 8" :key="n"></div>
          </div>
        </div>
      </div>
      
      <!-- 没有更多产品提示 -->
      <div v-if="!isLoading && noMoreProducts && products.length > 0" class="no-more-products">
        <div class="no-more-icon">
          <i class="fas fa-satellite"></i>
        </div>
        <p>No more products available</p>
        <div class="cosmic-line"></div>
      </div>
    </div>
  </div>
</template>

<script>
import StarryBackground from '@/components/StarryBackground.vue'
import WorldMap from '@/components/WorldMap.vue'
import ProductCard from '@/components/ProductCard.vue'
import productsApi from '@/api/products'
import apiClient from '@/services/api'
import { ref, onMounted, onUnmounted } from 'vue'

export default {
  name: 'ProductsTwo',
  components: {
    StarryBackground,
    WorldMap,
    ProductCard
  },
  setup() {
    const searchQuery = ref('')
    const isDarkMode = ref(false)
    const categories = ref([])
    const selectedCategory = ref(null)
    const categoryBrands = ref([])
    const selectedBrand = ref(null)
    const brandsByCategory = ref({}) // 存储每个分类对应的品牌列表
    const isMobile = ref(window.innerWidth <= 480)
    const showFilterDropdown = ref(false)
    const activeFilter = ref('recommend')
    const sortDirection = ref('asc')
    const searchContainerRef = ref(null)
    const themeObserver = ref(null)
    
    // 下拉菜单位置
    const dropdownPosition = ref({
      position: 'absolute',
      top: '0px',
      left: '0px',
      bottom: 'auto',
      width: 'auto'
    });
    
    // 添加API相关数据
    const products = ref([]);
    const filteredProducts = ref([]);
    const displayedProducts = ref([]);
    const isLoading = ref(false);
    const isLoadingMore = ref(false);
    const error = ref(null);
    const noMoreProducts = ref(false);
    const totalProducts = ref(0); // 服务器返回的总条数
    
    // 添加分页相关数据
    const currentPage = ref(1);
    const itemsPerPage = ref(16);
    const apiCurrentPage = ref(1);
    const apiPageSize = ref(20);
    const hasMorePages = ref(true);
    
    // 添加过滤相关数据
    const priceRange = ref({
      min: null,
      max: null
    });
    const isRequestPending = ref(false);
    
    // 添加滚动监听相关数据
    const scrollThreshold = ref(window.innerWidth <= 768 ? 300 : 500); // 根据设备类型设置不同的阈值
    const isScrollListenerActive = ref(false); // 是否已添加滚动监听
    const lastScrollTop = ref(0); // 记录上次滚动位置
    
    /**
     * 获取分类列表
     */
    async function fetchCategories() {
      try {
        const response = await productsApi.getAllCategories();
        if (response.code === 200 && response.data) {
          categories.value = response.data.map(category => ({
            id: category.categoryId || category.id,
            name: category.name
          }));
        } else {
          console.error('Error getting categories:', response);
          categories.value = [];
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        categories.value = [];
      }
    }
    
    /**
     * 获取商品列表
     */
    async function fetchProducts(isLoadMore = false) {
      console.log('🔥 fetchProducts被调用，当前选中分类:', selectedCategory.value, '是否加载更多:', isLoadMore);
      console.log('🔥 调用时的排序状态:', activeFilter.value, sortDirection.value);
      
      let newProducts = []; // 将变量声明移到方法开头，确保整个方法都能访问
      
      try {
        if (!isLoadMore) {
          isLoading.value = true;
          apiCurrentPage.value = 1; // 重置API页码
          hasMorePages.value = true; // 重置分页状态
          // 重置错误状态
          error.value = null;
        } else {
          isLoadingMore.value = true;
        }
        
        noMoreProducts.value = false; // 重置状态
        let response;
        
        // 使用本地变量存储当前选中分类，避免请求过程中被修改
        const selectedCat = selectedCategory.value;
        const selectedBrd = selectedBrand.value;
        
        // 构建分页参数
        const paginationParams = {
          page: apiCurrentPage.value,
          pageSize: apiPageSize.value
        };
        
        // 判断是否有选中的分类
        if (selectedCat || selectedBrd) {
          console.log('根据分类获取商品:', selectedCat, '页码:', apiCurrentPage.value);
          console.log('根据品牌获取商品:', selectedBrd);
          
          const categoryParams = {
            minPrice: priceRange.value.min,
            maxPrice: priceRange.value.max,
            ...paginationParams
          };
          
          response = await productsApi.getProductsByCategory(selectedCat, selectedBrd, categoryParams);
        } else {
          // 搜索商品或获取所有商品，使用相同的API
          const params = {
            ...paginationParams
          };
          
          // 只在有搜索关键词时添加keyword参数
          if (searchQuery.value && searchQuery.value.trim()) {
            console.log('根据关键词搜索商品:', searchQuery.value, '页码:', apiCurrentPage.value);
            params.keyword = searchQuery.value.trim();
          } else {
            console.log('获取所有商品，页码:', apiCurrentPage.value);
          }
          
          // 添加价格范围参数(如果有)
          if (priceRange.value.min !== null) {
            params.minPrice = priceRange.value.min;
          }
          if (priceRange.value.max !== null) {
            params.maxPrice = priceRange.value.max;
          }
          
          response = await productsApi.searchProducts(params);
        }
        
        console.log('API响应:', response);
        
        if (response.code === 200 && response.data) {
          // 处理新的数据格式，数据在 data.list 中
          const dataList = response.data.list || response.data; // 兼容两种数据格式
          const totalCount = response.data.total || 0; // 获取总条数
          
          newProducts = dataList.map(item => ({
            ...item,
            id: item.productId || item.id,
            name: item.name,
            price: item.price,
            imageUrl: item.mainImage || item.image,
            platform: item.platform || 'Unknown',
            likes: item.likes || 0,
            views: item.views || 0,
            comments: Array.isArray(item.comments) ? item.comments : [],
            showTag: Math.random() > 0.5, // 随机展示标签
            showCheck: Math.random() > 0.7, // 随机展示勾选框
            isCollected: false
          }));
          
          if (!isLoadMore) {
            // 首次加载，替换产品列表
            products.value = newProducts;
            filteredProducts.value = [...products.value];
            totalProducts.value = totalCount; // 使用服务器返回的总条数
            
            // 应用当前的排序状态（如果有）
            applySortingIfNeeded();
            
            // 重置分页并加载第一页
            currentPage.value = 1;
            displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);
          } else {
            // 加载更多，追加到现有产品列表
            products.value = [...products.value, ...newProducts];
            filteredProducts.value = [...products.value];
            // totalProducts 保持为服务器返回的总条数，不累加
            
            // 应用当前的排序状态（如果有）
            applySortingIfNeeded();
            
            // 重新计算需要显示的产品数量（保持之前显示的数量 + 新的一页）
            const newDisplayCount = displayedProducts.value.length + itemsPerPage.value;
            const actualDisplayCount = Math.min(newDisplayCount, filteredProducts.value.length);
            displayedProducts.value = filteredProducts.value.slice(0, actualDisplayCount);
            
            console.log(`加载更多后重新计算显示产品，当前显示: ${displayedProducts.value.length} 个`);
          }
          
          // 根据总条数和当前已加载的数据量判断是否还有更多页
          const currentLoadedCount = products.value.length;
          const hasMoreData = currentLoadedCount < totalCount;
          
          if (!hasMoreData || newProducts.length < apiPageSize.value) {
            hasMorePages.value = false;
            noMoreProducts.value = true;
          } else {
            hasMorePages.value = true;
            apiCurrentPage.value++; // 为下次加载更多准备
          }
          
          console.log(`数据加载完成: 当前${currentLoadedCount}条，总共${totalCount}条，还有更多: ${hasMorePages.value}`);
        } else {
          console.error('获取产品错误:', response.msg);
          if (!isLoadMore) {
            products.value = [];
            filteredProducts.value = [];
            displayedProducts.value = [];
            totalProducts.value = 0; // 重置总条数
            // 设置错误消息
            error.value = `Failed to load products: ${response.msg || 'Unknown error'}`;
          }
          hasMorePages.value = false;
          noMoreProducts.value = true;
        }

        // 检查每个商品的收藏状态
        if (localStorage.getItem('isLoggedIn')) {
          // 只检查新加载的商品
          const productsToCheck = isLoadMore ? newProducts : products.value;
          await checkCollectionStatus(productsToCheck);
        }

        // 检查每个商品的点赞状态
        if (localStorage.getItem('isLoggedIn')) {
          // 只检查新加载的商品
          const productsToCheck = isLoadMore ? newProducts : products.value;
          await checkLikeStatus(productsToCheck);
        }

      } catch (err) {
        console.error('获取产品错误:', err);
        if (!isLoadMore) {
          products.value = [];
          filteredProducts.value = [];
          displayedProducts.value = [];
          totalProducts.value = 0; // 重置总条数
          // 设置错误消息
          error.value = `Failed to load products: ${err.message || 'Unknown error'}`;
        }
        hasMorePages.value = false;
        noMoreProducts.value = true;
      } finally {
        if (!isLoadMore) {
          isLoading.value = false;
        } else {
          isLoadingMore.value = false;
        }
        // 如果是通过按钮触发的请求，重置请求标志
        if (!isLoadMore) {
          isRequestPending.value = false;
        }
      }
    }
    
    // 检查商品是否被收藏
    async function checkCollectionStatus(products) {
      if (!localStorage.getItem('isLoggedIn')) return;
      
      try {
        // 获取所有商品ID
        const productIds = products.map(p => p.id);
        
        // 批量检查收藏状态
        const response = await apiClient.post(`/omg/collect/isCollected`, {
          userId: JSON.parse(localStorage.getItem('userInfo')).userId,
          productIds: productIds
        });
        
        if (response.code === 200 && response.data) {
          // 更新每个商品的收藏状态
          products.forEach(product => {
            product.isCollected = response.data[product.id] || false;
          });
        }
      } catch (error) {
        console.error('批量检查收藏状态失败:', error);
      }
    }
    
    // 检查商品是否被点赞
    async function checkLikeStatus(products) {
      if (!localStorage.getItem('isLoggedIn')) return;
      
      try {
        // 获取所有产品ID
        const productIds = products.map(p => p.id);
        
        // 使用修改后的API批量检查
        const response = await productsApi.checkLikeToday(productIds);
        
        if (response && response.code === 200 && response.data) {
          // 更新每个产品的点赞状态
          products.forEach(product => {
            product.isLiked = response.data[product.id] || false;
          });
          
          console.log('批量点赞状态检查完成');
        } else {
          console.warn('批量检查未返回有效数据，回退到单个检查');
        }
      } catch (error) {
        console.error('批量检查点赞状态失败:', error);
      }
    }
    
    // 根据当前排序状态应用排序
    function applySortingIfNeeded() {
      console.log('🎯 applySortingIfNeeded被调用，当前排序状态:', activeFilter.value, sortDirection.value);
      console.log('🎯 filteredProducts长度:', filteredProducts.value.length);
      
      if (activeFilter.value === 'amountSold') {
        // 按销量排序
        filteredProducts.value.sort((a, b) => b.amountSold - a.amountSold);
        console.log('✅ 应用销量排序完成');
      } else if (activeFilter.value === 'price') {
        // 按价格排序
        if (sortDirection.value === 'asc') {
          filteredProducts.value.sort((a, b) => a.price - b.price);
          console.log('✅ 应用价格升序排序完成');
        } else {
          filteredProducts.value.sort((a, b) => b.price - a.price);
          console.log('✅ 应用价格降序排序完成');
        }
      } else if (activeFilter.value === 'seller') {
        // 按卖家排序
        filteredProducts.value.sort((a, b) => {
          if (sortDirection.value === 'asc') {
            return a.platform.localeCompare(b.platform);
          } else {
            return b.platform.localeCompare(a.platform);
          }
        });
        console.log('✅ 应用卖家排序完成');
      } else if (activeFilter.value === 'favorite') {
        // 按收藏量排序（假设有likes或类似字段）
        filteredProducts.value.sort((a, b) => b.likes - a.likes);
        console.log('✅ 应用收藏排序完成');
      }
    }

    // 检测当前主题
    const checkCurrentTheme = () => {
      // 检查HTML元素的data-theme属性
      const htmlElement = document.documentElement;
      const theme = htmlElement.getAttribute('data-theme');
      isDarkMode.value = theme === 'dark';
      
      // 应用主题样式到搜索框
      applySearchTheme();
      
      console.log('当前主题:', theme, '是暗色系:', isDarkMode.value);
    };
    
    // 应用搜索框主题样式
    const applySearchTheme = () => {
      if (searchContainerRef.value) {
        if (isDarkMode.value) {
          searchContainerRef.value.classList.add('dark-theme');
          searchContainerRef.value.classList.remove('light-theme');
        } else {
          searchContainerRef.value.classList.add('light-theme');
          searchContainerRef.value.classList.remove('dark-theme');
        }
      }
    };
    
    // 处理主题变化
    const handleThemeChange = (data) => {
      if (data && typeof data.isDarkMode !== 'undefined') {
        // 获取当前主题
        isDarkMode.value = data.isDarkMode;
        
        // 手动应用主题到关键元素
        const productsElement = document.querySelector('.products-two');
        if (productsElement) {
          productsElement.style.background = isDarkMode.value ? 
            'linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%)' : 
            'linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%)';
        }
        
        // 应用主题到搜索框
        applySearchTheme();
      }
    };
    
    // 处理搜索
    const handleSearch = () => {
      if (searchQuery.value.trim()) {
        console.log('搜索:', searchQuery.value);
        
        // 如果已有请求在进行中，忽略
        if (isRequestPending.value) {
          console.log('请求正在处理中，忽略搜索');
          return;
        }
        
        // 设置请求标志
        isRequestPending.value = true;
        
        // 重置分类和品牌选择
        selectedCategory.value = null;
        selectedBrand.value = null;
        
        // 重置API分页状态
        apiCurrentPage.value = 1;
        hasMorePages.value = true;
        
        // 获取产品
        fetchProducts().finally(() => {
          isRequestPending.value = false;
        });
      }
    };
    
    // 选择分类
    const toggleCategoryDropdown = async (categoryId) => {
      console.log('toggleCategoryDropdown被调用，分类ID:', categoryId);
      
      // 如果已有请求在进行中，忽略
      if (isRequestPending.value) {
        console.log('请求正在处理中，忽略分类切换');
        return;
      }
      
      // 检查categoryId是否有效
      if (categoryId === undefined || categoryId === null) {
        console.error('无效的分类ID:', categoryId);
        return;
      }
      
      if (selectedCategory.value === categoryId) {
        // 如果点击的是当前选中的分类，则关闭下拉菜单
        console.log('关闭当前选中的分类下拉菜单');
        selectedCategory.value = null;
        categoryBrands.value = [];
      } else {
        // 设置请求标志
        isRequestPending.value = true;
        
        try {
          // 切换到新的分类
          console.log('切换到新的分类:', categoryId);
          
          // 先设置选中的分类ID
          selectedCategory.value = categoryId;
          selectedBrand.value = null;
          
          // 计算下拉菜单位置
          calculateDropdownPosition();
          
          // 先获取品牌数据，再计算位置
          // 如果已经缓存了该分类的品牌数据，直接使用缓存
          if (brandsByCategory.value[categoryId]) {
            console.log('使用缓存的品牌数据:', brandsByCategory.value[categoryId]);
            categoryBrands.value = brandsByCategory.value[categoryId];
          } else {
            // 否则，获取该分类下的品牌
            console.log('获取分类的品牌数据');
            await fetchBrandsByCategory(categoryId);
          }
          
          // 使用选中的分类获取产品
          await fetchProducts();
        } finally {
          // 完成请求，重置标志
          isRequestPending.value = false;
        }
      }
    };
    
    // 计算下拉菜单位置
    const calculateDropdownPosition = () => {
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        const selectedChip = document.querySelector(`.filter-chip.active`);
        console.log('选中的标签元素:', selectedChip);
        
        if (selectedChip) {
          const rect = selectedChip.getBoundingClientRect();
          console.log('标签位置信息:', rect);
          
          // 根据设备类型设置下拉菜单位置
          if (isMobile.value) {
            // 移动端显示在分类正下方，完全贴合
            dropdownPosition.value = {
              position: 'fixed',
              top: `${rect.bottom}px`, // 完全贴合，不留间隙
              left: '50%',
              transform: 'translateX(-50%)',
              width: `${Math.max(rect.width, 120)}px`, // 至少与按钮一样宽
              maxWidth: '350px',
              bottom: 'auto',
              '--arrow-left': '50%' // 箭头位置居中
            };
          } else {
            // PC端显示在分类下方，完全贴合
            dropdownPosition.value = {
              position: 'absolute',
              top: `${rect.bottom + window.scrollY}px`, // 完全贴合，不留间隙
              left: `${rect.left}px`,
              bottom: 'auto',
              width: `${Math.max(rect.width, 180)}px`, // 至少与按钮一样宽
              transform: 'none'
            };
          }
          
          console.log('设置的下拉菜单位置:', dropdownPosition.value);
        } else {
          console.error('未找到选中的分类标签元素，使用默认位置');
          
          // 如果找不到选中的标签，设置一个默认位置
          if (isMobile.value) {
            dropdownPosition.value = {
              position: 'fixed',
              top: '150px',
              left: '50%',
              transform: 'translateX(-50%)',
              width: '90%',
              maxWidth: '350px',
              bottom: 'auto'
            };
          } else {
            dropdownPosition.value = {
              position: 'absolute',
              top: '150px',
              left: '50%',
              transform: 'translateX(-50%)',
              bottom: 'auto',
              width: '180px'
            };
          }
          
          console.log('设置的默认下拉菜单位置:', dropdownPosition.value);
        }
      }, 10);
    };
    
    // 选择品牌
    const selectBrand = (brandId) => {
      // 如果已有请求在进行中，忽略
      if (isRequestPending.value) {
        console.log('请求正在处理中，忽略品牌选择');
        return;
      }
      
      // 设置请求标志
      isRequestPending.value = true;
      
      try {
        if (selectedBrand.value === brandId) {
          selectedBrand.value = null;
        } else {
          selectedBrand.value = brandId;
        }
        console.log('选中品牌:', brandId);
        
        // 使用选中的品牌获取产品
        fetchProducts();
      } finally {
        // 延迟重置请求标志，避免重复点击
        setTimeout(() => {
          isRequestPending.value = false;
        }, 300);
      }
    };
    
    // 获取分类下的品牌
    const fetchBrandsByCategory = async (categoryId) => {
      try {
        // 检查categoryId是否有效
        if (categoryId === undefined || categoryId === null) {
          console.error('无效的分类ID:', categoryId);
          return;
        }
        
        // 确保categoryId是字符串类型
        const catId = categoryId.toString();
        console.log('正在获取分类ID:', catId, '的品牌数据');
        
        // 调用API获取品牌，确保传递categoryId参数
        try {
          const response = await productsApi.getBrandsByCategory(catId);
          console.log('分类ID:', catId, '的品牌数据:', response);
          
          if (response && response.data && Array.isArray(response.data)) {
            categoryBrands.value = response.data.map(brand => ({
              id: brand.brandId || brand.id,
              name: brand.name,
              parentCategoryId: categoryId
            }));
            brandsByCategory.value[categoryId] = categoryBrands.value;
          } else if (Array.isArray(response)) {
            categoryBrands.value = response.map(brand => ({
              id: brand.brandId || brand.id,
              name: brand.name,
              parentCategoryId: categoryId
            }));
            brandsByCategory.value[categoryId] = categoryBrands.value;
          } else {
            // 如果API返回的数据无效，使用模拟数据
            console.warn('API返回的品牌数据无效，使用模拟数据');
            const mockData = [
              { id: 1, name: 'Hoodies', parentCategoryId: categoryId },
              { id: 2, name: 'T-Shirt', parentCategoryId: categoryId },
              { id: 3, name: 'Pants', parentCategoryId: categoryId },
              { id: 4, name: 'Sets', parentCategoryId: categoryId }
            ];
            categoryBrands.value = mockData;
            brandsByCategory.value[categoryId] = mockData;
          }
        } catch (apiErr) {
          console.error('API调用失败:', apiErr);
          // 使用模拟数据
          const mockData = [
            { id: 1, name: 'Hoodies', parentCategoryId: categoryId },
            { id: 2, name: 'T-Shirt', parentCategoryId: categoryId },
            { id: 3, name: 'Pants', parentCategoryId: categoryId },
            { id: 4, name: 'Sets', parentCategoryId: categoryId }
          ];
          categoryBrands.value = mockData;
          brandsByCategory.value[categoryId] = mockData;
        }
      } catch (err) {
        console.error('获取品牌数据失败:', err, '分类ID:', categoryId);
        // 发生错误时使用模拟数据
        const mockData = [
          { id: 1, name: 'Hoodies', parentCategoryId: categoryId },
          { id: 2, name: 'T-Shirt', parentCategoryId: categoryId },
          { id: 3, name: 'Pants', parentCategoryId: categoryId },
          { id: 4, name: 'Sets', parentCategoryId: categoryId }
        ];
        categoryBrands.value = mockData;
        brandsByCategory.value[categoryId] = mockData;
      }
    };
    
    // 添加点击外部关闭下拉菜单
    const handleClickOutside = (event) => {
      // 如果点击的不是分类或其子元素，也不是品牌下拉菜单
      const filterChips = document.querySelectorAll('.filter-chip');
      const brandsDropdown = document.querySelector('.brands-dropdown-container');
      let clickedInside = false;
      
      // 检查是否点击在分类上
      filterChips.forEach(chip => {
        if (chip.contains(event.target)) {
          clickedInside = true;
        }
      });
      
      // 检查是否点击在品牌下拉菜单上
      if (brandsDropdown && brandsDropdown.contains(event.target)) {
        clickedInside = true;
      }
      
      // 如果点击在外部，关闭下拉菜单
      if (!clickedInside && selectedCategory.value !== null) {
        selectedCategory.value = null;
        categoryBrands.value = [];
      }
    };
    
    // 处理窗口尺寸变化
    const handleResize = () => {
      isMobile.value = window.innerWidth <= 480;
      // 更新滚动阈值
      scrollThreshold.value = window.innerWidth <= 768 ? 300 : 500;
      // 重新设置分类样式
      setupCategoriesStyle();
      if (selectedCategory.value !== null) {
        // 重新计算下拉菜单位置
        calculateDropdownPosition();
      }
    };
    
    // 设置分类样式
    const setupCategoriesStyle = () => {
      if (typeof window === 'undefined') return;
      
      setTimeout(() => {
        const filterChips = document.querySelector('.filter-chips');
        if (filterChips) {
          // 根据设备类型设置样式
          if (isMobile.value) {
            filterChips.style.flexWrap = 'wrap';
            filterChips.style.width = '100%';
          } else {
            // PC端可以根据需要设置不同样式
            filterChips.style.flexWrap = 'wrap';
          }
        }
      }, 100);
    };
    
    // 切换过滤下拉框显示状态
    const toggleFilterDropdown = () => {
      showFilterDropdown.value = !showFilterDropdown.value;
      
      // 点击外部区域关闭下拉框
      if (showFilterDropdown.value) {
        setTimeout(() => {
          document.addEventListener('click', handleOutsideClick);
        }, 10);
      } else {
        document.removeEventListener('click', handleOutsideClick);
      }
    };
    
    // 设置激活的过滤选项
    const setActiveFilter = (filter) => {
      activeFilter.value = filter;
      
      // 应用过滤选项
      applySortingIfNeeded();
      
      // 重新加载排序后的产品数据
      displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);
      
      console.log('设置过滤选项:', filter);
      
      // 关闭下拉框
      showFilterDropdown.value = false;
    };
    
    // 刷新过滤
    const refreshFilter = () => {
      console.log('刷新过滤');
      // 重置过滤选项
      activeFilter.value = 'recommend';
      sortDirection.value = 'asc';
      
      // 重新获取产品数据
      fetchProducts();
    };
    
    // 处理点击外部区域
    const handleOutsideClick = (event) => {
      const filterIcon = document.querySelector('.filter-icon');
      const filterDropdown = document.querySelector('.filter-dropdown');
      
      if (filterIcon && filterDropdown) {
        // 如果点击的不是过滤图标或下拉框内的元素，则关闭下拉框
        if (!filterIcon.contains(event.target) && !filterDropdown.contains(event.target)) {
          showFilterDropdown.value = false;
          document.removeEventListener('click', handleOutsideClick);
        }
      }
    };
    
    // 设置排序方向
    const setSortDirection = (direction) => {
      sortDirection.value = direction;
      activeFilter.value = 'seller'; // 自动选择seller作为过滤条件
      console.log('设置排序方向:', direction);
      
      // 应用排序
      applySortingIfNeeded();
      
      // 重新加载排序后的产品数据
      displayedProducts.value = filteredProducts.value.slice(0, itemsPerPage.value);
    };
    
    // 获取分类名称
    const getCategoryName = (categoryId) => {
      const category = categories.value.find(c => c.id === categoryId);
      return category ? category.name : 'Unknown Category';
    };
    
    // 加载更多产品
    const loadMoreProducts = () => {
      if (isLoadingMore.value || noMoreProducts.value || isRequestPending.value) return;
      
      // 立即设置加载状态
      isLoadingMore.value = true;
      
      // 检查是否还有更多本地数据可显示
      const currentDisplayedCount = displayedProducts.value.length;
      
      // 如果本地还有更多数据未显示
      if (currentDisplayedCount < filteredProducts.value.length) {
        // 从本地数据加载更多
        const nextBatch = filteredProducts.value.slice(
          currentDisplayedCount, 
          currentDisplayedCount + itemsPerPage.value
        );
        
        // 使用setTimeout模拟加载过程，提供更好的视觉反馈
        setTimeout(() => {
          displayedProducts.value = [...displayedProducts.value, ...nextBatch];
          
          // 检查是否显示了所有本地数据
          if (displayedProducts.value.length >= filteredProducts.value.length && hasMorePages.value) {
            // 需要从服务器加载更多数据
            fetchProducts(true);
          } else if (displayedProducts.value.length >= filteredProducts.value.length) {
            // 没有更多数据了
            noMoreProducts.value = true;
            isLoadingMore.value = false;
          } else {
            // 还有本地数据，重置加载状态
            isLoadingMore.value = false;
          }
        }, 500); // 800ms的加载动画展示时间
      } else if (hasMorePages.value) {
        // 从服务器加载更多数据
        fetchProducts(true);
      } else {
        // 没有更多数据了
        noMoreProducts.value = true;
        isLoadingMore.value = false;
      }
    };
    
    // 添加滚动监听函数
    const handleScroll = () => {
      if (isLoading.value || isLoadingMore.value || noMoreProducts.value || isRequestPending.value) return;
      
      // 获取当前滚动位置
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      
      // 判断是否向下滚动
      const isScrollingDown = scrollTop > lastScrollTop.value;
      
      // 更新上次滚动位置
      lastScrollTop.value = scrollTop;
      
      // 只有向下滚动时才触发加载
      if (isScrollingDown) {
        // 计算滚动位置
        const scrollPosition = window.innerHeight + window.pageYOffset;
        const documentHeight = document.documentElement.scrollHeight;
        const distanceFromBottom = documentHeight - scrollPosition;
        
        // 当距离底部小于阈值时加载更多
        if (distanceFromBottom < scrollThreshold.value) {
          loadMoreProducts();
        }
      }
    };
    
    // 添加防抖函数
    const debounce = (fn, delay) => {
      let timer = null;
      return function() {
        const context = this;
        const args = arguments;
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(context, args);
        }, delay);
      };
    };
    
    // 使用防抖处理滚动事件
    const debouncedHandleScroll = debounce(handleScroll, 200);
    
    // 处理收藏状态更新
    const handleCollectUpdated = (product) => {
      console.log('收藏状态已更新:', product);
      // 更新产品列表中对应产品的收藏状态
      const index = products.value.findIndex(p => p.id === product.id);
      if (index !== -1) {
        products.value[index].isCollected = product.isCollected;
      }
    };
    
    // 处理点赞状态更新
    const handleLikeUpdated = (product) => {
      console.log('点赞状态已更新:', product);
      // 更新产品列表中对应产品的点赞状态和点赞数
      const index = products.value.findIndex(p => p.id === product.id);
      if (index !== -1) {
        products.value[index].isLiked = product.isLiked;
        products.value[index].likes = product.likes;
      }
    };
    
    onMounted(() => {
      checkCurrentTheme();
      
      // 获取分类数据
      fetchCategories();
      
      // 获取产品数据
      fetchProducts();
      
      // 使用MutationObserver监听主题变化
      const htmlElement = document.documentElement;
      themeObserver.value = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'data-theme') {
            checkCurrentTheme();
          }
        });
      });
      
      themeObserver.value.observe(htmlElement, { attributes: true, attributeFilter: ['data-theme'] });
      
      // 添加主题变化监听
      window.addEventListener('themechange', checkCurrentTheme);
      
      // 如果存在emitter，也添加监听
      if (window.emitter) {
        window.emitter.on('theme-changed', handleThemeChange);
      }
      
      // 监听系统主题变化
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', (e) => {
        isDarkMode.value = e.matches;
        applySearchTheme(); // 应用搜索框主题
      });
      
      // 设置分类样式
      setupCategoriesStyle();
      
      // 添加全局点击事件
      document.addEventListener('click', handleClickOutside);
      
      // 添加窗口尺寸变化事件
      window.addEventListener('resize', handleResize);
      
      // 添加滚动监听
      window.addEventListener('scroll', debouncedHandleScroll);
      isScrollListenerActive.value = true;
      
      // 组件卸载时清理
      onUnmounted(() => {
        // 停止观察DOM变化
        if (themeObserver.value) {
          themeObserver.value.disconnect();
        }
        
        // 移除主题变化监听
        window.removeEventListener('themechange', checkCurrentTheme);
        
        if (window.emitter) {
          window.emitter.off('theme-changed', handleThemeChange);
        }
        
        // 移除系统主题变化监听
        mediaQuery.removeEventListener('change', () => {});
        
        // 移除全局点击事件
        document.removeEventListener('click', handleClickOutside);
        document.removeEventListener('click', handleOutsideClick);
        
        // 移除窗口尺寸变化事件
        window.removeEventListener('resize', handleResize);
        
        // 移除滚动监听
        if (isScrollListenerActive.value) {
          window.removeEventListener('scroll', debouncedHandleScroll);
          isScrollListenerActive.value = false;
        }
      });
    });
    
    return {
      searchQuery,
      isDarkMode,
      handleSearch,
      categories,
      selectedCategory,
      toggleCategoryDropdown,
      categoryBrands,
      selectedBrand,
      selectBrand,
      dropdownPosition,
      isMobile,
      showFilterDropdown,
      activeFilter,
      toggleFilterDropdown,
      setActiveFilter,
      refreshFilter,
      sortDirection,
      setSortDirection,
      getCategoryName,
      searchContainerRef,
      // 替换模拟数据为API获取的数据
      products: displayedProducts,
      isLoading,
      isLoadingMore,
      noMoreProducts,
      loadMoreProducts,
      error,
      handleCollectUpdated,
      handleLikeUpdated
    };
  }
}
</script>

<style scoped>
.products-two {
  padding: 2rem 5rem;
  min-height: 100vh;
  color: #e0e0e0;
  /* 移除背景色和渐变设置，让StarryBackground生效 */
  background: transparent;
  background-image: none;
  position: relative;
  z-index: 1; /* 确保内容在星空背景之上 */
}

/* 世界地图部分的样式 */
.world-map-section {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  margin-bottom: 2rem;
}

/* 主题响应样式 */
[data-theme="dark"] .world-map-section {
  background: var(--card-bg) !important;
}

[data-theme="light"] .world-map-section {
  background: var(--card-bg) !important;
}

/* 搜索框样式 */
.search-container {
  display: flex;
  align-items: center;
  max-width: 1400px;
  height: 56px;
  border-radius: 8px;
  margin: 0 auto 2rem;
  padding: 0 16px;
  transition: all 0.3s ease;
  background-color: transparent;
  border: none;
  box-shadow: none;
  overflow: hidden;
}

/* 深色主题搜索框 */
.search-container.dark-theme {
  background-color: #1e1e2f;
  border: 1px solid #2a2a3a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 浅色主题搜索框 */
.search-container.light-theme {
  background-color: #e8d0f5;
  border: none;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

.search-icon, .camera-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  padding: 0 10px;
  transition: color 0.3s ease;
}

.dark-theme .search-icon,
.dark-theme .camera-icon {
  color: #e0e0e0;
}

.light-theme .search-icon,
.light-theme .camera-icon {
  color: #8e2de2;
}

.search-input {
  flex: 1;
  height: 100%;
  background: transparent;
  border: none;
  font-size: 16px;
  padding: 0 12px;
  outline: none;
  transition: color 0.3s ease;
}

.dark-theme .search-input {
  color: #e0e0e0;
}

.dark-theme .search-input::placeholder {
  color: #a0a0a0;
}

.light-theme .search-input {
  color: #333;
}

.light-theme .search-input::placeholder {
  color: #666;
}

/* 添加移动端世界地图容器样式 */
@media (max-width: 768px) {
  .world-map-section {
    width: 100%;
    max-width: 100%;
    padding: 0.5rem;
    margin: 0 auto 1.5rem;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
  }
  
  .products-two {
    padding: 1rem;
  }
  
  .search-container {
    width: calc(100% - 2rem);
    margin: 0 auto 1.5rem;
    height: 48px; /* 移动端稍微降低高度 */
    border-radius: 6px; /* 移动端稍微减小圆角 */
  }
}

/* ================ 分类过滤器样式 ================ */
.navbar-filter {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1400px;
  margin: 0 auto 2rem;
  padding: 0 1rem;
  position: relative;
}

.filter-icon {
  margin-right: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

/* 深色主题下的过滤器图标 */
[data-theme="dark"] .filter-icon {
  background-color: #8e2de2;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.3);
}

/* 浅色主题下的过滤器图标 */
[data-theme="light"] .filter-icon {
  background-color: #e8d0f5;
  border: none;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
}

/* 深色主题下的图标悬停效果 */
[data-theme="dark"] .filter-icon:hover {
  background-color: #9d4de8;
  transform: scale(1.05);
}

/* 浅色主题下的图标悬停效果 */
[data-theme="light"] .filter-icon:hover {
  background-color: #d9b5ef;
  transform: scale(1.05);
}

.filter-icon svg {
  transition: transform 0.3s ease;
}

/* 深色主题下的SVG图标颜色 */
[data-theme="dark"] .filter-icon svg {
  color: #ffffff;
}

/* 浅色主题下的SVG图标颜色 */
[data-theme="light"] .filter-icon svg {
  color: #8e2de2;
}

.filter-icon svg.rotate {
  transform: rotate(180deg);
}

.category-tabs {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 4px;
  max-width: 80%;
}

.category-tabs::-webkit-scrollbar {
  display: none;
}

.filter-chip {
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 5px;
  border: none;
}

/* 深色主题下的筛选器按钮 */
[data-theme="dark"] .filter-chip {
  background-color: #2a1a4a;
  color: #ffffff;
  border: none;
}

/* 浅色主题下的筛选器按钮 */
[data-theme="light"] .filter-chip {
  background-color: #e8d0f5;
  color: #333;
  border: none;
}

.dropdown-arrow {
  font-size: 10px;
  opacity: 0.7;
  transition: transform 0.3s ease;
}

.filter-chip.active .dropdown-arrow {
  transform: rotate(180deg);
  opacity: 1;
}

/* 深色主题下悬停效果 */
[data-theme="dark"] .filter-chip:hover {
  background-color: #3a2a5a;
}

/* 浅色主题下悬停效果 */
[data-theme="light"] .filter-chip:hover {
  background-color: #d9b5ef;
}

/* 深色主题下激活状态 */
[data-theme="dark"] .filter-chip.active {
  background-color: #8e2de2;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.3);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  position: relative;
  z-index: 1001;
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: none;
  color: #ffffff;
}

/* 浅色主题下激活状态 */
[data-theme="light"] .filter-chip.active {
  background-color: #9d4de8;
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
  position: relative;
  z-index: 1001;
  border-bottom: none;
  border-left: none;
  border-right: none;
  border-top: none;
  color: #ffffff;
}

/* 过滤下拉框 */
.filter-dropdown {
  position: absolute;
  top: 46px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
  max-width: 500px;
  border-radius: 12px;
  z-index: 1100; /* 提高z-index确保在遮罩层之上 */
  padding: 14px;
  animation: fadeIn 0.2s ease;
  backdrop-filter: blur(5px);
}

/* 深色主题下的过滤下拉框 */
[data-theme="dark"] .filter-dropdown {
  background: rgba(43, 32, 73, 0.98);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* 浅色主题下的过滤下拉框 */
[data-theme="light"] .filter-dropdown {
  background: rgba(232, 208, 245, 0.98);
  box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
  border: none;
}

.filter-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 12px;
  padding: 2px;
}

.filter-option {
  height: 52px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  padding: 0 15px;
  position: relative;
  letter-spacing: 0.5px;
}

/* 深色主题下的过滤选项 */
[data-theme="dark"] .filter-option {
  background-color: rgba(90, 51, 160, 0.7);
  color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  border: none;
}

/* 浅色主题下的过滤选项 */
[data-theme="light"] .filter-option {
  background-color: rgba(232, 208, 245, 0.7);
  color: #333;
  box-shadow: 0 1px 3px rgba(142, 45, 226, 0.2);
  border: none;
}

/* 深色主题下的悬停效果 */
[data-theme="dark"] .filter-option:hover {
  background-color: rgba(114, 69, 193, 0.9);
  box-shadow: 0 2px 10px 0 rgba(142, 45, 226, 0.2);
  transform: translateY(-1px);
}

/* 浅色主题下的悬停效果 */
[data-theme="light"] .filter-option:hover {
  background-color: rgba(217, 181, 239, 0.9);
  box-shadow: 0 2px 10px 0 rgba(142, 45, 226, 0.1);
  transform: translateY(-1px);
}

.filter-option:active {
  transform: translateY(0);
}

/* 深色主题下的激活状态 */
[data-theme="dark"] .filter-option.active {
  background-color: #8e2de2;
  box-shadow: 0 3px 10px rgba(142, 45, 226, 0.3);
}

/* 浅色主题下的激活状态 */
[data-theme="light"] .filter-option.active {
  background-color: #9d4de8;
  box-shadow: 0 3px 10px rgba(142, 45, 226, 0.2);
  color: #ffffff;
}

.seller-option {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  grid-column: 1;
}

.seller-option span {
  padding-left: 0;
  padding-right: 0;
}

.arrow-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  position: absolute;
  right: 15px;
  top: 0;
  bottom: 0;
}

.arrow-up, .arrow-down {
  cursor: pointer;
  height: 14px;
  opacity: 0.7;
  transition: all 0.2s ease;
  margin: 1px 0;
}

/* 深色主题下的箭头颜色 */
[data-theme="dark"] .arrow-up, 
[data-theme="dark"] .arrow-down {
  color: #ffffff;
}

/* 浅色主题下的箭头颜色 */
[data-theme="light"] .arrow-up, 
[data-theme="light"] .arrow-down {
  color: #8e2de2;
}

.arrow-up:hover, .arrow-down:hover {
  opacity: 1;
  transform: scale(1.15);
}

.active .arrow-up, .active .arrow-down {
  opacity: 1;
}

.refresh {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* 深色主题下的刷新按钮 */
[data-theme="dark"] .refresh {
  background-color: rgba(90, 51, 160, 0.7);
}

/* 浅色主题下的刷新按钮 */
[data-theme="light"] .refresh {
  background-color: rgba(232, 208, 245, 0.7);
}

/* 深色主题下的刷新按钮悬停效果 */
[data-theme="dark"] .refresh:hover {
  background-color: rgba(114, 69, 193, 0.9);
  box-shadow: 0 2px 12px 0 rgba(142, 45, 226, 0.2);
}

/* 浅色主题下的刷新按钮悬停效果 */
[data-theme="light"] .refresh:hover {
  background-color: rgba(217, 181, 239, 0.9);
  box-shadow: 0 2px 12px 0 rgba(142, 45, 226, 0.1);
}

.refresh:active {
  transform: scale(0.98);
}

/* 深色主题下的刷新图标颜色 */
[data-theme="dark"] .refresh-icon {
  color: #ffffff;
}

/* 浅色主题下的刷新图标颜色 */
[data-theme="light"] .refresh-icon {
  color: #8e2de2;
}

.refresh-icon {
  transition: all 0.3s;
}

.refresh:hover .refresh-icon {
  transform: rotate(30deg);
}

.refresh:active .refresh-icon {
  transform: rotate(60deg);
}

/* 背景遮罩 */
.filter-dropdown-backdrop, .dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1090; /* 提高z-index，但略低于下拉菜单 */
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .navbar-filter {
    padding: 0 10px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
    justify-content: center;
  }
  
  .navbar-filter::-webkit-scrollbar {
    display: none;
  }
  
  .filter-icon {
    margin-right: 10px;
  }
  
  .category-tabs {
    gap: 8px;
    max-width: 75%;
    justify-content: flex-start;
  }
  
  .filter-chip {
    padding: 6px 16px;
    font-size: 13px;
  }
  
  .filter-dropdown {
    width: 95%;
    max-width: 450px;
    left: 50%;
    transform: translateX(-50%);
  }
}

@media (max-width: 480px) {
  .navbar-filter {
    justify-content: center;
    padding: 0 0px;
    transform: none;
    flex-direction: column;
    width: 100%;
    margin-bottom: 2.5rem;
    position: relative; /* 添加相对定位，作为筛选器弹出框的定位参考 */
  }
  
  .category-tabs {
    max-width: 100%;
    margin-top: 10px;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
    padding: 0 5px;
  }
  
  .filter-chip {
    margin-bottom: 5px;
    min-width: 80px;
    text-align: center;
    justify-content: center;
  }
  
  .filter-dropdown {
    width: 90%;
    max-width: 350px;
    left: 50%;
    transform: translateX(-50%);
    top: 120px; /* 使用固定距离顶部的位置 */
    position: fixed; /* 使用固定定位 */
    z-index: 1100; /* 确保与PC端一致的z-index */
    padding: 10px; /* 减小内边距，让内容在小屏幕上更紧凑 */
  }
  
  /* 深色主题下的移动端过滤下拉框 */
  [data-theme="dark"] .filter-dropdown {
    background: rgba(43, 32, 73, 0.98);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  }
  
  /* 浅色主题下的移动端过滤下拉框 */
  [data-theme="light"] .filter-dropdown {
    background: rgba(232, 208, 245, 0.98);
    box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
  }
  
  .filter-options {
    grid-gap: 10px; /* 略微增加间距，确保按钮之间不会太挤 */
    padding: 0; /* 移除内边距 */
  }
  
  .filter-option {
    height: 42px; /* 稍微减小高度 */
    font-size: 14px; /* 减小字体大小 */
    padding: 0 8px; /* 减小水平内边距 */
  }
  
  .seller-option {
    grid-column: 1;
    grid-row: 3;
    justify-content: center;
    padding-left: 0;
    padding-right: 0;
  }
  
  /* 移动端深色主题下的刷新按钮 */
  [data-theme="dark"] .refresh {
    grid-column: 2;
    grid-row: 3;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }
  
  /* 移动端浅色主题下的刷新按钮 */
  [data-theme="light"] .refresh {
    grid-column: 2;
    grid-row: 3;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .arrow-container {
    right: 12px;
  }
  
  .brands-dropdown-container.mobile-dropdown {
    position: fixed;
    top: auto;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 350px;
    z-index: 1000;
    margin-top: 10px; /* 添加顶部外边距，适应换行布局 */
  }
}

/* 品牌下拉菜单样式 */
.brands-dropdown-container {
  position: absolute;
  z-index: 1000;
  width: 180px;
  margin-top: -75px; /* 移除顶部外边距，确保完全贴合 */
}

.brands-dropdown-container.mobile-dropdown {
  position: fixed;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.5);
  z-index: 1000;
  left: 50% !important;
  transform: translateX(-50%) !important;
  width: 90% !important;
  max-width: 350px !important;
  margin-top: 0; /* 移除顶部外边距，确保完全贴合 */
}

.brands-dropdown {
  border-radius: 12px;
  padding-top: 0;
  padding-bottom: 5px;
  animation: fadeIn 0.3s ease;
  overflow: hidden;
  position: relative;
  width: 100%;
  margin-top: 0;
  border-top-left-radius: 0; /* 使顶部边角与按钮贴合 */
  border-top-right-radius: 0; /* 使顶部边角与按钮贴合 */
}

/* 深色主题下的品牌下拉菜单 */
[data-theme="dark"] .brands-dropdown {
  background-color: #2a1a4a;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  border: none;
  margin-top: -1px;
}

/* 浅色主题下的品牌下拉菜单 */
[data-theme="light"] .brands-dropdown {
  background-color: #e8d0f5;
  box-shadow: 0 8px 20px rgba(142, 45, 226, 0.15);
  border: none;
  margin-top: -1px;
}

/* 移除箭头，使下拉框直接与按钮贴合 */
.brands-dropdown::before {
  display: none;
}

.mobile-dropdown .brands-dropdown::before {
  display: none;
}

/* 调整下拉框顶部样式 */
.dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 15px;
  position: sticky;
  top: 0;
  z-index: 2;
}

/* 深色主题下的下拉框头部 */
[data-theme="dark"] .dropdown-header {
  background-color: #3a2a5a;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 浅色主题下的下拉框头部 */
[data-theme="light"] .dropdown-header {
  background-color: #d9b5ef;
  border-bottom: 1px solid rgba(142, 45, 226, 0.1);
}

.dropdown-header span {
  font-weight: 500;
  font-size: 14px;
}

/* 深色主题下的标题文字 */
[data-theme="dark"] .dropdown-header span {
  color: #e0e0e0;
}

/* 浅色主题下的标题文字 */
[data-theme="light"] .dropdown-header span {
  color: #333;
}

.brand-dropdown-item {
  padding: 8px 15px; /* 减少内边距使其更紧凑 */
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  text-align: center;
}

/* 深色主题下的品牌项 */
[data-theme="dark"] .brand-dropdown-item {
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

/* 浅色主题下的品牌项 */
[data-theme="light"] .brand-dropdown-item {
  color: #333;
  border-bottom: 1px solid rgba(142, 45, 226, 0.05);
}

.brand-dropdown-item:last-child {
  border-bottom: none;
}

/* 深色主题下的品牌项悬停效果 */
[data-theme="dark"] .brand-dropdown-item:hover {
  background-color: rgba(90, 51, 160, 0.6);
}

/* 浅色主题下的品牌项悬停效果 */
[data-theme="light"] .brand-dropdown-item:hover {
  background-color: rgba(217, 181, 239, 0.6);
}

/* 深色主题下的激活品牌项 */
[data-theme="dark"] .brand-dropdown-item.active {
  background-color: #8e2de2;
  font-weight: 500;
  color: #ffffff;
}

/* 浅色主题下的激活品牌项 */
[data-theme="light"] .brand-dropdown-item.active {
  background-color: #9d4de8;
  font-weight: 500;
  color: #ffffff;
}

/* 背景遮罩 */
.dropdown-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999;
  animation: fadeIn 0.2s ease;
}

.empty-brands {
  padding: 20px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}

/* 深色主题下的关闭按钮 */
[data-theme="dark"] .close-btn {
  color: #e0e0e0;
}

/* 浅色主题下的关闭按钮 */
[data-theme="light"] .close-btn {
  color: #333;
}

/* 深色主题下的关闭按钮悬停效果 */
[data-theme="dark"] .close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 浅色主题下的关闭按钮悬停效果 */
[data-theme="light"] .close-btn:hover {
  background: rgba(0, 0, 0, 0.05);
}

/* 产品网格样式 */
.products-grid-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

/* 移动端适配 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }
  
  .products-grid-container {
    padding: 1.5rem 0.5rem;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 6px;
    padding: 0;
  }
  
  .products-grid-container {
    padding: 1rem 0.2rem;
    max-width: 100%;
  }
}

/* 加载状态样式 */
.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  height: 300px;
  border-radius: 16px;
  margin-bottom: 2rem;
  transition: all 0.3s ease;
}

/* 深色主题下的加载容器 */
[data-theme="dark"] .loading-spinner-container {
  background: rgba(20, 15, 40, 0.7);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

/* 浅色主题下的加载容器 */
[data-theme="light"] .loading-spinner-container {
  background: rgba(232, 208, 245, 0.7);
  box-shadow: 0 5px 15px rgba(142, 45, 226, 0.15);
  border: 1px solid rgba(142, 45, 226, 0.1);
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(120, 70, 200, 0.2);
  border-radius: 50%;
  border-top-color: #7b88ff;
  animation: spin 1.5s linear infinite;
  margin-bottom: 1rem;
}

/* 深色主题下的加载文字 */
[data-theme="dark"] .loading-spinner-container p {
  color: #c3a3ff;
  font-size: 1.2rem;
}

/* 浅色主题下的加载文字 */
[data-theme="light"] .loading-spinner-container p {
  color: #8e2de2;
  font-size: 1.2rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载更多按钮 */
.load-more-container {
  display: flex;
  justify-content: center;
  margin: 1.5rem 0 2.5rem;
  min-height: 100px;
  width: 100%;
}

.load-more-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #ffffff;
  border: 2px solid rgba(180, 120, 255, 0.5);
  padding: 1rem 2.5rem;
  border-radius: 50px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.3);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 200px;
}

.load-more-btn:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 30px rgba(120, 70, 200, 0.5);
}

.load-more-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.small-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(255, 255, 255, 0.2);
  border-top-color: #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

/* 没有更多产品提示 */
.no-more-products {
  text-align: center;
  padding: 1.5rem;
  font-style: italic;
  border-radius: 15px;
  margin: 1rem auto 2rem;
  max-width: 400px;
  transition: all 0.3s ease;
}

/* 深色主题下的无更多产品提示 */
[data-theme="dark"] .no-more-products {
  color: #c3a3ff;
  background: rgba(30, 30, 35, 0.7);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

/* 浅色主题下的无更多产品提示 */
[data-theme="light"] .no-more-products {
  color: #8e2de2;
  background: rgba(232, 208, 245, 0.7);
  box-shadow: 0 5px 15px rgba(142, 45, 226, 0.15);
  border: 1px solid rgba(142, 45, 226, 0.1);
}

.no-more-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  transition: color 0.3s ease;
}

/* 深色主题下的图标 */
[data-theme="dark"] .no-more-icon {
  color: #8a2be2;
}

/* 浅色主题下的图标 */
[data-theme="light"] .no-more-icon {
  color: #6a1b9a;
}

.cosmic-line {
  width: 80%;
  height: 1px;
  margin: 1rem auto 0;
  transition: background 0.3s ease;
}

/* 深色主题下的分隔线 */
[data-theme="dark"] .cosmic-line {
  background: linear-gradient(90deg, transparent, rgba(138, 43, 226, 0.6), transparent);
}

/* 浅色主题下的分隔线 */
[data-theme="light"] .cosmic-line {
  background: linear-gradient(90deg, transparent, rgba(106, 27, 154, 0.4), transparent);
}

/* 宇宙风格加载器样式 */
.cosmic-loader {
  position: relative;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cosmic-loader-ring {
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 3px solid transparent;
  animation: cosmicSpin 1.5s linear infinite;
}

/* 深色主题下的宇宙加载环 */
[data-theme="dark"] .cosmic-loader-ring {
  border-top-color: #9e49ff;
  border-left-color: #4cebff;
  border-right-color: #ff9be7;
  box-shadow: 
    0 0 15px rgba(156, 73, 255, 0.6),
    0 0 30px rgba(76, 235, 255, 0.4),
    0 0 45px rgba(255, 155, 231, 0.3);
}

/* 浅色主题下的宇宙加载环 */
[data-theme="light"] .cosmic-loader-ring {
  border-top-color: #8e2de2;
  border-left-color: #4a00e0;
  border-right-color: #c837ab;
  box-shadow: 
    0 0 15px rgba(142, 45, 226, 0.6),
    0 0 30px rgba(74, 0, 224, 0.4),
    0 0 45px rgba(200, 55, 171, 0.3);
}

.cosmic-loader-stars {
  position: absolute;
  width: 100%;
  height: 100%;
  animation: cosmicRotate 7s linear infinite;
}

.cosmic-star {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  opacity: 0.7;
  filter: blur(1px);
  animation: cosmicTwinkle 2s infinite alternate;
}

/* 深色主题下的星星 */
[data-theme="dark"] .cosmic-star {
  background-color: #ffffff;
}

/* 浅色主题下的星星 */
[data-theme="light"] .cosmic-star {
  background-color: #8e2de2;
}

@keyframes cosmicSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes cosmicRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes cosmicTwinkle {
  0% { opacity: 0.2; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1.2); }
}

/* 错误消息样式 */
.error-message {
  padding: 1.5rem;
  margin: 1rem auto;
  border-radius: 15px;
  text-align: center;
  max-width: 600px;
  transition: all 0.3s ease;
}

/* 深色主题下的错误消息 */
[data-theme="dark"] .error-message {
  background: rgba(150, 0, 0, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(150, 0, 0, 0.3);
  box-shadow: 0 0 20px rgba(150, 0, 0, 0.2);
}

/* 浅色主题下的错误消息 */
[data-theme="light"] .error-message {
  background: rgba(255, 107, 107, 0.1);
  color: #d32f2f;
  border: 1px solid rgba(211, 47, 47, 0.2);
  box-shadow: 0 0 20px rgba(211, 47, 47, 0.1);
}

.error-message button {
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  margin-left: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

/* 深色主题下的错误按钮 */
[data-theme="dark"] .error-message button {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

/* 浅色主题下的错误按钮 */
[data-theme="light"] .error-message button {
  background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
  color: #d32f2f;
  border: 1px solid rgba(211, 47, 47, 0.3);
}

/* 深色主题下的错误按钮悬停效果 */
[data-theme="dark"] .error-message button:hover {
  background: linear-gradient(135deg, #42306a, #352458);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4);
  border-color: rgba(120, 70, 200, 0.6);
}

/* 浅色主题下的错误按钮悬停效果 */
[data-theme="light"] .error-message button:hover {
  background: linear-gradient(135deg, #ffffff, #f5f5f5);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(211, 47, 47, 0.2);
  border-color: rgba(211, 47, 47, 0.5);
}

@media (max-width: 480px) {
  .filter-options {
    grid-gap: 10px; /* 略微增加间距，确保按钮之间不会太挤 */
    padding: 0; /* 移除内边距 */
  }

  .filter-option {
    height: 42px; /* 稍微减小高度 */
    font-size: 14px; /* 减小字体大小 */
    padding: 0 8px; /* 减小水平内边距 */
    border-radius: 8px;
  }
}

/* ================ PC端分类过滤器样式 ================ */
.category-filter {
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 1400px;
  margin: 0 auto 2rem;
  padding: 0 1rem;
  position: relative;
}

.filter-chips-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.filter-chips {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.filter-chip {
  padding: 8px 20px;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

/* 深色主题下的分类标签 */
[data-theme="dark"] .filter-chip {
  background: rgba(60, 40, 90, 0.6);
  border: 1px solid rgba(120, 70, 200, 0.3);
  color: #e0e0e0;
}

[data-theme="dark"] .filter-chip:hover {
  background: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

[data-theme="dark"] .filter-chip.active {
  background: rgba(142, 45, 226, 0.8);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

/* 浅色主题下的分类标签 */
[data-theme="light"] .filter-chip {
  background: rgba(232, 208, 245, 0.6);
  border: 1px solid rgba(142, 45, 226, 0.3);
  color: #2d3748;
}

[data-theme="light"] .filter-chip:hover {
  background: rgba(232, 208, 245, 0.8);
  border-color: rgba(142, 45, 226, 0.6);
}

[data-theme="light"] .filter-chip.active {
  background: rgba(142, 45, 226, 0.8);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.filter-chip.active .dropdown-arrow {
  transform: rotate(180deg);
}

/* ================ TrendingProducts 移动端样式 ================ */
/* 移动端分类和过滤按钮 */
.mobile-filter-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  width: 100%;
  padding: 0 5px;
  position: relative;
  z-index: 1100;
}

.category-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(30, 30, 45, 0.8);
  border: 1px solid rgba(90, 51, 160, 0.5);
  border-radius: 30px;
  color: white;
  padding: 12px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  margin-left: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  text-align: center;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.category-btn:hover {
  background-color: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

.category-btn:active {
  transform: scale(0.98);
}

.category-btn svg {
  margin-right: 10px;
  width: 18px;
  height: 18px;
  display: none; /* 隐藏SVG图标，与截图一致 */
}

.mobile-filter-category .filter-icon {
  width: 48px;
  height: 48px;
  min-width: 48px;
  background-color: #8e2de2;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mobile-filter-category .filter-icon svg {
  width: 24px;
  height: 24px;
  color: white;
}

/* 移动端过滤背景遮罩 */
.mobile-filter-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1150;
  animation: fadeIn 0.2s ease;
}

/* 移动端过滤下拉框 */
.mobile-filter-dropdown {
  position: absolute;
  top: 45px;
  left: -10px;
  width: calc(100% + 10px);
  background: #2b1a45;
  border-radius: 12px;
  z-index: 1200;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
  padding: 10px;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 移动端过滤选项网格 */
.mobile-filter-dropdown .filter-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: repeat(3, 1fr);
  gap: 8px;
  padding: 0;
}

.mobile-filter-dropdown .filter-option {
  background: rgba(60, 40, 90, 0.6);
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 8px;
  color: #e0e0e0;
  padding: 8px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 36px;
  pointer-events: auto;
  position: relative;
  z-index: 1250;
}

.mobile-filter-dropdown .filter-option:hover {
  background: rgba(90, 51, 160, 0.7);
  border-color: rgba(142, 45, 226, 0.8);
}

.mobile-filter-dropdown .filter-option.active {
  background: rgba(142, 45, 226, 0.8);
  border-color: rgba(142, 45, 226, 1);
  color: white;
}

.mobile-filter-dropdown .seller-option {
  grid-column: 1;
  grid-row: 3;
  justify-content: center;
  padding-left: 0;
  padding-right: 0;
}

.mobile-filter-dropdown .refresh {
  grid-column: 2;
  grid-row: 3;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-filter-dropdown .refresh svg {
  width: 16px;
  height: 16px;
}
</style>