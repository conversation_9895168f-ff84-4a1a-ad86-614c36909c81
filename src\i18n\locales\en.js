export default {
  navbar: {
    home: 'Home',
    products: 'Products',
    sellers: 'Sellers',
    howToBuy: 'How to Buy',
    community: 'Community',
    search: 'Search',
    cart: 'Cart',
    user: 'User'
  },
  login: {
    welcome: 'Welcome to AGTFIND',
    email: 'Email',
    emailPlaceholder: 'Enter your email address',
    password: 'Password',
    passwordPlaceholder: 'Enter your password',
    rememberMe: 'Remember me',
    forgotPassword: 'Forgot password?',
    loginButton: 'Login',
    noAccount: 'Don\'t have an account?',
    registerNow: 'Register now',
    orLoginWith: 'Or login with',
    googleLogin: 'Login with Google'
  },
  register: {
    createAccount: 'Create Your Account',
    username: '<PERSON><PERSON><PERSON>',
    usernamePlaceholder: 'Choose a username',
    email: 'Email',
    emailPlaceholder: 'Enter your email address',
    password: 'Password',
    passwordPlaceholder: 'Create a strong password',
    confirmPassword: 'Confirm Password',
    confirmPasswordPlaceholder: 'Confirm your password',
    passwordStrength: 'Password Strength:',
    weak: 'Weak',
    medium: 'Medium',
    strong: 'Strong',
    veryStrong: 'Very Strong',
    lowercase: 'Lowercase',
    uppercase: 'Uppercase',
    number: 'Number',
    special: 'Special',
    minLength: '6+ Chars',
    registerButton: 'Register',
    haveAccount: 'Already have an account?',
    loginHere: 'Login here',
    termsAgree: 'I agree to the',
    termsAndConditions: 'Terms and Conditions',
    and: 'and',
    privacyPolicy: 'Privacy Policy'
  },
  profile: {
    title: 'PROFILE',
    home: 'Home',
    uploadAvatar: 'Change avatar',
    uploading: 'Uploading...',
    clickToChange: 'Click the camera icon to change avatar',
    basicInfo: 'Basic Information',
    firstName: 'First Name',
    lastName: 'Last Name',
    phone: 'Phone',
    preferredCurrency: 'Preferred Currency',
    notSet: 'Not set',
    accountSettings: 'Account Settings',
    changePassword: 'Change Password',
    editProfile: 'Edit Profile',
    logout: 'Logout',
    save: 'Save Changes',
    cancel: 'Cancel',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmNewPassword: 'Confirm New Password',
    myFavorites: 'My Favorites',
    favoriteSellers: 'Favorite Sellers',
    enterFirstName: 'Enter your first name',
    enterLastName: 'Enter your last name',
    enterCurrentPassword: 'Enter your current password',
    enterNewPassword: 'Enter your new password',
    confirmNewPasswordPlaceholder: 'Confirm your new password',
    userAvatarAlt: 'User Avatar',
    defaultUsername: 'Username',
    defaultEmail: '<EMAIL>',
    editProfileTitle: 'Edit Profile',
    enterPhoneNumber: 'Enter your phone number',
    selectCurrency: 'Select currency',
    changePasswordTitle: 'Change Password',
    passwordStrengthLabel: 'Password Strength:',
    validation: {
      confirmPassword: 'Please confirm your password',
      passwordsDoNotMatch: 'Passwords do not match',
      validPhoneNumber: 'Please enter a valid phone number',
      enterCurrentPassword: 'Please enter your current password',
      enterNewPassword: 'Please enter your new password',
      passwordMinLength: 'Password must be at least 6 characters'
    },
    messages: {
      loginToView: 'Please login to view your profile',
      loadInfoError: 'Failed to load user information',
      profileUpdateSuccess: 'Profile updated successfully',
      profileUpdateFailed: 'Failed to update profile',
      profileUpdateFailedPrefix: 'Failed to update profile: ',
      strongerPassword: 'Please use a stronger password',
      passwordUpdateSuccess: 'Password updated successfully',
      passwordUpdateFailed: 'Failed to update password',
      passwordUpdateError: 'An error occurred while updating your password',
      avatarUploadSuccess: 'Avatar uploaded successfully',
      avatarUploadFailed: 'Failed to upload avatar. Please try again.'
    }
  },
  howTo: {
    title: 'How to Buy',
    subtitle: 'Follow these simple steps to make your purchase',
    imageTutorial: 'Image Tutorial',
    videoTutorial: 'Video Tutorial',
    step: 'Step',
    step1Title: 'Choose Your Product',
    step1Content: 'Browse through our extensive catalog and select the product you want to purchase.',
    step2Title: 'Check Product QC',
    step2Content: 'Review the product\'s QC (Quality Control) details, then click the purchase button on the product page.',
    tip: 'Tip',
    tipContent: 'Use filters and search to find your desired products quickly.'
  },
  product: {
    description: 'Description',
    buyNow: 'Buy Now',
    relatedProducts: 'Related Products',
    customerReviews: 'Customer Reviews',
    noDescription: 'No description available.',
    qcPhotos: 'Quality Control Photos',
    noQcImages: 'No QC images available for this product.',
    likes: 'Likes',
    views: 'Views',
    comments: 'Comments',
    shareThoughts: 'Share your thoughts...',
    postComment: 'Post Comment',
    pleaseLogin: 'Please login to post a comment',
    loginNow: 'Login Now',
    noComments: 'No comments yet. Be the first to comment!',
    reply: 'Reply',
    delete: 'Delete',
    dealOfTheDay: 'Deal of the Day',
    endsIn: 'Ends in',
    hours: 'hours',
    minutes: 'minutes',
    seconds: 'seconds',
    off: 'off',
    sortByNewest: 'Newest',
    sortByPopular: 'Popular',
    allComments: 'All Comments',
    commentCount: 'comments',
    commentSuccess: 'Comment posted successfully',
    commentError: 'Failed to post comment',
    deleteConfirmMessage: 'Are you sure you want to delete this comment? This action cannot be undone.',
    deleteConfirmTitle: 'Confirm Delete',
    deleteSuccess: 'Comment deleted successfully',
    deleteError: 'Failed to delete comment',
    deleteOperationError: 'An error occurred while deleting the comment',
    likeSuccess: 'Like successful',
    unlikeSuccess: 'Unlike successful',
    likeError: 'Failed to like comment',
    unlikeError: 'Failed to unlike comment',
    likeOperationError: 'Error processing like operation',
    loginToLike: 'Please login to like comments',
    incompleteUserInfo: 'User information is incomplete, please login again',
    loadMoreComments: 'Load more comments',
    skuNotAvailable: 'Product SKU not available, unable to redirect to purchase platform',
    redirectingToPlatform: 'Redirecting to purchase platform...',
    selectPlatformFirst: 'Please select a platform first',
    invalidPlatformLink: 'Invalid platform link'
  },
  common: {
    back: 'Back',
    shopNow: 'Shop Now',
    viewDetails: 'View Details',
    loading: 'Loading more products...',
    language: 'Language',
    currency: 'Currency',
    less: 'Less',
    more: 'More',
    confirm: 'Confirm',
    cancel: 'Cancel'
  },
  productsView: {
    searchPlaceholder: 'Search for treasures...',
    search: 'Search',
    filters: 'Filters',
    categories: 'Categories',
    brands: 'Brands',
    price: 'Price',
    priceRange: 'Price Range',
    sortBy: 'Sort By',
    newest: 'Newest',
    priceHighToLow: 'Price: High to Low',
    priceLowToHigh: 'Price: Low to High',
    popularity: 'Popularity',
    apply: 'Apply',
    reset: 'Reset',
    loadingMoreProducts: 'Loading more products...',
    noMoreProducts: 'No more products to load',
    sortByViews: 'Sort by Views',
    sortByPrice: 'Sort by Price',
    noProductsFound: 'No products found. Please try a different category or search term.',
    addToCartSuccess: 'Product added to cart successfully',
    addToCartError: 'Failed to add product to cart: '
  },
  home: {
    welcome: 'Welcome to AGTFIND!',
    welcomePoints: [
      'Unleash AGTFIND\'s ultimate spreadsheet power! ⚡ Discover 1000+ premium products 💎',
      'See the real truth! Access authentic reviews ✅, stunning QC photos 📸, and astounding USD prices 💰',
      'We fiercely curate top merchants 🏆 & guarantee quality 🛡 Find your perfect agent platform effortlessly',
      'AGTFIND: Your ultimate discovery weapon! ✨ Excellence'
    ],
    shippingDiscount: 'Shipping discount: "AGTFIND" 15%-18% off',
    getStarted: 'Get Started',
    dealOfTheDay: 'DEAL OF THE DAY',
    elevateYourRepGame: 'ELEVATE YOUR REP GAME HERE',
    introTitle: 'Welcome to OMGBUY',
    introDesc: 'Genuine QC and evaluations make product information transparent for easier searching and purchasing! Featuring over 3,000 products, vast transaction data, and numerous quality sellers!  No more relying on Reddit or spreadsheets—helping you easily find the items you love!',
    activeUsers: 'ACTIVE USERS',
    elevateRep: 'CODE: OMGBUY 15% OFF'
  },
  comment: {
    expand: 'Expand',
    collapse: 'Collapse replies',
    replies: 'replies',
    userAvatar: 'User avatar',
    reply: 'Reply',
    delete: 'Delete'
  },
  social: {
    instagram: 'Instagram',
    telegram: 'Telegram',
    discord: 'Discord',
    reddit: 'Reddit',
    twitter: 'Twitter'
  },
  faq: {
    title: 'FAQ',
    questions: 'Questions'
  }
}