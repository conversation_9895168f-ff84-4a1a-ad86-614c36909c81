<template>
  <div class="related-product-card" @click="handleClick">
    <div class="product-image-container">
      <img 
        :src="product.image" 
        :alt="product.name"
        @error="handleImageError"
        class="product-image"
      >
    </div>
    <div class="product-content">
      <h3 class="product-title">{{ product.name }}</h3>
      <div class="price-section">
        <span class="current-price">{{ currencySymbol }}{{ formatPrice(product.price) }}</span>
        <span v-if="product.originalPrice" class="original-price">
          {{ currencySymbol }}{{ formatPrice(product.originalPrice) }}
        </span>
      </div>
      <div class="product-actions">
        <div @click.stop="handleLike" class="action-item" :class="{ active: product.isLiked }">
          <i class="fas fa-thumbs-up"></i>
          <span>{{ product.likes || 0 }}</span>
        </div>
        <div @click.stop="handleViews" class="action-item">
          <i class="fas fa-eye"></i>
          <span>{{ product.views || 0 }}</span>
        </div>
        <div @click.stop="handleComments" class="action-item">
          <i class="fas fa-comment"></i>
          <span>{{ getCommentsCount() }}</span>
        </div>
        <div @click.stop="handleCollect" class="action-item" :class="{ active: product.isCollected }">
          <i class="fas fa-heart"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import productsApi from '@/api/products'
import { addCollection, removeFavorite } from '@/api/favorites'

export default {
  name: 'RelatedProductCard',
  props: {
    product: {
      type: Object,
      required: true
    },
    currencySymbol: {
      type: String,
      default: '$'
    },
    exchangeRate: {
      type: Number,
      default: 1
    }
  },
  methods: {
    handleClick() {
      this.$emit('product-click', this.product)
    },

    // handleImageError(event) {
    //   console.error('Image load error for related product:', this.product.name)
    //   event.target.src = 'https://via.placeholder.com/280x280/333/fff?text=No+Image'
    // },

    async handleLike() {
      const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
      if (!currentUser.id) {
        ElMessage.warning('Please log in to like products')
        return
      }

      try {
        const response = await productsApi.toggleLike(this.product.id || this.product.productId)
        if (response.code === 200) {
          this.$emit('like-updated', {
            ...this.product,
            isLiked: !this.product.isLiked,
            likes: this.product.likes + (this.product.isLiked ? -1 : 1)
          })
          ElMessage.success(this.product.isLiked ? 'Like removed' : 'Liked!')
        }
      } catch (error) {
        console.error('Error toggling like:', error)
        ElMessage.error('Failed to update like status')
      }
    },

    async handleCollect() {
      const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
      if (!currentUser.id) {
        ElMessage.warning('Please log in to collect products')
        return
      }

      try {
        if (this.product.isCollected) {
          await removeFavorite(this.product.id || this.product.productId)
          this.$emit('collect-updated', { ...this.product, isCollected: false })
          ElMessage.success('Removed from favorites')
        } else {
          await addCollection({
            productId: this.product.id || this.product.productId,
            userId: currentUser.id
          })
          this.$emit('collect-updated', { ...this.product, isCollected: true })
          ElMessage.success('Added to favorites')
        }
      } catch (error) {
        console.error('Error toggling collection:', error)
        ElMessage.error('Failed to update collection status')
      }
    },

    handleViews() {
      this.$emit('views-click', this.product)
    },

    handleComments() {
      this.$emit('comments-click', this.product)
    },

    formatPrice(price) {
      return (price * this.exchangeRate).toFixed(2)
    },

    getCommentsCount() {
      return (this.product.comments && this.product.comments.length) || 0
    }
  }
}
</script>

<style scoped>
.related-product-card {
  background: linear-gradient(135deg, #2a1f3b, #1e1a2e);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 100%;
  min-height: 280px;
  display: flex;
  flex-direction: column;
}

.related-product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(195, 163, 255, 0.4);
}

.product-image-container {
  width: 100%;
  height: 220px;
  overflow: hidden;
  background: rgba(15, 15, 20, 0.3);
  position: relative;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  display: block;
  vertical-align: middle;
  padding: 10px;
}

.related-product-card:hover .product-image {
  transform: scale(1.03);
}

.product-content {
  padding: 0.6rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.product-title {
  color: #ffffff;
  font-size: 0.85rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.1;
  height: 1.2em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  flex-wrap: wrap;
}

.current-price {
  color: #bb86fc;
  font-size: 0.9rem;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(187, 134, 252, 0.6);
}

.original-price {
  color: #888;
  font-size: 0.75rem;
  text-decoration: line-through;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0;
  gap: 0.5rem;
}

.action-item {
  color: #c3a3ff;
  font-size: 0.65rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.1rem;
  flex: 1;
  justify-content: center;
  background: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  padding: 0 !important;
}

.action-item:hover {
  transform: scale(1.05);
}

.action-item.active {
  color: #ff6b6b;
  text-shadow: 0 0 8px rgba(255, 107, 107, 0.8);
}

.action-item i {
  font-size: 0.8rem;
}

.action-item span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .related-product-card {
    min-height: 320px;
    border-radius: 10px;
  }
  
  .product-image-container {
    height: 210px;
    padding: 0;
    background: rgba(15, 15, 20, 0.3);
  }
  
  .product-image {
    object-fit: contain;
    padding: 8px;
    max-height: 100%;
    max-width: 100%;
  }
  
  .product-content {
    padding: 0.7rem 0.7rem 0.4rem;
    gap: 0.3rem;
  }
  
  .product-title {
    font-size: 0.9rem;
    height: 2.4em;
    line-height: 1.2;
    -webkit-line-clamp: 2;
    margin-bottom: 0.15rem;
  }

  .price-section {
    margin: 0 0 0.15rem;
  }
  
  .product-actions {
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .related-product-card {
    min-height: 300px;
  }
  
  .product-image-container {
    height: 190px;
    background: rgba(15, 15, 20, 0.2);
  }
  
  .product-image {
    object-fit: contain;
    padding: 5px;
  }
  
  .product-content {
    padding: 0.6rem 0.6rem 0.3rem;
    gap: 0.25rem;
  }
  
  .price-section {
    margin: 0 0 0.1rem;
  }
}
</style> 