<template>
  <div class="product-card" :class="{ 'light-theme': !isDarkMode }">
    <div class="trending-product-container">
      <!-- Product Images Container -->
      <div class="product-images-container">
        <!-- Main large image -->
        <div class="main-image-container">
          <img :src="productImage" :alt="productName">
          
          <!-- Yellow tag icon if showTag is true -->
          <div v-if="showTag" class="tag-icon">
            <img src="@/assets/2.png" alt="Yellow tag icon" class="yellow-tag-image">
          </div>
          
          <!-- Red checkmark if showCheck is true -->
          <div v-if="showCheck" class="check-icon">
            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M7.5924 1.20027C7.34888 1.4078 7.22711 1.51158 7.09706 1.59874C6.79896 1.79854 6.46417 1.93721 6.1121 2.00672C5.95851 2.03705 5.79903 2.04977 5.48008 2.07522C4.6787 2.13918 4.278 2.17115 3.94371 2.28923C3.17051 2.56233 2.56233 3.17051 2.28923 3.94371C2.17115 4.278 2.13918 4.6787 2.07522 5.48008C2.04977 5.79903 2.03705 5.95851 2.00672 6.1121C1.93721 6.46417 1.79854 6.79896 1.59874 7.09706C1.51158 7.22711 1.40781 7.34887 1.20027 7.5924C0.678835 8.20428 0.418104 8.51023 0.265216 8.83011C-0.0884052 9.56995 -0.0884052 10.43 0.265216 11.1699C0.418105 11.4898 0.678827 11.7957 1.20027 12.4076C1.40778 12.6511 1.51158 12.7729 1.59874 12.9029C1.79854 13.201 1.93721 13.5358 2.00672 13.8879C2.03705 14.0415 2.04977 14.201 2.07522 14.5199C2.13918 15.3213 2.17115 15.722 2.28923 16.0563C2.56233 16.8295 3.17051 17.4377 3.94371 17.7108C4.278 17.8288 4.6787 17.8608 5.48008 17.9248C5.79903 17.9502 5.95851 17.963 6.1121 17.9933C6.46417 18.0628 6.79896 18.2015 7.09706 18.4013C7.22711 18.4884 7.34887 18.5922 7.5924 18.7997C8.20429 19.3212 8.51023 19.5819 8.83011 19.7348C9.56995 20.0884 10.43 20.0884 11.1699 19.7348C11.4898 19.5819 11.7957 19.3212 12.4076 18.7997C12.6511 18.5922 12.7729 18.4884 12.9029 18.4013C13.201 18.2015 13.5358 18.0628 13.8879 17.9933C14.0415 17.963 14.201 17.9502 14.5199 17.9248C15.3213 17.8608 15.722 17.8288 16.0563 17.7108C16.8295 17.4377 17.4377 16.8295 17.7108 16.0563C17.8288 15.722 17.8608 15.3213 17.9248 14.5199C17.9502 14.201 17.963 14.0415 17.9933 13.8879C18.0628 13.5358 18.2015 13.201 18.4013 12.9029C18.4884 12.7729 18.5922 12.6511 18.7997 12.4076C19.3212 11.7957 19.5819 11.4898 19.7348 11.1699C20.0884 10.43 20.0884 9.56995 19.7348 8.83011C19.5819 8.51023 19.3212 8.20429 18.7997 7.5924C18.5922 7.34887 18.4884 7.22711 18.4013 7.09706C18.2015 6.79896 18.0628 6.46417 17.9933 6.1121C17.963 5.95851 17.9502 5.79903 17.9248 5.48008C17.8608 4.6787 17.8288 4.278 17.7108 3.94371C17.4377 3.17051 16.8295 2.56233 16.0563 2.28923C15.722 2.17115 15.3213 2.13918 14.5199 2.07522C14.201 2.04977 14.0415 2.03705 13.8879 2.00672C13.5358 1.93721 13.201 1.79854 12.9029 1.59874C12.7729 1.51158 12.6511 1.40781 12.4076 1.20027C11.7957 0.678828 11.4898 0.418105 11.1699 0.265216C10.43 -0.0884052 9.56995 -0.0884052 8.83011 0.265216C8.51023 0.418105 8.20428 0.678833 7.5924 1.20027ZM14.3735 7.86314C14.6913 7.5453 14.6913 7.03 14.3735 6.71216C14.0557 6.39433 13.5403 6.39433 13.2225 6.71216L8.37227 11.5624L6.77746 9.9676C6.45963 9.64977 5.94432 9.64977 5.62649 9.9676C5.30866 10.2854 5.30866 10.8007 5.62649 11.1186L7.79678 13.2889C8.11461 13.6067 8.62992 13.6067 8.94775 13.2889L14.3735 7.86314Z" fill="#F22C2C" fill-rule="evenodd" style="mix-blend-mode:normal"></path>
            </svg>
          </div>
        </div>
      </div>
      
      <!-- Product info section with price -->
      <div class="product-info-section">
        <div class="price-row">
          <p class="current-price">{{ formattedPrice }}</p>
          <div class="product-id">{{ productPlatform }}</div>
          <div class="favorite-icon" @click.stop="handleCollect">
            <i :class="[isCollected ? 'fas fa-heart collected' : 'far fa-heart']"></i>
          </div>
        </div>
        
        <h3 class="product-name">{{ productName }}</h3>
        
        <!-- Social Stats -->
        <div class="social-stats">
          <div class="stat-container" @click.stop="handleLike">
            <i :class="[isLiked ? 'fas fa-thumbs-up liked' : 'far fa-thumbs-up']"></i>
            <span>{{ productLikes }}</span>
          </div>
          <div class="stat-container">
            <i class="far fa-image"></i>
            <span>{{ productViews }}</span>
          </div>
          <div class="stat-container">
            <i class="fas fa-comment"></i>
            <span>{{ productComments }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { addCollection, removeFavorite } from '@/api/favorites'
import { ElMessage } from 'element-plus'
import productsApi from '@/api/products'

export default {
  name: 'ProductCard',
  props: {
    product: {
      type: Object,
      required: true
    },
    showTag: {
      type: Boolean,
      default: false
    },
    showCheck: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isDarkMode: true,
      isCollected: false,
      isLiked: false,
      localLikes: 0
    };
  },
  created() {
    this.isCollected = this.product.isCollected || false;
    this.isLiked = this.product.isLiked || false;
    this.localLikes = this.product.likes || 0;
  },
  computed: {
    // 确保即使API返回的数据格式不同，组件也能正确显示
    productImage() {
      return this.product.image || this.product.imageUrl || this.product.thumbnail || '';
    },
    productPrice() {
      // 处理不同格式的价格数据
      const price = this.product.price || this.product.salePrice || this.product.newPrice || '0';
      return typeof price === 'number' ? price.toFixed(2) : price;
    },
    formattedPrice() {
      // 根据截图格式化价格显示，例如$10.95
      return `$${this.productPrice}`;
    },
    productId() {
      return this.product.id || this.product.productId || this.product.itemId || '';
    },
    productPlatform() {
      // 获取原始平台名称
      const platform = this.product.platform || 'Amazon';
      
      // 根据平台名称返回对应的代码
      if (platform === '微店') return 'weidian';
      if (platform === '淘宝') return 'taobao';
      if (platform === '1688') return '1688';
      
      // 其他平台直接返回原始值
      return platform;
    },
    productName() {
      // 确保优先获取商品名称，截图中可能没有显示名称
      return this.product.name || this.product.title || this.product.productName || '';
    },
    productLikes() {
      return this.localLikes || this.product.likes || this.product.likeCount || '0';
    },
    productViews() {
      return this.product.views || this.product.viewCount || '0';
    },
    productComments() {
      // 如果comments是数组，则返回数组长度
      if (Array.isArray(this.product.comments)) {
        return this.product.comments.length;
      }
      // 否则返回commentCount属性或默认值0
      return this.product.commentCount || '0';
    }
  },
  mounted() {
    // 检查当前主题
    this.checkCurrentTheme();
    
    // 添加主题变化监听
    document.addEventListener('themechange', this.checkCurrentTheme);
    
    // 如果存在emitter，也添加监听
    if (window.emitter) {
      window.emitter.on('theme-changed', this.handleThemeChange);
    }
    
    // 监听HTML元素的data-theme属性变化
    const htmlElement = document.documentElement;
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          this.checkCurrentTheme();
        }
      });
    });
    
    observer.observe(htmlElement, { attributes: true, attributeFilter: ['data-theme'] });
    
    // 组件卸载时移除监听
    this.$options.unmounted = () => {
      document.removeEventListener('themechange', this.checkCurrentTheme);
      if (window.emitter) {
        window.emitter.off('theme-changed', this.handleThemeChange);
      }
      observer.disconnect();
    };
  },
  methods: {
    // 检测当前主题
    checkCurrentTheme() {
      // 检查HTML元素的data-theme属性
      const htmlElement = document.documentElement;
      const theme = htmlElement.getAttribute('data-theme');
      this.isDarkMode = theme === 'dark';
    },
    
    // 处理主题变化
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode !== 'undefined') {
        this.isDarkMode = data.isDarkMode;
      }
    },
    
    // 处理收藏/取消收藏
    async handleCollect(event) {
      // 阻止事件冒泡,避免点击收藏图标时跳转到产品详情
      event.stopPropagation();
      
      // 如果用户未登录,提示登录
      if (!localStorage.getItem('isLoggedIn')) {
        this.showToast('Please login first to add to favorites', 'warning');
        this.$router.push('/login');
        return;
      }
      
      try {
        if (this.isCollected) {
          // 取消收藏
          await removeFavorite(this.productId);
          this.isCollected = false;
          this.showToast('Removed from favorites');
        } else {
          // 添加收藏
          await addCollection(this.productId);
          this.isCollected = true;
          this.showToast('Added to favorites');
        }
        
        // 触发事件通知父组件收藏状态已更新
        this.$emit('collect-updated', {
          ...this.product,
          isCollected: this.isCollected
        });
      } catch (error) {
        console.error('收藏操作失败:', error);
        this.showToast(this.isCollected ? 'Failed to remove from favorites' : 'Failed to add to favorites', 'error');
      }
    },
    
    // 处理点赞/取消点赞
    async handleLike(event) {
      // 阻止事件冒泡,避免点击点赞图标时跳转到产品详情
      event.stopPropagation();
      
      // 如果用户未登录,提示登录
      if (!localStorage.getItem('isLoggedIn')) {
        this.showToast('Please login first to like', 'warning');
        this.$router.push('/login');
        return;
      }
      
      try {
        if (this.isLiked) {
          // 取消点赞
          await productsApi.unlikeProduct(this.productId);
          this.isLiked = false;
          // 减少点赞数
          this.localLikes = Math.max(0, parseInt(this.localLikes) - 1);
          this.showToast('Unlike successful');
        } else {
          // 检查用户今天是否已经点赞过该商品
          const checkResponse = await productsApi.checkLikeToday(this.productId);
          
          if (checkResponse.code === 200 && checkResponse.data && checkResponse.data.isLikedToday) {
            this.showToast('You have already liked this product today', 'warning');
            return;
          }
          
          // 点赞
          await productsApi.likeProduct(this.productId);
          this.isLiked = true;
          // 增加点赞数
          this.localLikes = parseInt(this.localLikes) + 1;
          this.showToast('Like successful');
        }
        
        // 触发事件通知父组件点赞状态已更新
        this.$emit('like-updated', {
          ...this.product,
          isLiked: this.isLiked,
          likes: this.localLikes
        });
      } catch (error) {
        console.error('点赞操作失败:', error);
        this.showToast(this.isLiked ? 'Unlike failed' : 'Like failed', 'error');
      }
    },
    
    // 显示提示消息
    showToast(message, type = 'success') {
      ElMessage({
        message,
        type,
        duration: 2000
      });
    }
  }
}
</script>

<style scoped>
.product-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #bf52e3;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.25);
  border-color: #8e2de2;
}

/* 浅色主题样式 */
.product-card.light-theme {
  background: #ffffff;
  border: 1px solid #e0c6f5;
  box-shadow: 0 4px 12px rgba(142, 45, 226, 0.1);
}

.product-card.light-theme:hover {
  box-shadow: 0 12px 24px rgba(142, 45, 226, 0.15);
  border-color: #bf52e3;
}

.trending-product-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

/* Product Images Container */
.product-images-container {
  width: 100%;
  background: #fff;
  padding: 0;
}

.main-image-container {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 0;
  min-height: 260px;
  overflow: hidden;
}

.main-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Tag Icon */
.tag-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
}

.yellow-tag-image {
  width: 21px;
  height: 21px;
  display: block;
}

/* 红色圆形图标 */
.check-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Product Info Section */
.product-info-section {
  padding: 15px;
  background: linear-gradient(to left, #a742c6 0%, #a742c6 10%, #5d1d8c 100%);
  color: white;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 浅色主题产品信息区域 */
.light-theme .product-info-section {
  background: linear-gradient(to left, #e0c6f5 0%, #d7b7ef 10%, #bf52e3 100%);
  color: #333;
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 8px;
}

.current-price {
  font-size: 28px;
  font-weight: 700;
  color: #bf52e3 !important;
  margin: 0;
  display: flex;
  align-items: center;
}

/* 浅色主题价格 */
.light-theme .current-price {
  color: #8e2de2 !important;
}

.product-id {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
}

/* 浅色主题产品ID */
.light-theme .product-id {
  background: rgba(142, 45, 226, 0.2);
  color: #333;
}

.favorite-icon {
  margin-left: auto;
  font-size: 20px;
  color: #ddd;
  opacity: 0.8;
  cursor: pointer;
  transition: all 0.3s ease;
}

.favorite-icon:hover {
  transform: scale(1.1);
}

.favorite-icon .collected {
  color: #ff71ce !important;
}

/* 浅色主题收藏图标 */
.light-theme .favorite-icon {
  color: #999;
}

.light-theme .favorite-icon .collected {
  color: #ff71ce !important;
}

.product-name {
  font-size: 14px;
  font-weight: normal;
  color: #fff;
  margin: 0 0 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 0.9;
}

/* 浅色主题产品名称 */
.light-theme .product-name {
  color: #333;
  opacity: 0.85;
}

/* Social Stats */
.social-stats {
  display: flex;
  justify-content: flex-start;
  gap: 20px;
  align-items: center;
  font-size: 13px;
  margin-top: auto; /* 推到底部 */
}

.social-stats .stat-container {
  display: flex;
  align-items: center;
  margin-right: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-stats .stat-container:hover {
  transform: translateY(-2px);
}

.liked {
  color: #ff6b6b;
}

.stat-container i {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* 浅色主题社交状态 */
.light-theme .stat-container {
  color: rgba(51, 51, 51, 0.8);
}

.light-theme .stat-container i {
  color: rgba(142, 45, 226, 0.6);
}

/* 响应式样式调整 */
@media (max-width: 768px) {
  .main-image-container {
    min-height: 200px;
  }
  
  .current-price {
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .main-image-container {
    min-height: 160px;
  }
  
  .product-info-section {
    padding: 10px;
  }
  
  .current-price {
    font-size: 22px;
  }
  
  .product-id {
    font-size: 10px;
    padding: 1px 6px;
  }
  
  .social-stats {
    gap: 10px;
    font-size: 12px;
  }
}
</style> 