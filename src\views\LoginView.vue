<template>
  <div class="login-page" :class="{ 'dark-theme': isDarkMode }" :data-theme="isDarkMode ? 'dark' : 'light'">
    <StarryBackground />
    <div class="login-container">
      <div class="login-header">
        <img src="@/assets/logo1.png" alt="AGTFIND Logo" class="logo">
        <h1>{{ $t('login.welcome') }}</h1>
      </div>
      
      <div class="login-form">
        <div class="form-group">
          <label for="email">{{ $t('login.email') }}</label>
          <input 
            type="email" 
            id="email" 
            v-model="email" 
            :placeholder="$t('login.emailPlaceholder')"
            class="form-input"
          >
        </div>
        
        <div class="form-group">
          <label for="password">{{ $t('login.password') }}</label>
          <div class="password-input-container">
            <input 
              :type="showPassword ? 'text' : 'password'" 
              id="password" 
              v-model="password" 
              :placeholder="$t('login.passwordPlaceholder')"
              class="form-input"
            >
            <button 
              type="button"
              class="toggle-password"
              @click="showPassword = !showPassword"
            >
              <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
            </button>
          </div>
        </div>
        
        <div class="form-options">
          <div class="remember-me">
            <input type="checkbox" id="remember" v-model="rememberMe">
            <label for="remember">{{ $t('login.rememberMe') }}</label>
          </div>
          <a href="#" class="forgot-password">{{ $t('login.forgotPassword') }}</a>
        </div>
        
        <button 
          type="button" 
          class="login-btn" 
          :disabled="isLoading"
          @click="login"
        >
          <span v-if="!isLoading">{{ $t('login.loginButton') }}</span>
          <span v-else class="loading-spinner"></span>
        </button>
      </div>
      
      <div class="register-section">
        <p>{{ $t('login.noAccount') }} <router-link to="/register" class="register-link">{{ $t('login.registerNow') }}</router-link></p>
      </div>
      
      <div class="social-login">
        <p>{{ $t('login.orLoginWith') }}</p>
        <!-- <div class="social-buttons">
          <button class="social-btn google" @click="googleLogin">
            <i class="fab fa-google"></i>
          </button>
          <button class="social-btn facebook">
            <i class="fab fa-facebook-f"></i>
          </button>
          <button class="social-btn github">
            <i class="fab fa-github"></i>
          </button>
        </div> -->
        
        <!-- 全尺寸谷歌登录按钮 -->
        <div class="google-login-btn" @click="googleLogin">
          <img src="@/assets/google-icon.svg" alt="Google" />
          <span>{{ $t('login.googleLogin') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import { loginApi } from '@/api/users'
import { tokenUtil } from '@/services/api'
import { emitter } from '@/utils/eventBus'
import StarryBackground from '@/components/StarryBackground.vue'

export default {
  name: 'LoginView',
  components: {
    StarryBackground
  },
  data() {
    // 主题管理
    const savedDarkMode = localStorage.getItem('darkMode');
    const initialDarkMode = savedDarkMode !== null ? savedDarkMode === 'true' : true;

    return {
      email: '',
      password: '',
      rememberMe: false,
      showPassword: false,
      isLoading: false,
      isDarkMode: initialDarkMode // 主题状态
    }
  },
  methods: {
    googleLogin() {
      // 修改为生产环境URL
      window.location.href = 'https://www.omgbuy.com/front/oauth2/login/google';
    },
    
    // 处理谷歌登录回调
    async processGoogleCallback() {
      // 从URL参数获取令牌和用户信息
      const urlParams = new URLSearchParams(window.location.search);
      const tokenValue = urlParams.get('tokenValue');
      const tokenName = urlParams.get('tokenName') || 'satoken'; // 默认使用satoken
      const userId = urlParams.get('userId');
      const userName = urlParams.get('userName');
      const userRole = urlParams.get('userRole');
      const email = urlParams.get('email');
      const userAvatar = urlParams.get('userAvatar');
      const sessionTimeout = urlParams.get('sessionTimeout');
      const tokenTimeout = urlParams.get('tokenTimeout');
      const loginDeviceType = urlParams.get('loginDeviceType');
      
      // 检查必要的参数是否存在
      if (tokenValue && tokenName) {
        console.log('检测到谷歌登录回调参数，处理登录状态');
        
        // 保存令牌
        tokenUtil.saveToken(tokenName, tokenValue);
        
        // 直接保存到satoken键，确保路由守卫可以正确识别登录状态
        localStorage.setItem('satoken', tokenValue);
        
        // 保存用户信息
        const userInfo = {
          userName,
          userRole,
          userId,
          email,
          sessionTimeout,
          tokenTimeout,
          loginDeviceType,
          userAvatar: userAvatar || ''
        };
        
        console.log('保存谷歌登录用户信息到localStorage:', userInfo);
        
        // 保存到localStorage
        localStorage.setItem('userInfo', JSON.stringify(userInfo));
        localStorage.setItem('isLoggedIn', 'true');
        
        // 发出登录成功事件
        emitter.emit('login-success', userInfo);
        
        // 显示成功消息
        ElMessage({
          message: 'Google login successful',
          type: 'success',
          duration: 2000
        });
        
        // 清除URL参数
        this.$router.replace({ 
          path: this.$route.query.redirect || '/',
          query: {} 
        });
        
        return true;
      }
      
      return false;
    },
    async login() {
      console.log('开始登录流程');
      // 检查表单是否有效
      if (!this.email || !this.password) {
        console.log('登录表单验证失败：邮箱或密码为空');
        ElMessage({
          message: 'Please enter email and password',
          type: 'warning',
          duration: 2000
        })
        return
      }
      
      try {
        console.log('表单验证通过，准备发送登录请求');
        this.isLoading = true
        
        console.log('调用登录API，参数：', { 
          email: this.email, 
          password: '***' // 密码不输出
        });

        //账号信息
        const params = {
          email: this.email,
          password: this.password,
          rememberMe: this.rememberMe
        }
        
        // 调用登录API
        const response = await loginApi(params)

        console.log('登录API响应成功:', response)
        
        // 检查响应状态码
        if (response && response.code === 200) {
          if (response.data) {
            const { 
              tokenName, 
              tokenValue,
              userName,
              userRole,
              userId,
              email,
              sessionTimeout,
              tokenTimeout,
              loginDeviceType,
              userAvatar
            } = response.data;
            
            if (tokenName && tokenValue) {
              console.log(`获取到令牌 ${tokenName}，保存到localStorage`);
              
              // 使用工具方法保存token
              tokenUtil.saveToken(tokenName, tokenValue);
              
              // 直接保存到satoken键，确保路由守卫可以正确识别登录状态
              localStorage.setItem('satoken', tokenValue);
              
              // 保存用户信息
              const userInfo = {
                userName,
                userRole,
                userId,
                email,
                sessionTimeout,
                tokenTimeout,
                loginDeviceType,
                userAvatar
              };
              
              console.log('保存用户信息到 localStorage:', userInfo);
              
              // 保存到localStorage以便全局访问
              localStorage.setItem('userInfo', JSON.stringify(userInfo));
              localStorage.setItem('isLoggedIn', 'true');
              
              if (this.rememberMe) {
                localStorage.setItem('rememberedEmail', this.email);
              }
              
              // 发出登录成功事件
              console.log('发送 login-success 事件，附带用户信息');
              emitter.emit('login-success', userInfo);
              
              ElMessage({
                message: 'Login successful',
                type: 'success',
                duration: 2000
              });
              
              console.log('登录成功，准备跳转');
              
              // 给界面一点时间来更新
              setTimeout(() => {
                // 跳转到指定页面
                const redirectPath = this.$route.query.redirect || '/';
                this.$router.push(redirectPath);
              }, 100);
              
              return;
            }
          }
          
          // 如果没有返回token信息
          console.log('响应中没有令牌信息', response);
          ElMessage({
            message: 'Login failed: Invalid server response',
            type: 'error',
            duration: 2000
          });
        } else {
          // 处理其他错误情况
          console.error('登录响应错误:', response);
          ElMessage({
            message: response.msg || 'Login failed',
            type: 'error',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('登录请求失败:', error);
        ElMessage({
          message: 'Login failed, please check email and password',
          type: 'error',
          duration: 2000
        });
      } finally {
        this.isLoading = false
        console.log('登录流程结束');
      }
    },

    // 主题变化处理
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode !== 'undefined') {
        this.isDarkMode = data.isDarkMode;
      }
    }
  },
  mounted() {
    // 先检查是否为谷歌登录回调
    const processed = this.processGoogleCallback();

    // 如果不是谷歌登录回调，继续执行原有逻辑
    if (!processed) {
      // 加载记住的邮箱
      const rememberedEmail = localStorage.getItem('rememberedEmail')
      if (rememberedEmail) {
        this.email = rememberedEmail
        this.rememberMe = true
      }
    }

    // 监听主题变化事件
    emitter.on('theme-changed', this.handleThemeChange);
    emitter.on('apply-theme-to-page', this.handleThemeChange);
  },

  beforeUnmount() {
    // 清理主题事件监听器
    emitter.off('theme-changed', this.handleThemeChange);
    emitter.off('apply-theme-to-page', this.handleThemeChange);
  }
}
</script>

<style scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  position: relative;
  /* 背景设置为透明，让StarryBackground显示 */
  background: transparent;
  background-image: none;
  z-index: 1; /* 确保高于星空背景 */
  transition: color 0.3s ease;
}

/* 深色主题页面 */
.login-page[data-theme="dark"],
.login-page.dark-theme {
  color: #e0e0e0;
}

/* 浅色主题页面 */
.login-page[data-theme="light"] {
  color: #2d3748;
}

.login-container {
  width: 100%;
  max-width: 420px;
  border-radius: 20px;
  padding: 2rem 2.5rem;
  position: relative;
  z-index: 5; /* 确保在星空背景之上 */
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

/* 深色主题登录容器 */
.login-page[data-theme="dark"] .login-container,
.login-page.dark-theme .login-container {
  background: linear-gradient(135deg, rgba(36, 36, 40, 0.75), rgba(26, 26, 32, 0.85));
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 30px rgba(120, 70, 200, 0.2);
  border: 1px solid rgba(120, 70, 200, 0.4);
}

.login-page[data-theme="dark"] .login-container:hover,
.login-page.dark-theme .login-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.5), 0 0 40px rgba(120, 70, 200, 0.4);
  border-color: rgba(120, 70, 200, 0.6);
}

/* 浅色主题登录容器 */
.login-page[data-theme="light"] .login-container {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.98));
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1), 0 0 30px rgba(147, 51, 234, 0.15);
  border: 1px solid rgba(147, 51, 234, 0.2);
}

.login-page[data-theme="light"] .login-container:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15), 0 0 40px rgba(147, 51, 234, 0.25);
  border-color: rgba(147, 51, 234, 0.4);
}

.login-header {
  text-align: center;
  margin-bottom: 1.5rem;
}

.logo {
  width: 120px;
  margin-bottom: 1rem;
  transition: filter 0.3s ease;
}

/* 深色主题Logo */
.login-page[data-theme="dark"] .logo,
.login-page.dark-theme .logo {
  filter: drop-shadow(0 0 10px rgba(195, 163, 255, 0.7));
}

/* 浅色主题Logo */
.login-page[data-theme="light"] .logo {
  filter: drop-shadow(0 0 10px rgba(147, 51, 234, 0.4));
}

.login-header h1 {
  font-size: 1.8rem;
  margin: 0;
  font-weight: 700;
  transition: all 0.3s ease;
}

/* 深色主题标题 */
.login-page[data-theme="dark"] .login-header h1,
.login-page.dark-theme .login-header h1 {
  color: #c3a3ff;
  text-shadow: 0 0 15px rgba(195, 163, 255, 0.5);
}

/* 浅色主题标题 */
.login-page[data-theme="light"] .login-header h1 {
  color: #7c3aed;
  text-shadow: 0 0 15px rgba(124, 58, 237, 0.3);
}

.login-form {
  margin-bottom: 1.5rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group:last-of-type {
  margin-bottom: 1rem; /* 减少最后一个表单组的底部间距 */
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* 深色主题表单标签 */
.login-page[data-theme="dark"] .form-group label,
.login-page.dark-theme .form-group label {
  color: #c3a3ff;
}

/* 浅色主题表单标签 */
.login-page[data-theme="light"] .form-group label {
  color: #7c3aed;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-height: 48px; /* 确保输入框有一致的高度 */
}

/* 密码输入框特殊处理 */
.password-input-container .form-input {
  padding-right: 2.8rem; /* 为密码切换按钮留出足够空间 */
}

/* 深色主题表单输入框 */
.login-page[data-theme="dark"] .form-input,
.login-page.dark-theme .form-input {
  border: 1px solid rgba(120, 70, 200, 0.3);
  background: rgba(35, 35, 40, 0.6);
  color: #e0e0e0;
}

.login-page[data-theme="dark"] .form-input:focus,
.login-page.dark-theme .form-input:focus {
  outline: none;
  border-color: #c3a3ff;
  box-shadow: 0 0 0 2px rgba(195, 163, 255, 0.2);
}

.login-page[data-theme="dark"] .form-input::placeholder,
.login-page.dark-theme .form-input::placeholder {
  color: #999;
}

/* 浅色主题表单输入框 */
.login-page[data-theme="light"] .form-input {
  border: 1px solid rgba(147, 51, 234, 0.3);
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
}

.login-page[data-theme="light"] .form-input:focus {
  outline: none;
  border-color: #7c3aed;
  box-shadow: 0 0 0 2px rgba(124, 58, 237, 0.2);
}

.login-page[data-theme="light"] .form-input::placeholder {
  color: #a0aec0;
}

.password-input-container {
  position: relative;
}

.toggle-password {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
}

/* 深色主题密码切换按钮 */
.login-page[data-theme="dark"] .toggle-password,
.login-page.dark-theme .toggle-password {
  color: #c3a3ff;
}

.login-page[data-theme="dark"] .toggle-password:hover,
.login-page.dark-theme .toggle-password:hover {
  color: #fff;
}

/* 浅色主题密码切换按钮 */
.login-page[data-theme="light"] .toggle-password {
  color: #7c3aed;
}

.login-page[data-theme="light"] .toggle-password:hover {
  color: #553c9a;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.8rem;
  margin-top: 0.5rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

/* 深色主题记住我 */
.login-page[data-theme="dark"] .remember-me,
.login-page.dark-theme .remember-me {
  color: #a9a9a9;
}

/* 浅色主题记住我 */
.login-page[data-theme="light"] .remember-me {
  color: #718096;
}

.remember-me input[type="checkbox"] {
  width: 16px;
  height: 16px;
  transition: accent-color 0.3s ease;
}

/* 深色主题复选框 */
.login-page[data-theme="dark"] .remember-me input[type="checkbox"],
.login-page.dark-theme .remember-me input[type="checkbox"] {
  accent-color: #c3a3ff;
}

/* 浅色主题复选框 */
.login-page[data-theme="light"] .remember-me input[type="checkbox"] {
  accent-color: #7c3aed;
}

.forgot-password {
  text-decoration: none;
  transition: color 0.3s ease;
}

/* 深色主题忘记密码链接 */
.login-page[data-theme="dark"] .forgot-password,
.login-page.dark-theme .forgot-password {
  color: #c3a3ff;
}

.login-page[data-theme="dark"] .forgot-password:hover,
.login-page.dark-theme .forgot-password:hover {
  color: #fff;
  text-decoration: underline;
}

/* 浅色主题忘记密码链接 */
.login-page[data-theme="light"] .forgot-password {
  color: #7c3aed;
}

.login-page[data-theme="light"] .forgot-password:hover {
  color: #553c9a;
  text-decoration: underline;
}

.login-btn {
  width: 100%;
  padding: 0.9rem;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 48px;
}

/* 深色主题登录按钮 */
.login-page[data-theme="dark"] .login-btn,
.login-page.dark-theme .login-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
}

.login-page[data-theme="dark"] .login-btn:hover,
.login-page.dark-theme .login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 30px rgba(120, 70, 200, 0.4);
  background: linear-gradient(135deg, #42306a, #352458);
  border-color: rgba(120, 70, 200, 0.6);
}

.login-page[data-theme="dark"] .login-btn:disabled,
.login-page.dark-theme .login-btn:disabled {
  background: linear-gradient(135deg, #2d2d35, #22222a);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

/* 浅色主题登录按钮 */
.login-page[data-theme="light"] .login-btn {
  background: linear-gradient(135deg, #7c3aed, #6d28d9);
  color: #ffffff;
  border: 1px solid rgba(124, 58, 237, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1), 0 0 20px rgba(124, 58, 237, 0.2);
}

.login-page[data-theme="light"] .login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), 0 0 30px rgba(124, 58, 237, 0.3);
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
  border-color: rgba(124, 58, 237, 0.5);
}

.login-page[data-theme="light"] .login-btn:disabled {
  background: linear-gradient(135deg, #a0aec0, #718096);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.7;
}

.login-btn:active {
  transform: translateY(0);
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transition: all 0.3s ease;
}

/* 深色主题加载动画 */
.login-page[data-theme="dark"] .loading-spinner,
.login-page.dark-theme .loading-spinner {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
}

/* 浅色主题加载动画 */
.login-page[data-theme="light"] .loading-spinner {
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.register-section {
  text-align: center;
  margin-bottom: 1.5rem;
  transition: color 0.3s ease;
}

/* 深色主题注册部分 */
.login-page[data-theme="dark"] .register-section,
.login-page.dark-theme .register-section {
  color: #a9a9a9;
}

/* 浅色主题注册部分 */
.login-page[data-theme="light"] .register-section {
  color: #718096;
}

.register-link {
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 深色主题注册链接 */
.login-page[data-theme="dark"] .register-link,
.login-page.dark-theme .register-link {
  color: #c3a3ff;
}

.login-page[data-theme="dark"] .register-link:hover,
.login-page.dark-theme .register-link:hover {
  color: #fff;
  text-decoration: underline;
}

/* 浅色主题注册链接 */
.login-page[data-theme="light"] .register-link {
  color: #7c3aed;
}

.login-page[data-theme="light"] .register-link:hover {
  color: #553c9a;
  text-decoration: underline;
}

.social-login {
  text-align: center;
}

.social-login p {
  margin-bottom: 1rem;
  position: relative;
  transition: color 0.3s ease;
}

/* 深色主题社交登录文本 */
.login-page[data-theme="dark"] .social-login p,
.login-page.dark-theme .social-login p {
  color: #a9a9a9;
}

/* 浅色主题社交登录文本 */
.login-page[data-theme="light"] .social-login p {
  color: #718096;
}

.social-login p::before,
.social-login p::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60px;
  height: 1px;
  transition: background 0.3s ease;
}

/* 深色主题分隔线 */
.login-page[data-theme="dark"] .social-login p::before,
.login-page[data-theme="dark"] .social-login p::after,
.login-page.dark-theme .social-login p::before,
.login-page.dark-theme .social-login p::after {
  background: rgba(120, 70, 200, 0.3);
}

/* 浅色主题分隔线 */
.login-page[data-theme="light"] .social-login p::before,
.login-page[data-theme="light"] .social-login p::after {
  background: rgba(124, 58, 237, 0.3);
}

.social-login p::before {
  left: 0;
}

.social-login p::after {
  right: 0;
}

.social-buttons {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.social-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid rgba(120, 70, 200, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.social-btn.google {
  background: linear-gradient(135deg, #db4437, #c62828);
}

.social-btn.facebook {
  background: linear-gradient(135deg, #4267B2, #365899);
}

.social-btn.github {
  background: linear-gradient(135deg, #333, #222);
}

.social-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.4), 0 0 15px rgba(120, 70, 200, 0.3);
}

.social-btn:active {
  transform: translateY(0);
}

/* 谷歌登录按钮样式 */
.google-login-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  border-radius: 25px;
  cursor: pointer;
  margin: 1.2rem auto 0;
  transition: all 0.3s ease;
  width: 100%;
  min-height: 48px;
}

/* 深色主题Google登录按钮 */
.login-page[data-theme="dark"] .google-login-btn,
.login-page.dark-theme .google-login-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  border: 1px solid rgba(120, 70, 200, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.login-page[data-theme="dark"] .google-login-btn:hover,
.login-page.dark-theme .google-login-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.1));
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3), 0 0 15px rgba(120, 70, 200, 0.3);
  transform: translateY(-2px);
}

/* 浅色主题Google登录按钮 */
.login-page[data-theme="light"] .google-login-btn {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 250, 252, 0.9));
  border: 1px solid rgba(124, 58, 237, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.login-page[data-theme="light"] .google-login-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 0.95));
  box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15), 0 0 15px rgba(124, 58, 237, 0.2);
  transform: translateY(-2px);
}

.google-login-btn img {
  width: 18px;
  height: 18px;
  margin-right: 10px;
  transition: filter 0.3s ease;
}

/* 深色主题Google图标 */
.login-page[data-theme="dark"] .google-login-btn img,
.login-page.dark-theme .google-login-btn img {
  filter: brightness(2);
}

/* 浅色主题Google图标 */
.login-page[data-theme="light"] .google-login-btn img {
  filter: brightness(1);
}

.google-login-btn span {
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
}

/* 深色主题Google按钮文字 */
.login-page[data-theme="dark"] .google-login-btn span,
.login-page.dark-theme .google-login-btn span {
  color: #e0e0e0;
}

/* 浅色主题Google按钮文字 */
.login-page[data-theme="light"] .google-login-btn span {
  color: #2d3748;
}

/* 移动端样式优化 */
@media (max-width: 768px) {
  .login-container {
    padding: 1.5rem;
  }
  
  .logo {
    width: 100px;
  }
  
  .login-header h1 {
    font-size: 1.5rem;
  }
  
  .social-login p::before,
  .social-login p::after {
    width: 40px;
  }
  
  .google-login-btn {
    padding: 12px 15px;
    max-width: 100%;
  }
  
  .google-login-btn span {
    font-size: 15px;
  }
}
</style>