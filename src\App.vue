<template>
  <div id="app" :class="{ 'mobile-device': isMobile, 'dark-theme': isDarkMode }" :data-theme="isDarkMode ? 'dark' : 'light'">
    <nav class="main-nav" :class="{ 'mobile': isMobile }" :style="{ background: isDarkMode ? 'rgba(77, 48, 105, 1)' : 'rgba(193, 151, 209, 1)' }">
      <div class="nav-container">
        <!-- Logo/品牌标题 -->
        <div class="nav-logo">
          <router-link to="/products">
            <BrandLogo />
          </router-link>
        </div>
        
        <!-- 移动端顶部工具栏 -->
        <div v-if="isMobile" class="mobile-top-toolbar">
          <!-- 在线用户数量显示 -->
          <div class="mobile-online-users" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">
            <img src="@/assets/person.png" alt="Users" class="users-icon" />
            <span class="online-count">{{ latestOnlineUsers }}</span>
          </div>
          
          <!-- 移动端语言切换器 -->
          <div class="mobile-language-selector" @click.stop="toggleMobileLanguageDropdown" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">
            <span class="language-code">{{ currentLanguage.toUpperCase() }}</span>
            <img src="@/assets/earth.png" alt="Language" class="earth-icon" />
            <!-- 移动端语言下拉菜单 -->
            <div class="mobile-language-dropdown" v-show="mobileLanguageDropdownVisible">
              <div 
                v-for="(name, code) in availableLanguages" 
                :key="code" 
                class="language-option"
                :class="{ 'active': currentLanguage === code }"
                @click.stop="changeLanguage(code)"
              >
                <span class="language-name">{{ name }}</span>
                <span class="language-code">{{ code.toUpperCase() }}</span>
              </div>
            </div>
          </div>
          
          <!-- 移动端货币切换器 -->
          <div class="mobile-currency-selector" @click.stop="toggleMobileCurrencyDropdown" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">
            <img src="@/assets/bizhong.png" alt="Currency" class="currency-icon-img" />
          <span class="currency-code">{{ currentCurrency }}</span>
            <!-- 移动端货币下拉菜单 -->
          <div class="mobile-currency-dropdown" v-show="mobileCurrencyDropdownVisible">
            <div 
              v-for="(symbol, code) in currencySymbols" 
              :key="code" 
              class="currency-option"
              :class="{ 'active': currentCurrency === code }"
              @click.stop="changeCurrency(code)"
            >
              <span class="currency-symbol">{{ symbol }}</span>
              <span class="currency-code">{{ code }}</span>
              </div>
            </div>
          </div>
          
          <!-- 移动端菜单按钮 -->
          <button class="mobile-menu-toggle" @click="toggleMobileMenu">
            <img src="@/assets/menu.png" alt="Menu" class="menu-icon" />
          </button>
        </div>
        
        <!-- 隐藏以前的移动端选择器容器 -->
        <div v-if="false" class="mobile-selectors-container">
          <!-- 内容被隐藏但保留代码以备需要 -->
        </div>
        
        <!-- 导航链接 - 在移动端为侧边栏，PC端保持原样 -->
        <div class="nav-links" :class="{ 'active': mobileMenuActive }">
          <!-- 用户信息区域 - 仅在移动端显示 -->
          <div v-if="isMobile" class="mobile-sidebar-user">
            <!-- 已登录状态 -->
            <template v-if="isLoggedIn">
              <div class="user-avatar-circle">
                <img :src="userAvatar || 'https://via.placeholder.com/70'" alt="User Avatar">
              </div>
              <div class="user-info-right">
                <div class="user-id-info">
                  <div class="user-id-label">USER ID</div>
                  <div class="user-id-number">ID : 411126662</div>
                </div>
                <!-- 主题切换按钮 -->
                <div class="user-theme-toggle" @click="toggleTheme">
                  <img :src="isDarkMode ? require('@/assets/Sun.png') : require('@/assets/Moon.png')" alt="Theme Toggle" class="theme-icon" />
                  <div class="toggle-switch">
                    <div class="toggle-track" :class="{ 'active': isDarkMode }">
                      <div class="toggle-thumb"></div>
                    </div>
                  </div>
                </div>
              </div>
            </template>

            <!-- 未登录状态 -->
            <template v-else>
              <div class="sidebar-login-prompt">
                <div class="login-icon-small">
                  <i class="fas fa-user-circle"></i>
                </div>
                <div class="login-info">
                  <div class="login-title-small">Please Login</div>
                  <div class="login-subtitle-small">Sign in to access your account</div>
                </div>
                <router-link to="/login" class="sidebar-login-btn" @click="closeMobileMenu">
                  <i class="fas fa-sign-in-alt"></i>
                </router-link>
              </div>
            </template>
          </div>
          
          <!-- 移动端导航 -->
          <template v-if="isMobile">
            <!-- 侧边栏小竖杠 -->
            <div class="sidebar-indicator" @click="closeMobileMenu"></div>
            
            <router-link to="/" class="sidebar-nav-item" @click="closeMobileMenu">
              <i class="fas fa-home"></i>
              <span>HOME</span>
            </router-link>
            
            <router-link to="/products" class="sidebar-nav-item" @click="closeMobileMenu">
              <i class="fas fa-shopping-bag"></i>
              <span>PRODUCTS</span>
            </router-link>
            
            <router-link to="/favorites" class="sidebar-nav-item" @click="closeMobileMenu">
              <i class="fas fa-heart"></i>
              <span>WISHLIST</span>
            </router-link>
            
            <router-link to="/sellers" class="sidebar-nav-item" @click="closeMobileMenu">
              <i class="fas fa-user"></i>
              <span>SELLER</span>
            </router-link>
            
            <router-link to="/forum" class="sidebar-nav-item" @click="closeMobileMenu">
              <i class="fas fa-comments"></i>
              <span>FORUM</span>
            </router-link>
            
            <router-link to="/how-to" class="sidebar-nav-item" @click="closeMobileMenu">
              <i class="fas fa-question-circle"></i>
              <span>HOW TO USE</span>
            </router-link>

            <!-- JOIN US 按钮 -->
            <div class="join-us-button">
              <div class="join-us-icons">
                <div class="join-icon discord-icon">
                  <i class="fab fa-discord"></i>
                </div>
                <div class="join-icon telegram-icon">
                  <i class="fab fa-telegram-plane"></i>
                </div>
              </div>
              <span class="join-us-text">JOIN US</span>
            </div>

          </template>
          
          <!-- PC端导航（恢复原来的） -->
          <template v-else>
            <router-link to="/" class="nav-link" @click="createRipple" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">{{ $t('navbar.home') }}</router-link>
            <router-link to="/products" class="nav-link" @click="createRipple" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">{{ $t('navbar.products') }}</router-link>
            <router-link to="/trending" class="nav-link" @click="createRipple" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">Trending</router-link>
            <router-link to="/sellers" class="nav-link" @click="createRipple" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">{{ $t('navbar.sellers') }}</router-link>
            <router-link to="/#faq-anchor" class="nav-link" :active-class="''" @click="createRipple" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">FAQ</router-link>
            <router-link to="/how-to" class="nav-link" @click="createRipple" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">{{ $t('navbar.howToBuy') }}</router-link>
          <router-link to="/community" class="nav-link" @click="createRipple">Community</router-link>
          <!-- <router-link to="/about" class="nav-link">About</router-link> -->
          
          <!-- Add currency switcher button -->
            <div class="currency-selector" @click="toggleCurrencyDropdown" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">
            <img src="@/assets/bizhong.png" alt="Currency" class="currency-icon-img" />
            <span class="currency-code">{{ currentCurrency }}</span>
            <i class="fas fa-chevron-down"></i>
            
            <!-- Currency dropdown menu -->
            <div class="currency-dropdown" v-show="currencyDropdownVisible">
              <div 
                v-for="(symbol, code) in currencySymbols" 
                :key="code" 
                class="currency-option"
                :class="{ 'active': currentCurrency === code }"
                @click.stop="changeCurrency(code)"
              >
                <span class="currency-symbol">{{ symbol }}</span>
                <span class="currency-code">{{ code }}</span>
              </div>
            </div>
          </div>
          
          <!-- Language selector button -->
            <div class="language-selector" @click="toggleLanguageDropdown" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">
            <span class="language-code">{{ currentLanguage.toUpperCase() }}</span>
            <img src="@/assets/earth.png" alt="Language" class="earth-icon" />
            <i class="fas fa-chevron-down"></i>
            
            <!-- Language dropdown menu -->
            <div class="language-dropdown" v-show="languageDropdownVisible">
              <div 
                v-for="(name, code) in availableLanguages" 
                :key="code" 
                class="language-option"
                :class="{ 'active': currentLanguage === code }"
                @click.stop="changeLanguage(code)"
              >
                <span class="language-name">{{ name }}</span>
                <span class="language-code">{{ code.toUpperCase() }}</span>
              </div>
            </div>
          </div>
            
            <!-- 主题切换按钮 -->
            <div class="theme-toggle" @click="toggleTheme" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">
              <div class="theme-icon">
                <i :class="isDarkMode ? 'fas fa-sun' : 'fas fa-moon'"></i>
            </div>
          </div>
          
          <!-- Login button or user avatar -->
          <div v-if="!isLoggedIn" class="auth-buttons">
            <router-link to="/login" class="nav-link login-button" @click="createRipple">
              <i class="fas fa-user"></i>
              <span>{{ $t('login.loginButton') }}</span>
            </router-link>
          </div>
          <div v-else class="user-profile" @click="toggleUserDropdown">
            <div class="avatar-container">
              <img :src="userAvatar || 'https://via.placeholder.com/40'" alt="User Avatar" class="user-avatar">
              <span class="user-name">{{ userName }}</span>
            </div>
            
            <!-- PC user dropdown menu -->
            <div v-if="!isMobile" class="user-dropdown" v-show="userDropdownVisible">
              <router-link to="/profile" class="dropdown-item">
                <i class="fas fa-user-circle"></i>
                <span>{{ $t('profile.title') }}</span>
              </router-link>
              <router-link to="/favorites" class="dropdown-item">
                <i class="fas fa-heart"></i>
                <span>{{ $t('profile.myFavorites') }}</span>
              </router-link>
              <router-link to="/favorite-sellers" class="dropdown-item">
                <i class="fas fa-store-alt"></i>
                <span>{{ $t('profile.favoriteSellers') }}</span>
              </router-link>
              <!-- <router-link to="/bookmarked-posts" class="dropdown-item">
                <i class="fas fa-bookmark"></i>
                <span>Bookmarked Posts</span>
              </router-link> -->
              <!-- <router-link to="/my-posts" class="dropdown-item">
                <i class="fas fa-file-alt"></i>
                <span>My Posts</span>
              </router-link> -->
              <!-- <router-link to="/points-center" class="dropdown-item">
                <i class="fas fa-coins"></i>
                <span>Points Center</span>
              </router-link> -->
              <div class="dropdown-divider"></div>
              <button class="dropdown-item logout" @click="logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>{{ $t('profile.logout') }}</span>
              </button>
            </div>
          </div>
          </template>
        </div>
      </div>
    </nav>
    
    <!-- Overlay, only shown when the mobile menu is open -->
    <div v-if="isMobile && mobileMenuActive" class="mobile-menu-overlay" @click="closeMobileMenu"></div>
    
    <div class="router-view-container">
      <router-view/>
      <FooterDisclaimer />
    </div>
    
    <FloatingSocial />
    
    <!-- Mobile user menu dialog - Custom implementation replacing Element Plus Dialog -->
    <div v-if="isMobile && mobileUserDialogVisible" class="custom-mobile-dialog-overlay" @click.self="mobileUserDialogVisible = false">
      <div class="custom-mobile-dialog">
        <div class="custom-dialog-header" :style="{ background: isDarkMode ? 'rgba(77, 48, 105, 1)' : 'rgba(193, 151, 209, 1)' }">
          <span class="custom-dialog-title" :style="{ color: isDarkMode ? '#ffffff' : '#333333' }">User Menu</span>
          <button class="custom-dialog-close" @click="mobileUserDialogVisible = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="mobile-user-menu">
          <!-- 已登录状态 -->
          <div v-if="isLoggedIn">
            <div class="mobile-user-info">
              <img :src="userAvatar || 'https://via.placeholder.com/80'" alt="User Avatar" class="mobile-user-avatar">
              <h3>{{ userName }}</h3>
            </div>
            <div class="mobile-menu-options">
              <router-link to="/profile" class="mobile-menu-item" @click="mobileUserDialogVisible = false">
                <i class="fas fa-user-circle"></i>
                <span>{{ $t('profile.title') }}</span>
              </router-link>
              <router-link to="/favorites" class="mobile-menu-item" @click="mobileUserDialogVisible = false">
                <i class="fas fa-heart"></i>
                <span>{{ $t('profile.myFavorites') }}</span>
              </router-link>
              <router-link to="/favorite-sellers" class="mobile-menu-item" @click="mobileUserDialogVisible = false">
                <i class="fas fa-store-alt"></i>
                <span>{{ $t('profile.favoriteSellers') }}</span>
              </router-link>
              <router-link to="/bookmarked-posts" class="mobile-menu-item" @click="mobileUserDialogVisible = false">
                <i class="fas fa-bookmark"></i>
                <span>Bookmarked Posts</span>
              </router-link>
              <router-link to="/my-posts" class="mobile-menu-item" @click="mobileUserDialogVisible = false">
                <i class="fas fa-file-alt"></i>
                <span>My Posts</span>
              </router-link>
              <router-link to="/points-center" class="mobile-menu-item" @click="mobileUserDialogVisible = false">
                <i class="fas fa-coins"></i>
                <span>Points Center</span>
              </router-link>
              <button class="mobile-menu-item logout" @click="logout">
                <i class="fas fa-sign-out-alt"></i>
                <span>{{ $t('profile.logout') }}</span>
              </button>
            </div>
          </div>

          <!-- 未登录状态 -->
          <div v-else class="mobile-login-prompt">
            <div class="login-icon">
              <i class="fas fa-user-circle"></i>
            </div>
            <h3 class="login-title">Please Login</h3>
            <p class="login-subtitle">Sign in to access your account and enjoy personalized features</p>
            <div class="login-actions">
              <router-link to="/login" class="mobile-login-button" @click="mobileUserDialogVisible = false">
                <i class="fas fa-sign-in-alt"></i>
                <span>Login</span>
              </router-link>
              <router-link to="/register" class="mobile-register-button" @click="mobileUserDialogVisible = false">
                <i class="fas fa-user-plus"></i>
                <span>Sign Up</span>
              </router-link>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Mobile Bottom Navigation -->
    <div v-if="false" class="mobile-bottom-nav" :style="{ background: isDarkMode ? 'rgba(77, 48, 105, 1)' : 'rgba(193, 151, 209, 1)' }">
      // ... existing mobile bottom nav content ...
    </div>
  </div>
</template>

<script>
import BrandLogo from '@/components/Logo.vue'
import FloatingSocial from '@/components/FloatingSocial.vue'
import { emitter } from '@/utils/eventBus'
import { ElMessage } from 'element-plus'
import i18n from './i18n'
import FooterDisclaimer from '@/components/FooterDisclaimer.vue'

export default {
  name: 'App',
  components: {
    BrandLogo,
    FloatingSocial,
    FooterDisclaimer
  },
  data() {
    // 从localStorage获取主题设置，如果没有则设为深色主题
    const savedDarkMode = localStorage.getItem('darkMode');
    const initialDarkMode = savedDarkMode !== null ? savedDarkMode === 'true' : true; // 默认深色主题
    
    // 保存默认设置
    if (savedDarkMode === null) {
      localStorage.setItem('darkMode', 'true');
    }
    
    return {
      isMobile: false,
      windowWidth: window.innerWidth,
      mobileMenuActive: false,
      currentCurrency: 'USD',
      currencyDropdownVisible: false,
      currentLanguage: localStorage.getItem('locale') || 'en',
      languageDropdownVisible: false,
      mobileLanguageDropdownVisible: false,
      availableLanguages: {
        'en': 'English',
        'fr': 'Français',
        'de': 'Deutsch',
        'es': 'Español',
        'it': 'Italiano',
        'pt': 'Português',
        'ru': 'Русский'
      },
      currencySymbols: {
        'USD': '$',
        'CNY': '¥',
        'EUR': '€',
        'GBP': '£',
        'JPY': '¥'
      },
      exchangeRates: {
        'USD': 1,
        'CNY': 7.2,
        'EUR': 0.93,
        'GBP': 0.79,
        'JPY': 150.5
      },
      isLoggedIn: false,
      userName: 'User',
      userAvatar: null,
      userDropdownVisible: false,
      mobileUserDialogVisible: false,
      mobileCurrencyDropdownVisible: false,
      ws: null,
      latestOnlineUsers: 0,
      isDarkMode: initialDarkMode // 使用初始化的主题设置
    }
  },
  created() {
    this.checkDevice();
    window.addEventListener('resize', this.onResize);
    // Load previously selected currency from local storage
    this.loadCurrencyPreference();
    // Click event listener to close currency dropdown menu
    document.addEventListener('click', this.handleGlobalClick);
    // Check login status
    this.checkLoginStatus();
    // Listen for login and logout events
    emitter.on('login-success', this.handleLoginSuccess);
    emitter.on('logout-success', this.handleLogoutSuccess);
    // 监听头像更新事件
    emitter.on('avatar-updated', this.handleAvatarUpdate);
    this.initOnlineUsersWebSocket();
    emitter.on('request-online-users', () => {
      emitter.emit('online-users', this.latestOnlineUsers);
    });
    // 初始化主题
    this.applyTheme();
  },
  mounted() {
    // Check login status again after DOM is loaded
    this.$nextTick(() => {
      console.log("DOM mounted, checking login status again");
      this.checkLoginStatus();
      
      // 在挂载后再次应用主题，确保所有元素都已经渲染
      this.applyTheme();
      
      // 监听路由变化
      this.$router.afterEach(() => {
        // 路由变化后，延迟一点再应用主题，确保新页面已渲染
        setTimeout(() => {
          this.applyTheme();
        }, 100);
      });
    });
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.onResize);
    document.removeEventListener('click', this.handleGlobalClick);
    // Clean up event listeners
    emitter.off('login-success', this.handleLoginSuccess);
    emitter.off('logout-success', this.handleLogoutSuccess);
    // 移除头像更新事件监听
    emitter.off('avatar-updated', this.handleAvatarUpdate);
    if (this.ws) {
      this.ws.close();
    }
  },
  methods: {
    checkDevice() {
      // Detect if it's a mobile device or desktop
      this.isMobile = window.innerWidth <= 768;
      document.documentElement.classList.toggle('mobile-device', this.isMobile);
      
      // If switching from mobile to desktop, close the mobile menu
      if (!this.isMobile && this.mobileMenuActive) {
        this.mobileMenuActive = false;
      }
    },
    onResize() {
      this.windowWidth = window.innerWidth;
      this.checkDevice();
    },
    toggleMobileMenu() {
      this.mobileMenuActive = !this.mobileMenuActive;
      // 使用类名控制而不是直接设置样式
      if (this.mobileMenuActive) {
        document.body.classList.add('mobile-menu-open');
        // 确保导航容器保持在原位
        document.querySelector('.nav-container').style.position = 'relative';
        document.querySelector('.nav-container').style.left = '0';
        document.querySelector('.nav-container').style.transform = 'none';
        // 确保LOGO和货币选择器在原位
        document.querySelector('.nav-logo').style.position = 'relative';
        document.querySelector('.nav-logo').style.left = '0';
        document.querySelector('.nav-logo').style.transform = 'none';
        if (document.querySelector('.mobile-currency-selector')) {
          document.querySelector('.mobile-currency-selector').style.position = 'relative';
          document.querySelector('.mobile-currency-selector').style.left = '0';
          document.querySelector('.mobile-currency-selector').style.transform = 'none';
        }
      } else {
        document.body.classList.remove('mobile-menu-open');
      }
    },
    closeMobileMenu() {
      this.mobileMenuActive = false;
      // 使用类名管理而不是直接设置样式
      document.body.classList.remove('mobile-menu-open');
      // 关闭所有下拉菜单
      this.closeAllDropdowns();
      // 清除所有可能在toggleMobileMenu中设置的内联样式
      document.querySelector('.nav-container').style.position = '';
      document.querySelector('.nav-container').style.left = '';
      document.querySelector('.nav-container').style.transform = '';
      document.querySelector('.nav-logo').style.position = '';
      document.querySelector('.nav-logo').style.left = '';
      document.querySelector('.nav-logo').style.transform = '';
      if (document.querySelector('.mobile-currency-selector')) {
        document.querySelector('.mobile-currency-selector').style.position = '';
        document.querySelector('.mobile-currency-selector').style.left = '';
        document.querySelector('.mobile-currency-selector').style.transform = '';
      }
    },
    // Show/hide currency dropdown menu
    toggleCurrencyDropdown(event) {
      event.stopPropagation();
      // Close user dropdown menu first
      this.userDropdownVisible = false;
      this.currencyDropdownVisible = !this.currencyDropdownVisible;
    },
    // Close currency dropdown menu
    closeCurrencyDropdown() {
      if (this.currencyDropdownVisible) {
        this.currencyDropdownVisible = false;
      }
    },
    // Toggle language dropdown menu
    toggleLanguageDropdown(event) {
      event.stopPropagation();
      // Close other dropdown menus first
      this.userDropdownVisible = false;
      this.currencyDropdownVisible = false;
      this.languageDropdownVisible = !this.languageDropdownVisible;
    },
    // Close language dropdown menu
    closeLanguageDropdown() {
      if (this.languageDropdownVisible) {
        this.languageDropdownVisible = false;
      }
    },
    // Change language
    changeLanguage(langCode) {
      this.currentLanguage = langCode;
      localStorage.setItem('locale', langCode);
      i18n.global.locale.value = langCode;
      emitter.emit('language-changed', langCode);
      this.languageDropdownVisible = false;
      this.mobileLanguageDropdownVisible = false;
    },
    // Toggle user dropdown menu
    toggleUserDropdown(event) {
      event.stopPropagation();
      
      // Close currency dropdown menu first
      this.currencyDropdownVisible = false;
      
      if (this.isMobile) {
        // Show dialog on mobile
        this.mobileUserDialogVisible = true;
      } else {
        // Toggle dropdown menu on PC
        this.userDropdownVisible = !this.userDropdownVisible;
      }
    },
    // Close all dropdown menus
    closeAllDropdowns() {
      this.currencyDropdownVisible = false;
      this.userDropdownVisible = false;
      this.mobileCurrencyDropdownVisible = false;
      this.languageDropdownVisible = false;
      this.mobileLanguageDropdownVisible = false;
      // Do not close mobile dialog, it has its own close button
    },
    // Handle login success event
    handleLoginSuccess(userInfo) {
      console.log("Login success event received", userInfo);
      this.isLoggedIn = true;
      this.userName = userInfo.userName || 'User';
      // Set user avatar
      this.userAvatar = userInfo.userAvatar || null;
    },
    
    // Handle logout success event
    handleLogoutSuccess() {
      console.log("Logout success event received");
      this.isLoggedIn = false;
      this.userName = 'User';
      this.userAvatar = null;
    },
    
    // Check user login status and load user info
    checkLoginStatus() {
      console.log("Checking login status");
      // Check login status directly from localStorage
      const isLoggedInValue = localStorage.getItem('isLoggedIn');
      console.log("isLoggedIn value:", isLoggedInValue);
      
      // 检查是否有token
      const hasToken = !!localStorage.getItem('satoken');
      console.log("Has satoken:", hasToken);
      
      // 如果两者都存在，才认为是已登录状态
      this.isLoggedIn = (isLoggedInValue === 'true' && hasToken);
      console.log("Setting isLoggedIn to:", this.isLoggedIn);
      
      if (this.isLoggedIn) {
        try {
          const userInfoStr = localStorage.getItem('userInfo');
          if (userInfoStr) {
            const userInfo = JSON.parse(userInfoStr);
            this.userName = userInfo.userName || 'User';
            // Set user avatar
            this.userAvatar = userInfo.userAvatar || null;
            console.log("Loaded user info:", userInfo);
          }
        } catch (e) {
          console.error('Failed to parse user info:', e);
        }
      } else {
        // 如果isLoggedIn为true但没有token，清除登录状态
        if (isLoggedInValue === 'true' && !hasToken) {
          localStorage.removeItem('isLoggedIn');
          console.log("Cleared inconsistent login state");
        }
      }
    },
    // User logout
    logout() {
      // Clear login status and token
      localStorage.removeItem('isLoggedIn');
      localStorage.removeItem('satoken');
      localStorage.removeItem('userInfo');
      
      // 清除所有可能的token
      const tokenName = localStorage.getItem('tokenName');
      if (tokenName) {
        localStorage.removeItem(tokenName);
        localStorage.removeItem('tokenName');
      }
      localStorage.removeItem('tokenValue');
      
      // Hide dropdown menus and dialog
      this.userDropdownVisible = false;
      this.mobileUserDialogVisible = false;
      
      // Update status
      this.isLoggedIn = false;
      this.userName = 'User';
      this.userAvatar = null;
      
      // Emit logout success event
      emitter.emit('logout-success');
      
      // Redirect to homepage or login page
      this.$router.push('/');
      
      // Display logout success message
      ElMessage({
        message: 'Logged out successfully',
        type: 'success'
      });
    },
    // Change currency
    changeCurrency(currencyCode) {
      if (this.currentCurrency !== currencyCode) {
        this.currentCurrency = currencyCode;
        // Save user's currency preference to local storage
        localStorage.setItem('preferredCurrency', currencyCode);
        // Use mitt to publish currency change event
        emitter.emit('currency-changed', {
          currency: currencyCode,
          symbol: this.currencySymbols[currencyCode],
          rate: this.exchangeRates[currencyCode]
        });
      }
      this.currencyDropdownVisible = false;
    },
    // Load user's preferred currency from local storage
    loadCurrencyPreference() {
      const savedCurrency = localStorage.getItem('preferredCurrency');
      if (savedCurrency && this.currencySymbols[savedCurrency]) {
        this.currentCurrency = savedCurrency;
      }
    },
    createRipple(event) {
      // Get the clicked element
      const button = event.currentTarget;
      
      // Create ripple element
      const circle = document.createElement("span");
      const diameter = Math.max(button.clientWidth, button.clientHeight);
      const radius = diameter / 2;
      
      // Calculate the click position relative to the button
      const rect = button.getBoundingClientRect();
      const x = event.clientX - rect.left - radius;
      const y = event.clientY - rect.top - radius;
      
      // Set ripple styles
      circle.style.width = circle.style.height = `${diameter}px`;
      circle.style.left = `${x}px`;
      circle.style.top = `${y}px`;
      circle.classList.add("ripple");
      
      // Remove existing ripples
      const ripple = button.querySelector(".ripple");
      if (ripple) {
        ripple.remove();
      }
      
      // Add new ripple
      button.appendChild(circle);
      
      // Remove after ripple animation ends
      setTimeout(() => {
        circle.remove();
      }, 600);
    },
    // Handle global click to close dropdown menus
    handleGlobalClick() {
      this.closeAllDropdowns();
    },
    toggleMobileCurrencyDropdown() {
      // 关闭语言下拉菜单
      this.mobileLanguageDropdownVisible = false;
      this.mobileCurrencyDropdownVisible = !this.mobileCurrencyDropdownVisible;
    },
    toggleMobileLanguageDropdown() {
      // 关闭货币下拉菜单
      this.mobileCurrencyDropdownVisible = false;
      this.mobileLanguageDropdownVisible = !this.mobileLanguageDropdownVisible;
    },
    // 处理头像更新事件
    handleAvatarUpdate(data) {
      console.log('Received avatar update event:', data);
      if (data && data.userAvatar) {
        this.userAvatar = data.userAvatar;
        
        // 为了确保视图更新，可以添加时间戳参数避免缓存
        const timestamp = Date.now();
        const avatarWithTimestamp = data.userAvatar.includes('?') 
          ? `${data.userAvatar}&t=${timestamp}` 
          : `${data.userAvatar}?t=${timestamp}`;
        
        // 更新DOM中的头像元素
        const avatarElements = document.querySelectorAll('.user-avatar');
        avatarElements.forEach(element => {
          element.src = avatarWithTimestamp;
        });
      }
    },
    initOnlineUsersWebSocket() {
      if (this.ws) {
        this.ws.close();
      }
      // 动态获取当前域名
      this.ws = new window.WebSocket(`wss://${window.location.host}/ws/online`);
      this.ws.onopen = () => {
        // 连接成功
      };
      this.ws.onmessage = (event) => {
        const count = Number(event.data);
        this.latestOnlineUsers = count;
        emitter.emit('online-users', count);
      };
      this.ws.onclose = () => {
        setTimeout(this.initOnlineUsersWebSocket, 2000);
      };
      this.ws.onerror = () => {
        this.ws.close();
      };
    },
    // 添加主题切换相关方法
    toggleTheme() {
      this.isDarkMode = !this.isDarkMode;
      localStorage.setItem('darkMode', this.isDarkMode);
      this.applyTheme();
      
      // 使用emitter广播主题变化
      emitter.emit('theme-changed', { isDarkMode: this.isDarkMode });
    },
    applyTheme() {
      if (this.isDarkMode) {
        document.documentElement.classList.add('dark-theme');
        document.documentElement.setAttribute('data-theme', 'dark');
        document.documentElement.style.setProperty('--primary-bg', 'linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%)');
        document.documentElement.style.setProperty('--nav-bg', 'rgba(77, 48, 105, 1)');
        document.documentElement.style.setProperty('--text-color', '#ffffff');
      } else {
        document.documentElement.classList.remove('dark-theme');
        document.documentElement.setAttribute('data-theme', 'light');
        document.documentElement.style.setProperty('--primary-bg', 'linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%)');
        document.documentElement.style.setProperty('--nav-bg', 'rgba(193, 151, 209, 1)');
        document.documentElement.style.setProperty('--text-color', '#333333');
      }
      
      // 通过事件总线通知所有页面组件主题已变更
      setTimeout(() => {
        emitter.emit('apply-theme-to-page', { isDarkMode: this.isDarkMode });
      }, 50);
    },
  },
  watch: {
    $route() {
      // Close mobile menu on route change
      if (this.mobileMenuActive) {
        this.closeMobileMenu();
      }
      // Close all dropdown menus
      this.closeAllDropdowns();
      
      // 路由变化时重新应用主题
      setTimeout(() => {
        this.applyTheme();
        // 广播路由变化事件
        emitter.emit('route-changed', { path: this.$route.path, isDarkMode: this.isDarkMode });
      }, 100);
    }
  },
  provide() {
    return {
      getCurrencyInfo: () => {
        return {
          currency: this.currentCurrency,
          symbol: this.currencySymbols[this.currentCurrency],
          rate: this.exchangeRates[this.currentCurrency]
        };
      },
      getThemeInfo: () => {
        return {
          isDarkMode: this.isDarkMode,
          themeColors: {
            background: this.isDarkMode ? 'rgba(77, 48, 105, 1)' : 'rgba(193, 151, 209, 1)',
            text: this.isDarkMode ? '#ffffff' : '#333333'
          }
        }
      }
    };
  }
}
</script>

<style>
/* 主题相关CSS变量 */
:root {
  /* 浅色主题变量 */
  --primary-bg: linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%);
  --nav-bg: rgba(193, 151, 209, 1);
  --text-color: #333333;
  --heading-color: #230F48;
  --card-bg: #ffffff;
  --search-bg: rgba(255, 255, 255, 0.5);
  --button-bg: rgba(255, 255, 255, 0.7);
  --border-color: rgba(76, 175, 80, 0.15);
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --primary-color: #9333ea;
  --secondary-color: #c084fc;
  --dark-bg: #ffffff;
  --light-bg: #f9f9f9;
  --text-dark: #2e7d32;
  --card-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
  --card-border: 1px solid rgba(76, 175, 80, 0.1);
  --transition-speed: 0.3s;
  --nav-text: #333333;
  --nav-active: #230F48;
  --nav-hover-bg: rgba(147, 51, 234, 0.15);
  --nav-active-border: #9333ea;
  --dropdown-bg: rgba(255, 255, 255, 0.95);
  --mobile-menu-bg: linear-gradient(125deg, #f9f5fc 0%, #ffffff 50%, #f9f5fc 100%);
  --mobile-menu-border: rgba(147, 51, 234, 0.2);
}

/* 深色主题变量 */
.dark-theme {
  --primary-bg: linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%);
  --nav-bg: rgba(77, 48, 105, 1);
  --text-color: #ffffff;
  --heading-color: #ffffff;
  --card-bg: rgba(30, 9, 64, 0.8);
  --search-bg: rgba(30, 9, 64, 0.8);
  --button-bg: rgba(50, 20, 80, 0.7);
  --border-color: rgba(147, 51, 234, 0.2);
  --shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  --primary-color: #a855f7;
  --secondary-color: #c084fc;
  --dark-bg: #0a0a12;
  --light-bg: #1a1a20;
  --text-dark: #e0e0e0;
  --card-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  --card-border: 1px solid rgba(147, 51, 234, 0.2);
  --nav-text: #ffffff;
  --nav-active: #ffffff;
  --nav-hover-bg: rgba(147, 51, 234, 0.15);
  --nav-active-border: #c084fc;
  --dropdown-bg: rgba(20, 20, 30, 0.95);
  --mobile-menu-bg: linear-gradient(125deg, #0a0a12 0%, #141428 50%, #0a0a12 100%);
  --mobile-menu-border: rgba(147, 51, 234, 0.2);
  background: linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%) !important;
  color: #ffffff !important;
}

/* 基础样式 */
html, body {
  margin: 0;
  padding: 0;
  background: var(--primary-bg);
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
  color: var(--text-color);
  transition: background 0.5s ease, color 0.5s ease;
}

/* Global styles for mobile bottom nav spacing */
@media (max-width: 768px) {
  body {
    padding-bottom: 70px; /* Add padding to body to account for the fixed bottom nav */
    padding-top: 60px; /* Add padding to account for fixed navbar */
  }
  
  #app {
    min-height: calc(100vh - 70px);
  }
}

/* 主题切换按钮样式 */
.theme-toggle, .mobile-theme-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  cursor: pointer;
  margin: 0 5px;
  border: 1px solid var(--border-color);
  transition: all 0.5s ease;
}

.theme-toggle:hover, .mobile-theme-toggle:hover {
  background: var(--nav-hover-bg);
  box-shadow: var(--shadow);
}

.theme-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: var(--nav-text);
  transition: all 0.5s ease;
}

.dark-theme .theme-icon {
  color: #f1c40f;
}

/* 导航栏样式 */
.main-nav {
  background: var(--nav-bg) !important;
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow);
  transition: all 0.5s ease;
  /* 删除紫色底部边框 */
  /* border-bottom: 1px solid rgba(147, 51, 234, 0.2); */
}

.main-nav:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
}

/* Mobile navigation adaptation */
@media (max-width: 768px) {
  body.mobile-menu-open {
    overflow: hidden;
  }
  
  .nav-logo {
    height: 30px;
    position: relative;
    z-index: 101;
  }
  
  .main-nav {
    padding: 0.6rem 0.8rem;
    background: var(--nav-bg) !important;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 95;
    transform: none !important;
  }
  
  .main-nav:hover {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  }

  .mobile-menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 34px;
    height: 34px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    padding: 4px;
    z-index: 110;
    transition: all 0.3s ease;
  }
  

}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1400px;
  margin: 0 auto;
}

.nav-logo {
  height: 40px;
  transition: transform 0.3s ease;
}

.nav-logo:hover {
  transform: scale(1.05);
}

.nav-links {
  display: flex;
  gap: 1.5rem;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 4px;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
}

.nav-link::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: #9333ea;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-link:hover {
  background: rgba(147, 51, 234, 0.15);
  color: #ffffff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(147, 51, 234, 0.3);
}

.nav-link:hover::after {
  width: 80%;
  background: #a855f7;
}

.nav-link:active {
  transform: translateY(0) scale(0.95);
  box-shadow: 0 2px 4px rgba(69, 104, 220, 0.2);
}

.nav-link.router-link-active {
  color: #ffffff;
  background: rgba(147, 51, 234, 0.2);
  font-weight: 600;
  box-shadow: 0 4px 10px rgba(147, 51, 234, 0.25);
  border-bottom: 2px solid #9333ea;
}

.nav-link.router-link-active::after {
  width: 100%;
  background: #9333ea;
}

.nav-link.router-link-active:hover {
  background: rgba(69, 104, 220, 0.15);
  box-shadow: 0 5px 15px rgba(69, 104, 220, 0.2);
  color: #7b88ff;
}

/* Add click ripple effect */
.nav-link {
  position: relative;
}

.nav-link .ripple {
  position: absolute;
  border-radius: 50%;
  background: rgba(123, 136, 255, 0.4);
  transform: scale(0);
  animation: ripple 0.6s linear;
  pointer-events: none;
}

@keyframes ripple {
  to {
    transform: scale(2.5);
    opacity: 0;
  }
}

/* Mobile navigation styles */
.mobile-menu-toggle {
  display: none;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  cursor: pointer;
  padding: 4px;
  z-index: 110;
  transition: all 0.3s ease;
}

/* Hide unwanted 'CATEGORIES' text in mobile menu */
/* .mobile-device .nav-links:after,
.nav-links.active:after,
.mobile-device .nav-links a:after,
.nav-links.active a:after,
.mobile-device a.CATEGORIES,
.nav-links.active .CATEGORIES,
.mobile-device .CATEGORIES,
.nav-links.active a[href="/categories"] {
  display: none !important;
} */

/* 修改：允许产品和品牌分类标题在移动端显示 */
/* 原来的隐藏标题规则 
.mobile-device .section-title,
.mobile-device .nav-links h3,
.nav-links.active h3,
.mobile-device .nav-links .section-title,
.nav-links.active .section-title {
  display: none !important;
} */

/* 仅隐藏导航栏内的标题，不隐藏产品页面中的分类标题 */
.mobile-device .nav-links .section-title,
.nav-links.active .section-title {
  display: none !important;
}

/* 确保产品页面中的分类标题始终显示 */
.mobile-device .filters-section .section-title,
.mobile-device .brands-grid-container .section-title,
.mobile-device .category-header .section-title,
.mobile-device .product-section-title {
  display: inline-block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Only hide specific category text in mobile menu, not all buttons */
.mobile-menu-overlay .unwanted-category-text {
  display: none !important;
}

/* Keep mobile menu buttons visible */
.mobile-menu-overlay button.nav-link,
.mobile-menu-overlay a.nav-link,
.mobile-menu-overlay .currency-option,
.mobile-menu-overlay .user-options button {
  display: flex !important;
}

/* Clear any problematic styles for mobile menu */
.nav-links.active {
  position: fixed;
}

/* This clears any pseudo-elements that might be displaying the text */
.nav-links.active:before,
.nav-links.active:after {
  content: none !important;
}

.mobile-menu-toggle:hover {
  transform: scale(1.1);
}

.mobile-menu-toggle:active {
  transform: scale(0.9);
}



.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(10, 10, 18, 0.8);
  z-index: 90;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Mobile navigation adaptation */
@media (max-width: 768px) {
  body.mobile-menu-open {
    overflow: hidden;
  }
  
  .nav-logo {
    height: 30px;
    position: relative;
    z-index: 101;
  }
  
  .main-nav {
    padding: 0.6rem 0.8rem;
    background: rgba(20, 20, 30, 0.95);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
    z-index: 95;
    transform: none !important;
  }
  
  .main-nav:hover {
    background: rgba(25, 25, 35, 0.98);
    box-shadow: 0 4px 15px rgba(120, 70, 200, 0.3);
  }

  .mobile-menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 34px;
    height: 34px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    padding: 4px;
    z-index: 110;
    transition: all 0.3s ease;
  }
  

  
  /* 移动端选择器容器 */
  .mobile-selectors-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-right: 0;
    position: relative;
    z-index: 90;
  }
  
  /* 移动端货币选择器 */
  .mobile-currency-selector,
  .mobile-language-selector,
  .mobile-theme-toggle,
  .mobile-online-users {
    padding: 0.5rem 0.75rem;
    background: rgba(54, 57, 89, 0.6);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--nav-text);
    border: 1px solid var(--border-color);
    margin: 0;
    position: relative;
    height: 36px;
  }
  
  .mobile-theme-toggle {
    width: 36px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .nav-links {
    position: fixed;
    top: 0;
    right: -100%;
    width: 70%;
    max-width: 300px;
    height: 100vh;
    background: #0a0a12;
    background-image: linear-gradient(125deg, #0a0a12 0%, #141428 50%, #0a0a12 100%);
    flex-direction: column;
    padding: 5rem 2rem 2rem;
    transition: right 0.3s ease;
    z-index: 100;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.5), -2px 0 10px rgba(120, 70, 200, 0.3);
    border-left: 1px solid rgba(120, 70, 200, 0.2);
  }

  .nav-links.active {
    right: 0;
  }

  .nav-link {
    padding: 1rem;
    width: 100%;
    border-bottom: 1px solid rgba(120, 70, 200, 0.2);
    font-size: 1.1rem;
    color: #e0e0e0;
  }
  
  .nav-link:hover {
    background: rgba(120, 70, 200, 0.2);
    color: #c3a3ff;
    box-shadow: 0 2px 5px rgba(120, 70, 200, 0.3);
    transform: none;
  }
  
  .nav-link:hover::after {
    width: 0;
  }
  
  .nav-link.router-link-active {
    background: rgba(120, 70, 200, 0.3);
    color: #c3a3ff;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(120, 70, 200, 0.4);
    border-bottom: none;
  }
  
  .discord-link {
    margin-left: 0;
    margin-top: 1rem;
    background: rgba(88, 101, 242, 0.2);
    justify-content: center;
    padding: 1rem;
    border-radius: 8px;
    color: #8990ff;
    border-color: rgba(88, 101, 242, 0.4);
  }
  
  .discord-icon {
    color: #8990ff;
  }
}

/* Prevent page content from being covered by the navigation bar */
@media (max-width: 768px) {
  /* 移除重复的padding-top定义，避免冲突 */
  /* body {
    padding-top: 60px;
  } */
  
  .main-nav.mobile {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    right: 0;
    z-index: 95;
  }
}



/* Discord link specific styles */
.discord-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: rgba(88, 101, 242, 0.2);
  border: 1px solid rgba(88, 101, 242, 0.4);
  margin-left: auto;
  color: #fff;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.discord-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(88, 101, 242, 1);
  transition: all 0.3s ease;
}

.discord-link:hover {
  background: rgba(88, 101, 242, 0.3);
  color: #fff;
  border-color: rgba(88, 101, 242, 0.6);
  transform: translateY(-2px);
}

.discord-link:hover .discord-icon {
  color: #fff;
  transform: scale(1.1);
}

.discord-link:active {
  transform: translateY(0) scale(0.95);
}

.discord-link.router-link-active {
  background: rgba(88, 101, 242, 0.4);
  border-color: rgba(88, 101, 242, 0.6);
}

/* Mobile navigation adaptation */
@media (max-width: 768px) {
  .nav-logo {
    height: 30px;
  }
  
  .main-nav {
    padding: 0.6rem 0.8rem;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .nav-links {
    position: fixed;
    top: 0;
    right: -100%;
    width: 70%;
    max-width: 300px;
    height: 100vh;
    background: #0a0a12;
    background-image: linear-gradient(125deg, #0a0a12 0%, #141428 50%, #0a0a12 100%);
    flex-direction: column;
    padding: 5rem 2rem 2rem;
    transition: right 0.3s ease;
    z-index: 100;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.5), -2px 0 10px rgba(120, 70, 200, 0.3);
    border-left: 1px solid rgba(120, 70, 200, 0.2);
  }

  .nav-links.active {
    right: 0;
  }

  .nav-link:hover {
    background: rgba(120, 70, 200, 0.2);
    color: #c3a3ff;
    box-shadow: 0 2px 5px rgba(120, 70, 200, 0.3);
  }
  
  .nav-link:hover::after {
    width: 0;
  }
  
  .nav-link.router-link-active {
    background: rgba(120, 70, 200, 0.3);
    color: #c3a3ff;
    font-weight: 600;
    box-shadow: 0 2px 5px rgba(120, 70, 200, 0.4);
    border-bottom: none;
  }
  
  .discord-link {
    margin-left: 0;
    margin-top: 1rem;
    background: rgba(88, 101, 242, 0.2);
    justify-content: center;
    padding: 1rem;
    border-radius: 8px;
    color: #8990ff;
    border-color: rgba(88, 101, 242, 0.4);
  }
  
  .discord-icon {
    color: #8990ff;
  }
}

/* Currency selector styles */
.currency-selector {
  position: relative;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.5s ease;
  margin-right: 0.5rem;
  color: var(--nav-text);
  border: 1px solid var(--border-color);
}

.currency-selector:hover {
  background: var(--nav-hover-bg);
  box-shadow: var(--shadow);
}

.currency-icon {
  font-weight: bold;
  color: #ac7cdd;
}

.currency-code {
  font-size: 0.9rem;
}

.currency-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  min-width: 140px;
  z-index: 101;
  margin-top: 0.5rem;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
  width: 100%;
  border: 1px solid var(--border-color);
}

/* 深色主题下的货币下拉菜单 */
[data-theme="dark"] .currency-dropdown {
  background: rgba(28, 30, 44, 0.95);
  border-color: rgba(120, 70, 200, 0.2);
}

/* 浅色主题下的货币下拉菜单 */
[data-theme="light"] .currency-dropdown {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(147, 51, 234, 0.1);
}

.currency-option {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
}

/* 深色主题下的货币选项 */
[data-theme="dark"] .currency-option {
  color: white;
}

/* 浅色主题下的货币选项 */
[data-theme="light"] .currency-option {
  color: #333;
}

/* Language selector styles */
.language-selector {
  position: relative;
  padding: 0.5rem 0.75rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.5s ease;
  margin-right: 1rem;
  color: white;
  border: 1px solid rgba(120, 70, 200, 0.2);
}

.language-selector:hover {
  background: rgba(120, 70, 200, 0.2);
  box-shadow: 0 2px 5px rgba(120, 70, 200, 0.3);
}

.language-selector i {
  font-size: 0.9rem;
}

.language-selector .earth-icon {
  width: 14px;
  height: 14px;
  margin-right: -0.2rem;
  object-fit: contain;
  filter: brightness(0) invert(1); /* 深色主题：白色图标 */
}

[data-theme="light"] .language-selector .earth-icon {
  filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
}

.language-code {
  font-size: 0.9rem;
  font-weight: 500;
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  min-width: 140px;
  z-index: 101;
  margin-top: 0.5rem;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
  width: 100%;
  border: 1px solid var(--border-color);
}

/* 深色主题下的语言下拉菜单 */
[data-theme="dark"] .language-dropdown {
  background: rgba(28, 30, 44, 0.95);
  border-color: rgba(120, 70, 200, 0.2);
}

/* 浅色主题下的语言下拉菜单 */
[data-theme="light"] .language-dropdown {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(147, 51, 234, 0.1);
}

.language-option {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
}

/* 深色主题下的语言选项 */
[data-theme="dark"] .language-option {
  color: white;
}

/* 浅色主题下的语言选项 */
[data-theme="light"] .language-option {
  color: #333;
}

/* 语言名称样式 */
.language-name {
  font-size: 0.9rem;
}

/* 语言代码样式 */
[data-theme="dark"] .language-dropdown .language-code {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="light"] .language-dropdown .language-code {
  font-size: 0.8rem;
  color: rgba(51, 51, 51, 0.6);
}

.mobile-currency-dropdown,
.mobile-language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(28, 30, 44, 0.98);
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  min-width: 120px;
  z-index: 101;
  margin-top: 0.5rem;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
  width: 100%;
}

.mobile-currency-dropdown {
  border: 1px solid rgba(120, 70, 200, 0.2);
}

.mobile-language-dropdown {
  border: 1px solid rgba(120, 70, 200, 0.2);
  min-width: 140px;
}

.currency-option {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  transition: all 0.2s ease;
  color: white;
}

.currency-option:hover {
  background: rgba(120, 70, 200, 0.2);
}

.currency-option.active {
  background: rgba(120, 70, 200, 0.3);
  font-weight: 600;
}

.language-option {
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  color: white;
}

.language-option:hover {
  background: rgba(120, 70, 200, 0.2);
}

.language-option.active {
  background: rgba(120, 70, 200, 0.3);
  font-weight: 600;
}

.language-name {
  font-size: 0.9rem;
}

.language-dropdown .language-code {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.6);
}

.currency-symbol {
  font-weight: bold;
  color: #ac7cdd;
}

@media (max-width: 768px) {
  /* 移动端选择器容器样式 */
  .mobile-selectors-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-right: 0;
    position: relative;
    z-index: 90;
  }
  
  /* 移动端货币选择器样式 */
  .mobile-currency-selector {
    padding: 0.5rem 0.75rem;
    background: rgba(54, 57, 89, 0.6);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    border: 1px solid rgba(120, 70, 200, 0.2);
    margin: 0;
    position: relative;
  }
  
  .mobile-currency-selector:active {
    background: rgba(120, 70, 200, 0.4);
  }
  
  /* 移动端语言选择器样式 */
  .mobile-language-selector {
    padding: 0.5rem 0.75rem;
    background: rgba(54, 57, 89, 0.6);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: white;
    border: 1px solid rgba(120, 70, 200, 0.2);
    margin: 0;
    position: relative;
  }
  
  .mobile-language-selector:active {
    background: rgba(120, 70, 200, 0.4);
  }
  
  .mobile-language-selector .earth-icon {
    width: 14px;
    height: 14px;
    margin-right: -0.2rem;
    object-fit: contain;
    filter: brightness(0) invert(1); /* 深色主题：白色图标 */
  }

  [data-theme="light"] .mobile-language-selector .earth-icon {
    filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
  }
  
  /* 移动端下拉菜单样式 */
  .mobile-currency-dropdown,
  .mobile-language-dropdown {
    position: absolute;
    width: 140px;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: rgba(28, 30, 44, 0.98);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    border-radius: 4px;
    z-index: 103;
    overflow: hidden;
  }
  
  .mobile-currency-dropdown {
    border: 1px solid rgba(120, 70, 200, 0.2);
  }
  
  .mobile-language-dropdown {
    border: 1px solid rgba(120, 70, 200, 0.2);
  }
  
  .currency-option, 
  .language-option {
    color: #e0e0e0;
    padding: 0.75rem 1rem;
  }
  
  .currency-option:hover, 
  .language-option:hover {
    background: rgba(120, 70, 200, 0.2);
  }
  
  .currency-option.active {
    background: rgba(120, 70, 200, 0.3);
    font-weight: 600;
  }
  
  .language-option.active {
    background: rgba(120, 70, 200, 0.3);
    font-weight: 600;
  }
  
  .currency-icon {
    color: #ac7cdd;
  }
  
  .currency-symbol {
    color: #ac7cdd;
  }
}

/* Login button styles */
.auth-buttons {
  margin-right: 1rem;
}

.login-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: linear-gradient(135deg, #7846c8 0%, #6033a0 100%);
  border: 1px solid rgba(120, 70, 200, 0.4);
  color: #fff;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.login-button:hover {
  background: linear-gradient(135deg, #8956d8 0%, #7141b0 100%);
  border-color: rgba(120, 70, 200, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(120, 70, 200, 0.3);
}

.login-button i {
  font-size: 1rem;
}

/* User avatar and dropdown menu styles */
.user-profile {
  position: relative;
  margin-right: 1rem;
  cursor: pointer;
  z-index: 101;
}

.avatar-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.3rem 0.75rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.avatar-container:hover {
  background: linear-gradient(135deg, #4a4f75 0%, #33353f 100%);
  border-color: rgba(69, 104, 220, 0.5);
  box-shadow: 0 2px 8px rgba(69, 104, 220, 0.3);
}

.user-avatar {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(69, 104, 220, 0.5);
}

.user-name {
  color: white;
  font-size: 0.9rem;
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  min-width: 180px;
  z-index: 102;
  animation: fadeIn 0.2s ease;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.8rem 1rem;
  color: white;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  font-size: 0.95rem;
}

.dropdown-item i {
  width: 20px;
  text-align: center;
  color: #7b88ff;
}

.dropdown-item:hover {
  background: rgba(69, 104, 220, 0.2);
}

.dropdown-divider {
  height: 1px;
  background: rgba(69, 104, 220, 0.2);
  margin: 0.3rem 0;
}

.dropdown-item.logout {
  color: #ff6b6b;
}

.dropdown-item.logout i {
  color: #ff6b6b;
}

.dropdown-item.logout:hover {
  background: rgba(255, 107, 107, 0.1);
}

@media (max-width: 768px) {
  /* ... existing mobile styles ... */
  
  .login-button {
    width: 100%;
    justify-content: center;
    padding: 0.8rem;
    margin-top: 1rem;
  }
  
  .user-profile {
    width: 100%;
    margin: 1rem 0 0;
  }
  
  .avatar-container {
    width: 100%;
    justify-content: center;
    padding: 0.8rem;
    border-radius: 20px;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
  }
  
  .avatar-container:hover {
    background: linear-gradient(135deg, #4a4f75 0%, #33353f 100%);
    border-color: rgba(69, 104, 220, 0.5);
    box-shadow: 0 2px 8px rgba(69, 104, 220, 0.3);
  }
  
  .user-dropdown {
    position: relative;
    width: 100%;
    margin-top: 0.5rem;
    right: 0;
    left: 0;
    background: rgba(28, 30, 44, 0.98);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(69, 104, 220, 0.2);
  }
  
  .dropdown-item {
    color: #e0e0e0;
  }
  
  .dropdown-item i {
    color: #7b88ff;
  }
  
  .dropdown-item:hover {
    background: rgba(69, 104, 220, 0.2);
  }
  
  .dropdown-divider {
    background: rgba(69, 104, 220, 0.2);
  }
}

/* Completely override Element Plus dialog styles to fix white border issue */
/* Global override for dialog background and border */
.el-overlay {
  background-color: rgba(0, 0, 0, 0.75) !important;
  backdrop-filter: blur(4px) !important;
}

.el-overlay-dialog {
  overflow: hidden !important;
  padding: 0 !important;
  margin: 0 !important;
}

.dark-modal {
  background-color: rgba(0, 0, 0, 0.75) !important;
}

/* Custom mobile dialog styles - Fully custom implementation replacing Element Plus Dialog */
.custom-mobile-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.75);
  backdrop-filter: blur(4px);
  z-index: 2000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.2s ease;
}

.custom-mobile-dialog {
  width: 90%;
  max-width: 340px;
  background-color: #0a0a12;
  background-image: linear-gradient(125deg, #0a0a12 0%, #141428 50%, #0a0a12 100%);
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid rgba(120, 70, 200, 0.3);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.5), 0 0 30px rgba(120, 70, 200, 0.3);
  animation: scaleIn 0.2s ease;
}

.custom-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid rgba(120, 70, 200, 0.2);
  background: linear-gradient(135deg, #242428, #1a1a20);
}

.custom-dialog-title {
  color: #c3a3ff;
  font-weight: 500;
  font-size: 1.1rem;
}

.custom-dialog-close {
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s;
}

.custom-dialog-close:hover {
  color: #c3a3ff;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.9); }
  to { transform: scale(1); }
}

/* Mobile user info styles - updated to match the new theme */
.mobile-user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: rgba(30, 30, 40, 0.7);
  border-bottom: 1px solid rgba(120, 70, 200, 0.2);
}

.mobile-user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 3px solid #c3a3ff;
  object-fit: cover;
  margin-bottom: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3), 0 0 15px rgba(120, 70, 200, 0.4);
}

.mobile-user-info h3 {
  color: #e0e0e0;
  margin: 0;
  font-size: 1.2rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.mobile-menu-options {
  padding: 10px 0;
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: #e0e0e0;
  text-decoration: none;
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  font-size: 1rem;
  transition: background 0.3s;
}

.mobile-menu-item i {
  width: 30px;
  font-size: 1.2rem;
  color: #c3a3ff;
  margin-right: 15px;
}

.mobile-menu-item:hover,
.mobile-menu-item:active {
  background: rgba(120, 70, 200, 0.2);
}

.mobile-menu-item.logout {
  color: #ff6b6b;
}

.mobile-menu-item.logout i {
  color: #ff6b6b;
}

.mobile-menu-item.logout:hover {
  background: rgba(255, 107, 107, 0.1);
}

/* Mobile login prompt styles */
.mobile-login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
  text-align: center;
  background: rgba(30, 30, 40, 0.7);
}

.mobile-login-prompt .login-icon {
  margin-bottom: 20px;
}

.mobile-login-prompt .login-icon i {
  font-size: 4rem;
  color: #c3a3ff;
  opacity: 0.8;
}

.mobile-login-prompt .login-title {
  color: #e0e0e0;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 10px 0;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.mobile-login-prompt .login-subtitle {
  color: #b0b0b0;
  font-size: 0.9rem;
  line-height: 1.4;
  margin: 0 0 30px 0;
  max-width: 280px;
}

.mobile-login-prompt .login-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  max-width: 200px;
}

.mobile-login-button,
.mobile-register-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  font-size: 1rem;
  transition: all 0.3s ease;
  gap: 8px;
}

.mobile-login-button {
  background: linear-gradient(135deg, #7846c8 0%, #9b59b6 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.3);
}

.mobile-login-button:hover {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(120, 70, 200, 0.4);
}

.mobile-register-button {
  background: rgba(255, 255, 255, 0.1);
  color: #e0e0e0;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.mobile-register-button:hover {
  background: rgba(120, 70, 200, 0.2);
  border-color: rgba(120, 70, 200, 0.5);
  color: #ffffff;
}

/* Light theme styles for mobile login prompt */
[data-theme="light"] .mobile-login-prompt {
  background: rgba(255, 255, 255, 0.9);
}

[data-theme="light"] .mobile-login-prompt .login-icon i {
  color: #7846c8;
}

[data-theme="light"] .mobile-login-prompt .login-title {
  color: #333333;
  text-shadow: none;
}

[data-theme="light"] .mobile-login-prompt .login-subtitle {
  color: #666666;
}

[data-theme="light"] .mobile-register-button {
  background: rgba(120, 70, 200, 0.1);
  color: #333333;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

[data-theme="light"] .mobile-register-button:hover {
  background: rgba(120, 70, 200, 0.2);
  border-color: rgba(120, 70, 200, 0.5);
  color: #333333;
}

/* Mobile currency selector styles */
.mobile-currency-selector {
  display: flex;
  align-items: center;
  padding: 0.4rem 0.6rem;
  background: rgba(30, 30, 40, 0.6);
  border-radius: 4px;
  margin-right: 0.8rem;
  cursor: pointer;
  position: relative;
  transition: all 0.2s ease;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.mobile-currency-selector:hover {
  background: rgba(120, 70, 200, 0.2);
  box-shadow: 0 2px 5px rgba(120, 70, 200, 0.3);
}

.mobile-currency-selector .currency-icon {
  font-weight: bold;
  color: #c3a3ff;
  font-size: 1rem;
  margin-right: 4px;
}

.mobile-currency-selector .currency-icon-img {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  object-fit: contain;
  filter: brightness(0) invert(1); /* 深色主题：白色图标 */
}

[data-theme="light"] .mobile-currency-selector .currency-icon-img {
  filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
}

.currency-selector .currency-icon-img {
  width: 14px;
  height: 14px;
  margin-right: 4px;
  object-fit: contain;
  filter: brightness(0) invert(1); /* 深色主题：白色图标 */
}

[data-theme="light"] .currency-selector .currency-icon-img {
  filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
}

.mobile-currency-selector .currency-code {
  font-size: 0.85rem;
  color: #e0e0e0;
}

.mobile-currency-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: rgba(20, 20, 30, 0.95);
  border-radius: 4px;
  width: 120px;
  z-index: 200;
  margin-top: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 0 15px rgba(120, 70, 200, 0.3);
  animation: fadeIn 0.2s ease;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.mobile-currency-dropdown .currency-option {
  padding: 0.6rem 0.8rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.2s ease;
  color: #e0e0e0;
}

.mobile-currency-dropdown .currency-symbol {
  color: #c3a3ff;
}

.mobile-currency-dropdown .currency-code {
  font-size: 0.85rem;
  color: #e0e0e0;
}

.mobile-currency-dropdown .currency-option:hover {
  background: rgba(120, 70, 200, 0.2);
}

.mobile-currency-dropdown .currency-option.active {
  background: rgba(120, 70, 200, 0.3);
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(25, 25, 30, 0.8);
  border-radius: 5px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4d347f, #332255);
  border-radius: 5px;
  border: 2px solid rgba(25, 25, 30, 0.8);
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5d4495, #442277);
  box-shadow: 0 0 10px rgba(51, 34, 85, 0.7);
}

/* Firefox scrollbar styles */
* {
  scrollbar-width: thin;
  scrollbar-color: #332255 rgba(25, 25, 30, 0.8);
}

@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
}
/* Mobile Bottom Navigation Styles - Enhanced Cosmic Theme */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 65px; /* Slightly taller for better touch targets */
  background: linear-gradient(180deg, rgba(20, 15, 45, 0.85) 0%, rgba(30, 25, 60, 0.95) 100%);
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.6), 0 0 25px rgba(123, 136, 255, 0.3);
  z-index: 9999; /* 设置最高层级 */
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border-top: 1px solid rgba(123, 136, 255, 0.3);
  border-radius: 20px 20px 0 0; /* Rounded top corners */
  padding-bottom: calc(5px + env(safe-area-inset-bottom)); /* For iPhone notch support with extra padding */
  transform: translateZ(0); /* Hardware acceleration */
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
}

/* Add cosmic background with stars effect */
.mobile-bottom-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(circle at 20% 40%, rgba(123, 136, 255, 0.15) 0%, transparent 50%),
                    radial-gradient(circle at 80% 30%, rgba(160, 128, 255, 0.15) 0%, transparent 50%);
  opacity: 0.8;
  z-index: -1;
  }
  
.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  width: 16.66%; /* 修改宽度以适应6个导航项 */
  height: 100%;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: visible;
  padding: 8px 0;
  transform-style: preserve-3d;
  }
  
.nav-icon {
  font-size: 1.3rem; /* 稍微减小图标大小 */
  margin-bottom: 2px; /* 减小底部边距 */
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
  z-index: 2;
}

.nav-label {
  font-size: 0.7rem; /* 减小字体大小 */
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  letter-spacing: 0.2px;
  opacity: 0.9;
  margin-top: 1px; /* 减小顶部边距 */
}

.bottom-nav-item::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  width: 70%;
  height: 3px;
  background: linear-gradient(90deg, rgba(123, 136, 255, 0), rgba(123, 136, 255, 1), rgba(123, 136, 255, 0));
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 3px 3px 0 0;
  box-shadow: 0 0 15px rgba(123, 136, 255, 0.7);
  transform: translateY(3px);
}

.bottom-nav-item.active {
  color: #fff;
  transform: translateY(-4px);
}

.bottom-nav-item:active {
  transform: scale(0.95);
}

.bottom-nav-item.active::before {
  opacity: 1;
  transform: translateY(0);
}

.bottom-nav-item.active .nav-icon {
  transform: translateY(0);
  color: #a080ff;
  text-shadow: 0 0 15px rgba(123, 136, 255, 0.8);
  filter: drop-shadow(0 0 8px rgba(123, 136, 255, 0.6));
}

.bottom-nav-item.active .nav-label {
  color: #fff;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(123, 136, 255, 0.6);
}

.bottom-nav-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(123, 136, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 0 15px rgba(123, 136, 255, 0.5);
  transform: translateZ(0);
}

.bottom-nav-item.active .bottom-nav-avatar {
  transform: scale(1.1);
  border-color: rgba(123, 136, 255, 1);
  box-shadow: 0 0 18px rgba(123, 136, 255, 0.7);
}

/* Custom cosmic animation for active state */
@keyframes cosmicPulse {
  0% { opacity: 0.4; transform: scale(0.8); }
  50% { opacity: 0.8; transform: scale(1.1); }
  100% { opacity: 0.4; transform: scale(0.8); }
}

@keyframes starTwinkle {
  0% { opacity: 0.3; }
  50% { opacity: 1; }
  100% { opacity: 0.3; }
}

/* Star particles around active icons */
.bottom-nav-item.active .nav-icon::before {
  content: '';
  position: absolute;
  width: 4px;
  height: 4px;
  background-color: rgba(123, 136, 255, 0.8);
  border-radius: 50%;
  left: -10px;
  top: 0;
  animation: starTwinkle 2s infinite ease-in-out;
  box-shadow: 0 0 5px rgba(123, 136, 255, 0.8);
}

.bottom-nav-item.active .nav-icon::after {
  animation: cosmicPulse 3s infinite ease-in-out;
}

/* Add additional star particles with different positions and animations */
.bottom-nav-item.active::after {
  content: '';
  position: absolute;
  width: 3px;
  height: 3px;
  background-color: #a080ff;
  border-radius: 50%;
  top: 25%;
  right: 25%;
  box-shadow: 0 0 8px #a080ff;
  animation: starTwinkle 2.5s infinite ease-in-out;
  animation-delay: 0.5s;
}
  /* 移动端侧边栏改进 */
  @media (max-width: 768px) {
    .nav-links {
      overflow-y: auto; /* 添加滚动条确保所有菜单项都可见 */
      padding-bottom: 80px; /* 为底部导航留出空间 */
    }

    .nav-links a.nav-link,
    .nav-links button.nav-link {
      display: flex !important;
    width: 100%;
      padding: 0.8rem 1rem;
      margin: 0.2rem 0;
      border-radius: 6px;
      font-size: 1.1rem;
      transition: all 0.2s ease;
    }

    .nav-links .currency-selector {
      display: block !important;
      margin-top: 1rem;
    }

    /* 修复移动端侧边栏中的按钮样式 */
    .mobile-menu-overlay button:not(.mobile-menu-toggle):not(.close-btn),
    .mobile-menu-overlay a.nav-link {
      display: block !important;
      opacity: 1 !important;
      visibility: visible !important;
    }
  }

/* 修复移动端侧边栏打开时顶部导航栏被推到左边的问题 */
.mobile-device .main-nav {
  position: fixed !important;
  width: 100% !important;
  left: 0 !important;
  right: 0 !important;
  top: 0 !important;
  transform: none !important;
  z-index: 95 !important;
  box-sizing: border-box !important;
}

/* 确保导航容器和内部元素保持位置 */
.mobile-device .nav-container {
  position: relative !important;
  transform: none !important;
  left: 0 !important;
  width: 100% !important;
  }
  
/* 确保导航容器内的元素不受侧边栏影响 */
.mobile-device .nav-logo,
.mobile-device .mobile-currency-selector,
.mobile-device .mobile-menu-toggle {
  position: relative !important;
  transform: none !important;
  left: auto !important;
  right: auto !important;
  }
  
/* 使用新的方式实现侧边栏，确保不影响导航栏 */
.mobile-device .nav-links.active {
  position: fixed !important;
  z-index: 100;
  top: 0 !important;
  right: 0 !important;
  transform: none !important;
  margin-left: 0 !important;
  width: 70% !important;
  max-width: 300px !important;
  }
  
/* 移动端品牌与产品分类标题样式 */
@media (max-width: 768px) {
  .filters-section .section-title,
  .brands-grid-container .section-title,
  .category-header .section-title,
  .product-section-title {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    text-align: center;
    font-size: 1.5rem !important;
    padding: 0.4rem 1.5rem !important;
    margin-bottom: 1rem !important;
    z-index: 5 !important;
    position: relative !important;
  }
  
  /* 品牌和分类标题容器样式 */
  .brands-header,
  .category-header {
    display: flex !important;
    justify-content: center !important;
    margin-bottom: 1.5rem !important;
    width: 100% !important;
    z-index: 5 !important;
    position: relative !important;
  }
}

@media (max-width: 768px) {
  /* ... existing mobile styles ... */
  
  /* 移动端在线用户显示样式 */
  .mobile-online-users {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.4rem 0.6rem;
    background: rgba(123, 104, 238, 0.25);
    border-radius: 4px;
    margin-left: 2px;
    color: #fff;
    border: 1px solid rgba(120, 70, 200, 0.3);
  }
  
  .mobile-online-users .users-icon {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    object-fit: contain;
    filter: brightness(0) invert(1); /* 深色主题：白色图标 */
  }

  [data-theme="light"] .mobile-online-users .users-icon {
    filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
  }
  
  .online-count {
    font-size: 0.85rem;
    font-weight: 500;
  }
  
  /* 调整选择器容器样式 */
  .mobile-selectors-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-right: 0;
    position: relative;
    z-index: 90;
  }
}

#app {
  font-family: 'Roboto', 'Arial', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: var(--text-color);
  background: var(--primary-bg);
  min-height: 100vh;
  position: relative;
  transition: background 0.5s ease, color 0.5s ease;
}

/* 为移动设备上的主要内容添加底部间距，避免被底部导航栏挡住 */
@media (max-width: 768px) {
  #app .router-view-container {
    padding-bottom: 80px; /* 给底部导航栏留出空间 */
    position: relative;
    z-index: 1;
    min-height: calc(100vh - 60px); /* 考虑顶部导航栏高度 */
    overflow-x: hidden;
  }
  
  /* 确保页脚组件不会溢出容器 */
  #app .router-view-container .footer-disclaimer-glow,
  #app .router-view-container .footer-copyright-bar {
    position: relative;
    z-index: 5;
    overflow: hidden;
  }
}

/* 全站颜色方案调整 - 支持主题切换 */

/* 覆盖BESTSELLER、ELEVATE YOUR REP GAME HERE和About AGTFIND Platform板块的背景色 */
.bestseller,
.products,
.about {
  background: var(--primary-bg) !important;
}

/* 主背景颜色 */
.home {
  background: var(--primary-bg) !important;
}

/* 各种区块背景颜色调整 */
.intro-section,
.daily-deal-section,
.featured-section,
.bestseller,
.products,
.recommended-products,
.info-section,
.faq {
  background: transparent !important;
}

/* 卡片和区块背景 */
.daily-product-card,
.product-card,
.carousel-item,
.info-block,
.faq-item {
  background: var(--card-bg) !important;
  border: var(--card-border) !important;
  box-shadow: var(--card-shadow) !important;
}

/* 输入框和搜索框 */
input, 
select,
textarea {
  background: var(--search-bg) !important;
  color: var(--text-color) !important;
  border: 1px solid var(--border-color) !important;
}

/* 按钮颜色 */
.view-all-btn,
.info-btn,
.action-btn {
  background: var(--button-bg) !important;
  color: var(--primary-color) !important;
  border: 1px solid var(--border-color) !important;
}

.view-all-btn:hover,
.info-btn:hover,
.action-btn:hover {
  background: var(--nav-hover-bg) !important;
  color: var(--nav-active) !important;
}

/* 标题颜色 */
h1, h2, h3, h4, h5, h6 {
  color: var(--heading-color) !important;
  text-shadow: none !important;
}

/* 文本颜色 */
p, span, div {
  color: var(--text-color) !important;
}

/* 强调色 */
.price,
.discount-badge,
.highlight {
  color: var(--primary-color) !important;
  text-shadow: none !important;
}

/* 导航栏颜色 */
.navbar {
  background: var(--nav-bg) !important;
  backdrop-filter: blur(10px) !important;
  border-bottom: 1px solid var(--border-color) !important;
}

/* 修复特定组件的颜色 */
.daily-deal-container {
  background: var(--card-bg) !important;
}

.countdown-overlay {
  background: var(--search-bg) !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .home {
    background: var(--primary-bg) !important;
  }
}

/* 移动底部导航样式 */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 65px;
  background: var(--nav-bg);
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  backdrop-filter: blur(15px);
  border-top: 1px solid var(--border-color);
  border-radius: 20px 20px 0 0;
  padding-bottom: calc(5px + env(safe-area-inset-bottom));
  transform: translateZ(0);
  transition: all 0.5s ease;
  overflow: hidden;
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--nav-text);
  text-decoration: none;
  width: 16.66%;
  height: 100%;
  transition: all 0.4s ease;
  position: relative;
  overflow: visible;
  padding: 8px 0;
  transform-style: preserve-3d;
}

.nav-icon {
  font-size: 1.3rem;
  margin-bottom: 2px;
  transition: all 0.4s ease;
  position: relative;
  z-index: 2;
  color: var(--nav-text);
}

.nav-label {
  font-size: 0.7rem;
  font-weight: 500;
  transition: all 0.4s ease;
  letter-spacing: 0.2px;
  opacity: 0.9;
  margin-top: 1px;
  color: var(--nav-text);
}

.bottom-nav-item.active .nav-icon {
  transform: translateY(0);
  color: var(--primary-color);
  text-shadow: 0 0 15px rgba(123, 136, 255, 0.8);
  filter: drop-shadow(0 0 8px rgba(123, 136, 255, 0.6));
}

.bottom-nav-item.active .nav-label {
  color: var(--primary-color);
  font-weight: 600;
}

/* 移动端菜单覆盖层 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 90;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease;
}

/* 移动端用户对话框 */
.custom-mobile-dialog {
  width: 90%;
  max-width: 340px;
  background: var(--card-bg);
  border-radius: 15px;
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow);
  animation: scaleIn 0.2s ease;
}

.custom-dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--nav-bg);
}

.custom-dialog-title {
  color: var(--nav-text);
  font-weight: 500;
  font-size: 1.1rem;
}

.mobile-user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: var(--card-bg);
  border-bottom: 1px solid var(--border-color);
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: var(--text-color);
  text-decoration: none;
  border: none;
  background: transparent;
  width: 100%;
  text-align: left;
  font-size: 1rem;
  transition: background 0.3s;
}

.mobile-menu-item i {
  width: 30px;
  font-size: 1.2rem;
  color: var(--primary-color);
  margin-right: 15px;
}

/* Mobile Bottom Navigation Styles */
.mobile-bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 65px;
  background: var(--nav-bg) !important;
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  backdrop-filter: blur(15px);
  border-top: 1px solid var(--border-color);
  border-radius: 20px 20px 0 0;
  padding-bottom: calc(5px + env(safe-area-inset-bottom));
  transform: translateZ(0);
  transition: all 0.5s ease;
  overflow: hidden;
}

/* 全局主题应用 */
[data-theme='dark'] {
  --primary-bg: linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%);
  --nav-bg: rgba(77, 48, 105, 1);
  --text-color: #ffffff;
  --heading-color: #ffffff;
  --card-bg: rgba(30, 9, 64, 0.8);
  --search-bg: rgba(30, 9, 64, 0.8);
  --button-bg: rgba(50, 20, 80, 0.7);
  --border-color: rgba(147, 51, 234, 0.2);
  --shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  --primary-color: #a855f7;
  --secondary-color: #c084fc;
  --dark-bg: #0a0a12;
  --light-bg: #1a1a20;
  --text-dark: #e0e0e0;
  --card-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  --card-border: 1px solid rgba(147, 51, 234, 0.2);
  --nav-text: #ffffff;
  --nav-active: #ffffff;
  --nav-hover-bg: rgba(147, 51, 234, 0.15);
  --nav-active-border: #c084fc;
  --dropdown-bg: rgba(20, 20, 30, 0.95);
  --mobile-menu-bg: linear-gradient(125deg, #0a0a12 0%, #141428 50%, #0a0a12 100%);
  --mobile-menu-border: rgba(147, 51, 234, 0.2);
  background: linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%) !important;
  color: #ffffff !important;
}

[data-theme='light'] {
  --primary-bg: linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%);
  --nav-bg: rgba(193, 151, 209, 1);
  --text-color: #333333;
  --heading-color: #230F48;
  --card-bg: #ffffff;
  --search-bg: rgba(255, 255, 255, 0.5);
  --button-bg: rgba(255, 255, 255, 0.7);
  --border-color: rgba(76, 175, 80, 0.15);
  --shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  --primary-color: #9333ea;
  --secondary-color: #c084fc;
  --dark-bg: #ffffff;
  --light-bg: #f9f9f9;
  --text-dark: #2e7d32;
  --card-shadow: 0 2px 8px rgba(76, 175, 80, 0.15);
  --card-border: 1px solid rgba(76, 175, 80, 0.1);
  --transition-speed: 0.3s;
  --nav-text: #333333;
  --nav-active: #230F48;
  --nav-hover-bg: rgba(147, 51, 234, 0.15);
  --nav-active-border: #9333ea;
  --dropdown-bg: rgba(255, 255, 255, 0.95);
  --mobile-menu-bg: linear-gradient(125deg, #f9f5fc 0%, #ffffff 50%, #f9f5fc 100%);
  --mobile-menu-border: rgba(147, 51, 234, 0.2);
  background: linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%) !important;
  color: #333333 !important;
}

/* 确保主题应用到各种页面组件 */
.router-view-container > div, 
.router-view-container > section,
.router-view-container > main {
  background: var(--primary-bg) !important;
  color: var(--text-color) !important;
}

/* 确保卡片等UI组件遵循主题 */
.el-card,
.card,
.product-card,
.seller-card,
.info-card {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
  border: var(--card-border) !important;
  box-shadow: var(--card-shadow) !important;
}

/* 文本颜色 */
h1, h2, h3, h4, h5, h6 {
  color: var(--heading-color) !important;
}

p, span, div {
  color: var(--text-color) !important;
}

/* 确保主题在所有页面应用 - 使用[data-theme]选择器 */
[data-theme="dark"] {
  --primary-bg: linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%);
  --nav-bg: rgba(77, 48, 105, 1);
  --text-color: #ffffff;
  --heading-color: #ffffff;
}

[data-theme="light"] {
  --primary-bg: linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%);
  --nav-bg: rgba(193, 151, 209, 1);
  --text-color: #333333;
  --heading-color: #230F48;
}

/* 修改所有产品页面、商家页面等的背景色 */
[data-theme="dark"] .router-view-container > div,
[data-theme="dark"] .router-view-container > section,
[data-theme="dark"] .product-page,
[data-theme="dark"] .seller-page,
[data-theme="dark"] .trending-page {
  background: var(--primary-bg) !important;
  color: var(--text-color) !important;
}

[data-theme="light"] .router-view-container > div,
[data-theme="light"] .router-view-container > section,
[data-theme="light"] .product-page,
[data-theme="light"] .seller-page,
[data-theme="light"] .trending-page {
  background: var(--primary-bg) !important;
  color: var(--text-color) !important;
}

/* 产品页面特定主题样式覆盖 - 使用更强力的选择器和!important */
[data-theme="dark"] .products,
[data-theme="dark"] .product-page,
[data-theme="dark"] .products-view,
[data-theme="dark"] .product-detail-view {
  background: var(--primary-bg) !important;
  color: var(--text-color) !important;
}

[data-theme="light"] .products,
[data-theme="light"] .product-page,
[data-theme="light"] .products-view,
[data-theme="light"] .product-detail-view {
  background: var(--primary-bg) !important;
  color: var(--text-color) !important;
}

/* 产品卡片覆盖样式 */
[data-theme="dark"] .product-card {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
  box-shadow: var(--shadow) !important;
}

[data-theme="light"] .product-card {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
  box-shadow: var(--shadow) !important;
}

/* 产品信息区域 */
[data-theme="dark"] .product-info {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
}

[data-theme="light"] .product-info {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
}

/* 产品名称 */
[data-theme="dark"] .product-name {
  color: var(--text-color) !important;
}

[data-theme="light"] .product-name {
  color: var(--text-color) !important;
}

/* 筛选区域 */
[data-theme="dark"] .filters-section,
[data-theme="dark"] .filter-container {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
}

[data-theme="light"] .filters-section,
[data-theme="light"] .filter-container {
  background: var(--card-bg) !important;
  color: var(--text-color) !important;
}

/* 产品图片区域 */
[data-theme="dark"] .product-image {
  background: var(--card-bg) !important;
}

[data-theme="light"] .product-image {
  background: var(--card-bg) !important;
}

/* 确保产品卡片悬停效果与主题一致 */
[data-theme="dark"] .product-card:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4), 0 0 15px rgba(147, 51, 234, 0.5) !important;
}

[data-theme="light"] .product-card:hover {
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1), 0 0 15px rgba(147, 51, 234, 0.3) !important;
}

/* 产品图片容器 */
[data-theme="dark"] .product-image,
[data-theme="light"] .product-image {
  position: relative;
  overflow: hidden;
  background: transparent !important;
}

/* 覆盖产品卡片的特殊悬停动画 */
[data-theme="dark"] .product-card::after,
[data-theme="light"] .product-card::after {
  display: none !important;
}

[data-theme="dark"] .product-card:hover::after,
[data-theme="light"] .product-card:hover::after {
  animation: none !important;
  opacity: 0 !important;
}

/* 登录按钮主题响应样式 */
[data-theme="light"] .login-button {
  background: linear-gradient(135deg, #c084fc 0%, #9333ea 100%) !important;
  border: 1px solid rgba(193, 151, 209, 0.4) !important;
  color: #fff !important;
}

[data-theme="light"] .login-button:hover {
  background: linear-gradient(135deg, #d8b4fe 0%, #a855f7 100%) !important;
  border-color: rgba(193, 151, 209, 0.6) !important;
}

/* 深色主题下保持原样 */
[data-theme="dark"] .login-button {
  background: linear-gradient(135deg, #7846c8 0%, #6033a0 100%) !important;
  border: 1px solid rgba(120, 70, 200, 0.4) !important;
}

[data-theme="dark"] .login-button:hover {
  background: linear-gradient(135deg, #8956d8 0%, #7141b0 100%) !important;
  border-color: rgba(120, 70, 200, 0.6) !important;
}

/* 移动端顶部工具栏样式 */
.mobile-top-toolbar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 8px;
  position: relative;
  z-index: 90;
}

/* 移动端导航样式优化 */
@media (max-width: 768px) {
  .nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    padding: 0.5rem 1rem;
  }
  
  .nav-logo {
    flex: 0 0 auto;
    height: 28px; /* 略小一点的logo */
    margin-right: auto; /* 确保靠左对齐 */
  }
  
  /* 在线用户计数器样式 */
  .mobile-online-users {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.35rem 0.5rem;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
  }
  
  [data-theme="light"] .mobile-online-users {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(147, 51, 234, 0.1);
  }
  
  .mobile-online-users .users-icon {
    width: 14px;
    height: 14px;
    margin-right: 5px;
    object-fit: contain;
    filter: brightness(0) invert(1); /* 深色主题：白色图标 */
  }

  [data-theme="light"] .mobile-online-users .users-icon {
    filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
  }
  
  .online-count {
    font-size: 0.85rem;
    font-weight: 500;
  }
  
  /* 语言选择器样式 */
  .mobile-language-selector {
    display: flex;
    align-items: center;
    padding: 0.35rem 0.5rem;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }
  
  [data-theme="light"] .mobile-language-selector {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(147, 51, 234, 0.1);
  }
  
  .mobile-language-selector .language-code {
    font-size: 0.85rem;
    margin-right: 3px;
  }
  
  .mobile-language-selector .earth-icon {
    width: 14px;
    height: 14px;
    object-fit: contain;
    filter: brightness(0) invert(1); /* 深色主题：白色图标 */
  }

  [data-theme="light"] .mobile-language-selector .earth-icon {
    filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
  }
  
  /* 货币选择器样式 */
  .mobile-currency-selector {
    display: flex;
    align-items: center;
    padding: 0.35rem 0.5rem;
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
  }
  
  [data-theme="light"] .mobile-currency-selector {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(147, 51, 234, 0.1);
  }
  
  .mobile-currency-selector .currency-icon {
    font-size: 0.85rem;
    margin-right: 3px;
  }

  .mobile-currency-selector .currency-icon-img {
    width: 14px;
    height: 14px;
    margin-right: 3px;
    object-fit: contain;
    filter: brightness(0) invert(1); /* 深色主题：白色图标 */
  }

  [data-theme="light"] .mobile-currency-selector .currency-icon-img {
    filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
  }
  
  .mobile-currency-selector .currency-code {
    font-size: 0.85rem;
  }
  
  /* 菜单按钮样式 */
  .mobile-menu-toggle {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 34px;
    height: 34px;
    padding: 4px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  [data-theme="light"] .mobile-menu-toggle {
    background: rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(147, 51, 234, 0.1);
  }

  .mobile-menu-toggle .menu-icon {
    width: 24px;
    height: 24px;
    object-fit: contain;
    transition: all 0.3s ease;
    filter: brightness(0) invert(1); /* 将图片变为白色 */
  }

  [data-theme="light"] .mobile-menu-toggle .menu-icon {
    filter: brightness(0) invert(0.2); /* 浅色主题下变为深色 */
  }

  /* 激活状态的菜单 */
  .mobile-menu-active .mobile-menu-toggle .menu-icon {
    transform: rotate(90deg) scale(1.1);
  }
  
  /* 改进下拉菜单样式 */
  .mobile-language-dropdown,
  .mobile-currency-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 5px;
    min-width: 120px;
    background: rgba(30, 30, 40, 0.95);
    border-radius: 4px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    z-index: 100;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  [data-theme="light"] .mobile-language-dropdown,
  [data-theme="light"] .mobile-currency-dropdown {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(147, 51, 234, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  }
  
  .language-option,
  .currency-option {
    padding: 0.6rem 0.75rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
  }
  
  [data-theme="dark"] .language-option,
  [data-theme="dark"] .currency-option {
    color: #e0e0e0;
  }
  
  [data-theme="light"] .language-option,
  [data-theme="light"] .currency-option {
    color: #333333;
  }
  
  .language-option:hover,
  .currency-option:hover {
    background: rgba(147, 51, 234, 0.15);
  }
  
  .language-option.active,
  .currency-option.active {
    background: rgba(147, 51, 234, 0.3);
    font-weight: 500;
  }
}

/* 调整移动端导航栏高度和间距 */
@media (max-width: 768px) {
  .main-nav {
    padding: 0.3rem 0.5rem !important; /* 减小上下内边距 */
    height: 64px !important; /* 固定高度为64px */
    min-height: 64px !important;
    max-height: 64px !important;
    display: flex !important;
    align-items: center !important;
  }
  
  .nav-container {
    padding: 0 0.5rem !important; /* 减小左右内边距 */
    max-width: 100% !important;
    width: 100% !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
  }
  
  /* 调整导航栏布局 */
  .nav-logo {
    margin-right: auto !important;
    flex: 0 0 auto !important;
  }
  
  /* 调整右侧工具栏 */
  .mobile-top-toolbar {
    display: flex !important;
    align-items: center !important;
    gap: 6px !important;
    margin-left: auto !important;
    margin-right: 5px !important; /* 给右侧添加一点间距，避免太靠右 */
  }
  
  /* 调整菜单按钮 */
  .mobile-menu-toggle {
    margin-right: 5px !important; /* 确保菜单按钮不会太靠右 */
  }
  
  /* 调整在线用户、语言和货币选择器样式 */
  .mobile-online-users,
  .mobile-language-selector,
  .mobile-currency-selector {
    padding: 0.3rem 0.5rem !important;
    font-size: 0.85rem !important;
  }
  
  /* 标题居中 */
  .nav-logo {
    position: absolute !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
  }
  
  /* 简化语言和货币选择器，移除下拉指示器 */
  .mobile-language-selector i.fa-chevron-down,
  .mobile-currency-selector i.fa-chevron-down {
    display: none !important;
  }
}

/* 极小屏幕适配 */
@media (max-width: 360px) {
  .mobile-top-toolbar {
    gap: 4px !important;
  }
  
  .mobile-online-users,
  .mobile-language-selector,
  .mobile-currency-selector {
    padding: 0.2rem 0.4rem !important;
  }
}

@media (max-width: 768px) {
  /* 减小白色框框的宽度 */
  .mobile-online-users,
  .mobile-language-selector,
  .mobile-currency-selector {
    padding: 0.2rem 0.3rem !important; /* 减小内边距 */
    min-width: 30px !important; /* 设置最小宽度 */
    width: auto !important; /* 自动宽度，但受到min-width限制 */
    max-width: 40px !important; /* 设置最大宽度 */
    height: 28px !important; /* 控制高度 */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }
  
  /* 在线用户计数器特殊处理 */
  .mobile-online-users {
    width: 36px !important; /* 固定宽度 */
  }
  
  /* 语言选择器特殊处理 */
  .mobile-language-selector {
    width: 34px !important; /* 固定宽度 */
  }
  
  /* 货币选择器特殊处理 */
  .mobile-currency-selector {
    width: 40px !important; /* 固定宽度 */
  }
  
  /* 减小字体大小 */
  .language-code,
  .currency-code,
  .online-count {
    font-size: 0.75rem !important;
  }
  
  /* 减少图标和文本之间的间距 */
  .mobile-online-users .users-icon {
    width: 12px !important;
    height: 12px !important;
    margin-right: 1px !important;
  }

  .mobile-language-selector .earth-icon {
    width: 12px !important;
    height: 12px !important;
    margin-right: 1px !important;
  }

  .mobile-currency-selector .currency-icon-img {
    width: 12px !important;
    height: 12px !important;
    margin-right: 1px !important;
  }
}

@media (max-width: 768px) {
  /* 移除底部导航栏相关样式 */
  .mobile-bottom-nav {
    display: none !important;
  }
  
  /* 移除为底部导航栏预留的padding */
  body {
    padding-bottom: 0 !important; /* 移除为底部导航栏预留的padding */
  }
  
  #app .router-view-container {
    padding-bottom: 20px !important; /* 减小底部padding */
  }
}

/* 美化移动端菜单按钮 */
@media (max-width: 768px) {
  .mobile-menu-toggle {
    width: 34px !important;
    height: 34px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: rgba(255, 255, 255, 0.15) !important;
    border-radius: 6px !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    transition: all 0.3s ease !important;
    padding: 4px !important;
    cursor: pointer !important;
  }

  [data-theme="light"] .mobile-menu-toggle {
    background: rgba(255, 255, 255, 0.3) !important;
    border: 1px solid rgba(147, 51, 234, 0.1) !important;
  }

  .mobile-menu-toggle .menu-icon {
    width: 24px !important;
    height: 24px !important;
    object-fit: contain !important;
    transition: all 0.3s ease !important;
    filter: brightness(0) invert(1) !important; /* 将图片变为白色 */
  }

  [data-theme="light"] .mobile-menu-toggle .menu-icon {
    filter: brightness(0) invert(0.2) !important; /* 浅色主题下变为深色 */
  }

  .mobile-menu-toggle:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2) !important;
  }

  .mobile-menu-toggle:hover .menu-icon {
    transform: scale(1.1) !important;
  }

  .mobile-menu-toggle:active {
    transform: translateY(0) scale(0.95) !important;
  }

  /* 菜单激活状态 */
  .mobile-menu-active .mobile-menu-toggle {
    background: rgba(147, 51, 234, 0.3) !important;
    border-color: rgba(147, 51, 234, 0.4) !important;
  }

  .mobile-menu-active .mobile-menu-toggle .menu-icon {
    transform: rotate(90deg) scale(1.1) !important;
    filter: brightness(0) invert(1) !important;
  }
}



/* 用户头像容器样式 */
.avatar-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.3rem 0.75rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

/* 深色主题下的用户头像容器 */
[data-theme="dark"] .avatar-container {
  background: linear-gradient(135deg, #3a3f65 0%, #23252f 100%);
  border-color: rgba(69, 104, 220, 0.3);
}

/* 浅色主题下的用户头像容器 */
[data-theme="light"] .avatar-container {
  background: linear-gradient(135deg, #f0e6ff 0%, #e6d6f9 100%);
  border-color: rgba(147, 51, 234, 0.2);
}

/* 用户头像容器悬停效果 */
[data-theme="dark"] .avatar-container:hover {
  background: linear-gradient(135deg, #4a4f75 0%, #33353f 100%);
  border-color: rgba(69, 104, 220, 0.5);
  box-shadow: 0 2px 8px rgba(69, 104, 220, 0.3);
}

[data-theme="light"] .avatar-container:hover {
  background: linear-gradient(135deg, #f9f2ff 0%, #efe5fc 100%);
  border-color: rgba(147, 51, 234, 0.3);
  box-shadow: 0 2px 8px rgba(147, 51, 234, 0.2);
}

/* 用户名称样式 */
[data-theme="dark"] .user-name {
  color: white;
}

[data-theme="light"] .user-name {
  color: #333333;
}

/* 用户下拉菜单样式 */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 0.5rem;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  min-width: 180px;
  z-index: 102;
  animation: fadeIn 0.2s ease;
  overflow: hidden;
  border: 1px solid var(--border-color);
}

/* 深色主题下的用户下拉菜单 */
[data-theme="dark"] .user-dropdown {
  background: rgba(28, 30, 44, 0.95);
  border-color: rgba(69, 104, 220, 0.2);
}

/* 浅色主题下的用户下拉菜单 */
[data-theme="light"] .user-dropdown {
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(147, 51, 234, 0.1);
}

/* 下拉菜单项样式 */
[data-theme="dark"] .dropdown-item {
  color: white;
}

[data-theme="light"] .dropdown-item {
  color: #333333;
}

/* 下拉菜单项图标样式 */
[data-theme="dark"] .dropdown-item i {
  color: #7b88ff;
}

[data-theme="light"] .dropdown-item i {
  color: #9333ea;
}

/* 下拉菜单分隔线样式 */
[data-theme="dark"] .dropdown-divider {
  background: rgba(69, 104, 220, 0.2);
}

[data-theme="light"] .dropdown-divider {
  background: rgba(147, 51, 234, 0.1);
}

/* 下拉菜单项悬停效果 */
[data-theme="dark"] .dropdown-item:hover {
  background: rgba(69, 104, 220, 0.2);
}

[data-theme="light"] .dropdown-item:hover {
  background: rgba(147, 51, 234, 0.1);
}

/* 货币和语言选项悬停效果 */
[data-theme="dark"] .currency-option:hover,
[data-theme="dark"] .language-option:hover {
  background: rgba(69, 104, 220, 0.2);
}

[data-theme="light"] .currency-option:hover,
[data-theme="light"] .language-option:hover {
  background: rgba(147, 51, 234, 0.1);
}

/* 活动状态的货币和语言选项 */
[data-theme="dark"] .currency-option.active,
[data-theme="dark"] .language-option.active {
  background: rgba(69, 104, 220, 0.3);
  font-weight: 600;
}

[data-theme="light"] .currency-option.active,
[data-theme="light"] .language-option.active {
  background: rgba(147, 51, 234, 0.2);
  font-weight: 600;
}

/* 移动端侧边栏样式 */
@media (max-width: 768px) {
  /* 侧边栏容器 */
  .nav-links {
    position: fixed;
    top: 0;
    right: -100%;
    width: 75%;
    max-width: 280px;
    height: 100vh;
    transition: right 0.3s ease;
    z-index: 100;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    padding: 0;
    border-radius: 30px 0 0 30px;
  }
  
  /* 深色主题侧边栏 */
  [data-theme="dark"] .nav-links {
    background: #46296c;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
  }
  
  /* 浅色主题侧边栏 */
  [data-theme="light"] .nav-links {
    background: #ffffff;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  }
  
  .nav-links.active {
    right: 0;
  }
  
  /* 用户信息区域 */
  .mobile-sidebar-user {
    width: 100%;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
  }
  
  /* 深色主题用户区域 */
  [data-theme="dark"] .mobile-sidebar-user {
    background: #46296c;
  }
  
  /* 浅色主题用户区域 */
  [data-theme="light"] .mobile-sidebar-user {
    background: #ffffff;
  }
  
  /* 用户头像 */
  .user-avatar-circle {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.3);
    margin-right: 5px;
  }
  
  .user-avatar-circle img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  /* 用户信息右侧区域 */
  .user-info-right {
    display: flex;
    flex-direction: column;
    justify-content: center;
    flex: 1;
    margin-top: 18px; /* 整体往下移动一点 */
  }

  /* 用户ID信息 */
  .user-id-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 8px;
  }
  
  .user-id-label {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 4px;
    letter-spacing: 0.5px;
  }
  
  [data-theme="dark"] .user-id-label {
    color: #ffffff;
  }
  
  [data-theme="light"] .user-id-label {
    color: #333333;
  }
  
  .user-id-number {
    font-size: 12px;
    opacity: 0.8;
  }
  
  [data-theme="dark"] .user-id-number {
    color: rgba(255, 255, 255, 0.8);
  }
  
  [data-theme="light"] .user-id-number {
    color: rgba(51, 51, 51, 0.8);
  }

  /* 侧边栏登录提示样式 */
  .sidebar-login-prompt {
    display: flex;
    align-items: center;
    gap: 12px;
    width: calc(100% - 20px); /* 减少总宽度，让按钮不会太靠右 */
    padding-right: 20px; /* 给右侧留出更多空间 */
  }

  .login-icon-small {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(120, 70, 200, 0.2);
    border: 2px solid rgba(120, 70, 200, 0.3);
  }

  .login-icon-small i {
    font-size: 1.5rem;
    color: #c3a3ff;
  }

  .login-info {
    flex: 1;
    min-width: 0; /* 防止文字过长时挤压其他元素 */
    margin-right: 8px; /* 与登录按钮保持固定间距 */
  }

  .login-title-small {
    color: #e0e0e0;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 2px;
  }

  .login-subtitle-small {
    color: #b0b0b0;
    font-size: 0.75rem;
    line-height: 1.2;
  }

  .sidebar-login-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #7846c8 0%, #9b59b6 100%);
    border-radius: 8px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(120, 70, 200, 0.3);
    flex-shrink: 0; /* 防止按钮被压缩 */
  }

  .sidebar-login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(120, 70, 200, 0.4);
  }

  .sidebar-login-btn i {
    font-size: 1rem;
  }

  /* 浅色主题适配 */
  [data-theme="light"] .login-icon-small {
    background: rgba(120, 70, 200, 0.1);
    border: 2px solid rgba(120, 70, 200, 0.2);
  }

  [data-theme="light"] .login-icon-small i {
    color: #7846c8;
  }

  [data-theme="light"] .login-title-small {
    color: #333333;
  }

  [data-theme="light"] .login-subtitle-small {
    color: #666666;
  }

  /* 用户主题切换按钮 */
  .user-theme-toggle {
    display: flex;
    align-items: center;
    /* gap: 8px; */
    padding: 4px 8px;
    border-radius: 6px;
    background: rgba(120, 70, 200, 0.1);
    border: 1px solid rgba(120, 70, 200, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    width: fit-content;
  }

  .user-theme-toggle:hover {
    background: rgba(120, 70, 200, 0.2);
    border-color: rgba(120, 70, 200, 0.3);
  }

  .user-theme-toggle .theme-icon {
    width: 16px;
    height: 16px;
    object-fit: contain;
    filter: brightness(0) invert(1); /* 深色主题：白色图标 */
  }

  [data-theme="light"] .user-theme-toggle {
    background: rgba(120, 70, 200, 0.05);
    border: 1px solid rgba(120, 70, 200, 0.15);
  }

  [data-theme="light"] .user-theme-toggle .theme-icon {
    filter: brightness(0) invert(0.2); /* 浅色主题：深色图标 */
  }

  /* 导航项目 */
  .sidebar-nav-item {
    display: flex;
    align-items: center;
    width: 100%;
    height: 60px;
    padding: 0 25px;
    margin: 5px 0;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: background-color 0.2s ease;
    border-radius: 15px;
    letter-spacing: 0.5px;
  }
  
  /* 导航项目图标 */
  .sidebar-nav-item i {
    margin-right: 15px;
    font-size: 16px;
    width: 20px;
    text-align: center;
  }
  
  /* 深色主题导航项目 */
  [data-theme="dark"] .sidebar-nav-item {
    background: #1e1133;
    color: #ffffff;
    margin: 5px 10px;
    border-radius: 15px;
  }
  
  [data-theme="dark"] .sidebar-nav-item.router-link-active {
    background: #7b4ca7;
    color: #ffffff;
  }
  
  /* 浅色主题导航项目 */
  [data-theme="light"] .sidebar-nav-item {
    background: #e8d1f6;
    color: #333333;
    margin: 5px 10px;
    border-radius: 15px;
  }
  
  [data-theme="light"] .sidebar-nav-item.router-link-active {
    background: #7b4ca7;
    color: #ffffff;
  }

  /* JOIN US 按钮样式 */
  .join-us-button {
    display: flex;
    align-items: center;
    width: calc(100% - 20px);
    height: 50px;
    margin: 10px 10px;
    padding: 0 20px;
    background: linear-gradient(135deg, #7846c8 0%, #9b59b6 100%);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(120, 70, 200, 0.3);
  }

  .join-us-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(120, 70, 200, 0.4);
  }

  .join-us-icons {
    display: flex;
    gap: 8px;
    margin-right: 15px;
  }

  .join-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .discord-icon {
    background: #5865f2;
  }

  .telegram-icon {
    background: #0088cc;
  }

  .join-icon i {
    color: white;
    font-size: 16px;
  }

  .join-icon:hover {
    transform: scale(1.1);
  }

  .join-us-text {
    color: white;
    font-weight: 600;
    font-size: 14px;
    letter-spacing: 0.5px;
  }

  /* JOIN US 按钮主题适配 */
  [data-theme="dark"] .join-us-button {
    background: linear-gradient(135deg, #7846c8 0%, #9b59b6 100%);
    box-shadow: 0 4px 15px rgba(120, 70, 200, 0.3);
  }

  [data-theme="dark"] .join-us-button:hover {
    box-shadow: 0 6px 20px rgba(120, 70, 200, 0.4);
  }

  [data-theme="light"] .join-us-button {
    background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
  }

  [data-theme="light"] .join-us-button:hover {
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
  }

  /* Discord 图标主题适配 */
  [data-theme="dark"] .discord-icon {
    background: #5865f2;
  }

  [data-theme="light"] .discord-icon {
    background: #4f46e5; /* 浅色主题下稍微深一点的紫色 */
  }

  /* Telegram 图标主题适配 */
  [data-theme="dark"] .telegram-icon {
    background: #0088cc;
  }

  [data-theme="light"] .telegram-icon {
    background: #0ea5e9; /* 浅色主题下稍微亮一点的蓝色 */
  }

  /* 文字颜色在两个主题下都保持白色 */
  [data-theme="dark"] .join-us-text,
  [data-theme="light"] .join-us-text {
    color: white;
  }

  /* 图标颜色在两个主题下都保持白色 */
  [data-theme="dark"] .join-icon i,
  [data-theme="light"] .join-icon i {
    color: white;
  }

  /* 主题切换按钮 */
  .sidebar-theme-toggle {
    display: flex;
    align-items: center;
    width: calc(100% - 20px);
    height: 60px;
    padding: 0 25px;
    margin: 5px 10px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-radius: 15px;
    letter-spacing: 0.5px;
    margin-top: auto;
    margin-bottom: 20px;
  }
  
  /* 深色主题切换按钮 */
  [data-theme="dark"] .sidebar-theme-toggle {
    background: #1e1133;
    color: #ffffff;
  }
  
  /* 浅色主题切换按钮 */
  [data-theme="light"] .sidebar-theme-toggle {
    background: #e8d1f6;
    color: #333333;
  }
  
  .sidebar-theme-toggle i {
    margin-right: 15px;
    font-size: 16px;
    width: 20px;
    text-align: center;
  }
  
  /* 开关样式 */
  .toggle-switch {
    margin: 0 10px;
  }
  
  .toggle-track {
    position: relative;
    width: 40px;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
  }
  
  [data-theme="light"] .toggle-track {
    background: rgba(0, 0, 0, 0.2);
  }
  
  .toggle-track.active {
    background: rgba(123, 76, 167, 0.6);
  }
  
  .toggle-thumb {
    position: absolute;
    width: 16px;
    height: 16px;
    background: #ffffff;
    border-radius: 50%;
    top: 2px;
    left: 2px;
    transition: transform 0.3s ease;
  }
  
  .toggle-track.active .toggle-thumb {
    transform: translateX(20px);
  }
  
  /* 侧边栏滚动条样式 */
  .nav-links::-webkit-scrollbar {
    width: 4px;
  }
  
  .nav-links::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .nav-links::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }
  
  [data-theme="light"] .nav-links::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.1);
  }
  
  /* 移动端覆盖层样式 */
  .mobile-menu-overlay {
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(3px);
  }
}

/* 侧边栏小竖杠样式 */
.sidebar-indicator {
  position: absolute !important;
  left: 3px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 5px !important;
  height: 15% !important;
  background: rgba(255, 255, 255, 0.5) !important;
  border-radius: 5px !important;
  z-index: 10 !important;
  cursor: pointer !important;
  transition: all 0.3s ease !important;
}

.sidebar-indicator:hover {
  background: rgba(255, 255, 255, 0.8) !important;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5) !important;
}

.sidebar-indicator:active {
  transform: translateY(-50%) scale(0.9) !important;
}

[data-theme="light"] .sidebar-indicator {
  background: rgba(147, 51, 234, 0.3) !important;
}

[data-theme="light"] .sidebar-indicator:hover {
  background: rgba(147, 51, 234, 0.6) !important;
  box-shadow: 0 0 10px rgba(147, 51, 234, 0.4) !important;
}

/* 调整导航项目位置 */
@media (max-width: 768px) {
  .sidebar-nav-item {
    margin-left: 30px !important;
    width: calc(100% - 40px) !important;
  }
  
  .sidebar-theme-toggle {
    margin-left: 30px !important;
    width: calc(100% - 40px) !important;
  }
}

/* 修改移动端导航栏标题样式,避免覆盖侧边栏 */
@media (max-width: 768px) {
  /* 标题居中 */
  .nav-logo {
    position: absolute !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 90 !important; /* 确保logo的z-index低于侧边栏 */
    transition: opacity 0.3s ease !important;
  }
  
  /* 当侧边栏打开时,隐藏或调整logo,防止覆盖侧边栏 */
  .mobile-menu-active .nav-logo {
    opacity: 0.2 !important; /* 降低logo不透明度使其不那么明显 */
    z-index: 90 !important; /* 确保比侧边栏的z-index低 */
  }
  
  /* 确保侧边栏z-index高于logo */
  .nav-links.active {
    z-index: 100 !important;
  }
  
  /* 侧边栏打开时的覆盖层样式 */
  .mobile-menu-overlay {
    z-index: 90 !important; 
  }
}
</style>

<style scoped>
/* ... existing code ... */
</style>
