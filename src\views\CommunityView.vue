<template>
  <div class="page-background">
    <div class="community-container">
      <!-- 搜索框 -->
      <div class="search-container">
        <div class="search-icon">
            <i class="fas fa-search"></i>
          </div>
          <input 
            type="text" 
          placeholder="Search" 
          class="search-input"
          v-model="searchKeyword"
          @keyup.enter="searchPosts"
        >
      </div>

      <!-- 标签切换按钮 -->
      <div class="news-tabs">
        <div class="slider" :class="{ 'hottest': activeTab === 'hottest' }"></div>
        <button 
          class="tab-button" 
          :class="{ 'active': activeTab === 'latest' }"
          @click="setActiveTab('latest')"
        >Latest News</button>
        <button 
          class="tab-button" 
          :class="{ 'active': activeTab === 'hottest' }"
          @click="setActiveTab('hottest')"
        >Hottest News</button>
      </div>

      <!-- 内容卡片部分 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
      </div>

      <div v-else class="content-cards">
        <!-- 动态渲染帖子卡片 -->
        <div 
          v-for="post in displayPosts" 
          :key="post.id" 
          class="content-card"
          @click="navigateToPostDetail(post.id)"
        >
          <div class="card-image purple-gradient" :style="post.coverImage ? { backgroundImage: `url(${post.coverImage})` } : {}">
            <!-- 如果帖子有图片，则显示第一张图片 -->
            <img v-if="post.images && post.images.length > 0" :src="post.images[0].imageUrl" alt="Post image" class="post-image">
      </div>
          <div class="card-content">
            <div class="card-title">{{ post.title || 'Untitled Post' }}</div>
            <div class="card-actions">
              <button class="action-button" @click.stop="likePost(post)">
                <i :class="post.isLiked ? 'fas fa-heart' : 'far fa-heart'"></i>
                <span class="action-count">{{ post.likes || 0 }}</span>
          </button>
              <button class="action-button" @click.stop="navigateToPostDetail(post.id)">
                    <i class="far fa-comment"></i> 
                <span class="action-count">{{ post.count || 0 }}</span>
                  </button>
              <button class="action-button">
                <i class="far fa-eye"></i>
                <span class="action-count">{{ post.views || 0 }}</span>
                  </button>
              <button class="action-button" @click.stop="sharePost(post)">
                <i class="fas fa-share"></i>
        </button>
      </div>
      </div>
          </div>
          
        <!-- 无内容提示 -->
        <div v-if="displayPosts.length === 0" class="no-content-message">
          No posts found. Be the first to create a post!
            </div>
              </div>
              </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import postsApi from '@/api/posts';

export default {
  name: 'CommunityView',
  setup() {
    const router = useRouter();
    const activeTab = ref('latest');
    // 删除未使用的变量
    const latestPosts = ref([]);
    const hottestPosts = ref([]);
    const loading = ref(true);
    const error = ref(null);
    const searchKeyword = ref('');
    
    // 获取帖子列表
    const fetchPosts = async () => {
      loading.value = true;
      try {
        // 获取最新帖子，默认按时间排序
        const latestResponse = await postsApi.getAllPosts({
          sortBy: 'time'
        });
        
        if (latestResponse.code === 200 && Array.isArray(latestResponse.data)) {
          latestPosts.value = latestResponse.data.map(post => ({
              ...post,
            isLiked: false // 初始化点赞状态
          }));
        }
        
        // 获取最热帖子（按浏览量排序）
        const hottestResponse = await postsApi.getAllPosts({
          sortBy: 'views'
        });
        
        if (hottestResponse.code === 200 && Array.isArray(hottestResponse.data)) {
          hottestPosts.value = hottestResponse.data.map(post => ({
              ...post,
            isLiked: false // 初始化点赞状态
          }));
        }
        
        // 获取用户点赞状态
        if (latestPosts.value.length > 0 || hottestPosts.value.length > 0) {
          await checkUserLikeStatus();
        }
      } catch (err) {
        console.error('获取帖子失败:', err);
        error.value = '获取帖子失败，请稍后再试';
      } finally {
        loading.value = false;
      }
    };
    
    // 根据当前标签显示对应的帖子
    const displayPosts = computed(() => {
      return activeTab.value === 'latest' ? latestPosts.value : hottestPosts.value;
    });
    
    // 切换标签
    const setActiveTab = (tab) => {
      if (activeTab.value === tab) return; // 如果点击的是当前标签，不执行操作
      
      activeTab.value = tab;
      
      // 如果有搜索关键词，则根据当前标签重新搜索
      if (searchKeyword.value.trim()) {
        searchPosts();
      }
    };
    
    // 点赞/取消点赞帖子
    const likePost = async (post) => {
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，提示用户登录
        if (!userId) {
          alert('请先登录再进行点赞');
          router.push('/login');
          return;
        }

        const postId = post.id || post.postId;
        if (!postId) return;
        
        if (post.isLiked) {
          // 取消点赞 - 先立即更新UI状态
          updatePostLikeStatus(postId, false);
          // 点赞数减1（如果当前点赞数大于0）
          if (post.likes && post.likes > 0) {
            updatePostLikesCount(postId, post.likes - 1);
          }
          
          // 调用API
          const response = await postsApi.unlikePostNew({
          userId,
            postId
          });
          
          if (response.code === 200) {
            // 如果API返回了最新的点赞数，使用API返回的值更新
            if (response.data && response.data.likeCount !== undefined) {
              updatePostLikesCount(postId, response.data.likeCount);
            }
        } else {
            // 如果API失败，恢复原状态
            updatePostLikeStatus(postId, true);
            updatePostLikesCount(postId, post.likes);
            console.error('取消点赞失败:', response.msg);
          }
        } else {
          // 点赞 - 先立即更新UI状态
          updatePostLikeStatus(postId, true);
          // 点赞数加1
          updatePostLikesCount(postId, (post.likes || 0) + 1);
          
          // 调用API
          const response = await postsApi.likePostNew({
          userId,
            postId
          });
          
          if (response.code === 200) {
            // 如果API返回了最新的点赞数，使用API返回的值更新
            if (response.data && response.data.likeCount !== undefined) {
              updatePostLikesCount(postId, response.data.likeCount);
            }
        } else {
            // 如果API失败，恢复原状态
            updatePostLikeStatus(postId, false);
            updatePostLikesCount(postId, post.likes || 0);
            console.error('点赞失败:', response.msg);
          }
        }
      } catch (err) {
        console.error('点赞操作失败:', err);
      }
    };

    // 更新帖子点赞状态
    const updatePostLikeStatus = (postId, isLiked) => {
      latestPosts.value = latestPosts.value.map(post => {
        if ((post.id || post.postId) === postId) {
          return { ...post, isLiked };
        }
        return post;
      });
      
      hottestPosts.value = hottestPosts.value.map(post => {
        if ((post.id || post.postId) === postId) {
          return { ...post, isLiked };
        }
        return post;
      });
    };

    // 更新帖子点赞数量
    const updatePostLikesCount = (postId, likesCount) => {
      latestPosts.value = latestPosts.value.map(post => {
        if ((post.id || post.postId) === postId) {
          return { ...post, likes: likesCount };
        }
        return post;
      });
      
      hottestPosts.value = hottestPosts.value.map(post => {
        if ((post.id || post.postId) === postId) {
          return { ...post, likes: likesCount };
        }
        return post;
      });
    };

    // 检查用户点赞状态
    const checkUserLikeStatus = async () => {
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果没有用户ID，则不进行点赞状态检查
        if (!userId) {
          console.log('未登录用户，跳过点赞状态检查');
          return;
        }
        
        // 收集所有帖子ID
        const allPostIds = [
          ...latestPosts.value.map(post => post.id || post.postId),
          ...hottestPosts.value.map(post => post.id || post.postId)
        ].filter(Boolean);
        
        if (allPostIds.length === 0) return;
        
        // 调用批量检查API
        const likeStatusResponse = await postsApi.batchCheckPostLikeStatus({
          userId,
          postIds: allPostIds
        });
        
        if (likeStatusResponse.code === 200) {
          console.log('点赞状态检查结果:', likeStatusResponse.data);
          
          // 根据返回类型处理数据
          let likedPostIds = [];
          
          if (Array.isArray(likeStatusResponse.data)) {
            // 如果返回的是已点赞的ID数组
            likedPostIds = likeStatusResponse.data;
          } else if (typeof likeStatusResponse.data === 'object') {
            // 如果返回的是{postId: boolean}格式的对象
            likedPostIds = Object.entries(likeStatusResponse.data)
              .filter(([, isLiked]) => isLiked) // 修改此行，使用逗号代替下划线
              .map(([postId]) => postId);
          }
          
          console.log('已点赞的帖子ID:', likedPostIds);
          
          // 更新点赞状态
          latestPosts.value = latestPosts.value.map(post => {
            const postId = post.id || post.postId;
            return {
              ...post,
              isLiked: likedPostIds.includes(postId)
            };
          });
          
          hottestPosts.value = hottestPosts.value.map(post => {
            const postId = post.id || post.postId;
            return {
              ...post,
              isLiked: likedPostIds.includes(postId)
            };
          });
        }
      } catch (err) {
        console.error('获取点赞状态失败:', err);
      }
    };
    
    // 导航到帖子详情页
    const navigateToPostDetail = (postId) => {
      if (postId) {
        router.push(`/client/post/${postId}`);
      }
    };
    
    // 分享帖子
    const sharePost = (post) => {
      // 实现分享功能，例如复制链接或打开分享对话框
      const postUrl = `${window.location.origin}/client/post/${post.id || post.postId}`;
      navigator.clipboard.writeText(postUrl).then(() => {
        alert('Link copied to clipboard!');
      }).catch(err => {
        console.error('Failed to copy link:', err);
      });
    };
    
    // 搜索帖子
    const searchPosts = async () => {
      loading.value = true;
      try {
        // 根据当前标签决定排序方式
        const sortBy = activeTab.value === 'latest' ? 'time' : 'views';
        
        const params = {
          sortBy: sortBy
        };
        
        // 如果有搜索关键词，添加到请求参数中
        if (searchKeyword.value.trim()) {
          params.title = searchKeyword.value.trim();
        }
        
        const response = await postsApi.getAllPosts(params);
        
        if (response.code === 200 && Array.isArray(response.data)) {
          // 根据当前标签更新对应的帖子列表
          if (activeTab.value === 'latest') {
            latestPosts.value = response.data.map(post => ({
              ...post,
              isLiked: false
            }));
          } else {
            hottestPosts.value = response.data.map(post => ({
              ...post,
              isLiked: false
            }));
          }
          
          // 检查点赞状态
          await checkUserLikeStatus();
          }
        } catch (err) {
        console.error('搜索帖子失败:', err);
      } finally {
        loading.value = false;
      }
    };
    
    // 组件挂载时获取数据
    onMounted(() => {
      fetchPosts();
    });
    
    return {
      activeTab,
      displayPosts,
      loading,
      searchKeyword,
      setActiveTab,
      likePost,
      navigateToPostDetail,
      sharePost,
      searchPosts
    };
  }
}
</script>

<style scoped>
.page-background {
  width: 100%;
  min-height: 100vh;
  background: #0F0823;
  padding: 30px 0;
}

.community-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  color: #ffffff;
}

/* 搜索框样式 */
.search-container {
  position: relative;
  margin-bottom: 30px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #fff;
  font-size: 18px;
}

.search-input {
  width: 100%;
  background: rgba(30, 18, 40, 0.6);
  border: none;
  border-radius: 30px;
  padding: 15px 20px 15px 50px;
  color: #fff;
  font-size: 16px;
  outline: none;
  box-shadow: 0 2px 10px rgba(0,0,0,0.2);
  transition: box-shadow 0.3s ease;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

/* 标签切换按钮样式 */
.news-tabs {
  display: flex;
  background: rgba(30, 18, 40, 0.6);
  border-radius: 30px;
  margin: 0 auto 30px;
  overflow: hidden;
  position: relative;
  max-width: 600px;
  width: 100%;
  box-shadow: 0 3px 10px rgba(0,0,0,0.3);
}

.news-tabs .slider {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  background: linear-gradient(135deg, rgba(142, 44, 165, 1), rgba(84, 44, 130, 1));
  border-radius: 30px;
  transition: transform 0.3s ease;
  z-index: 1;
}

.news-tabs .slider.hottest {
  transform: translateX(100%);
}

.tab-button {
  flex: 1;
  background: transparent;
  border: none;
  color: #fff;
  padding: 15px 0;
    font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.tab-button.active {
  font-weight: 700;
  color: #fff;
}

/* 内容卡片样式 */
.content-cards {
  display: flex;
  flex-direction: column;
  gap: 40px;
  margin-bottom: 50px;
  max-width: 861px;
  margin-left: auto;
  margin-right: auto;
  align-items: center;
}

.content-card {
  width: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.5);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  cursor: pointer;
}

.card-image {
  width: 100%;
  background-size: cover;
  background-position: center;
}

.purple-gradient {
  background: linear-gradient(180deg, rgba(142, 44, 165, 1) 0%, rgba(112, 44, 150, 1) 30%, rgba(84, 44, 130, 1) 60%, rgba(57, 36, 78, 1) 100%);
}

.card-content {
  background: linear-gradient(180deg, rgba(57, 36, 78, 0.8) 0%, rgba(30, 18, 40, 0.9) 50%, rgba(19, 6, 20, 1) 100%);
  border-radius: 0 0 15px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
}

.card-title {
  font-size: 26px;
  font-weight: 500;
  color: #fff;
}

.card-actions {
  display: flex;
  gap: 35px;
}

.action-button {
  background: transparent;
  border: none;
  color: #fff;
  font-size: 26px;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 5px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.action-count {
  font-size: 14px;
  font-weight: 400;
}

.post-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.action-button:hover {
  transform: scale(1.15);
}

.content-card:hover {
  transform: scale(1.02);
  box-shadow: 0 12px 35px rgba(142, 44, 165, 0.4);
}

/* 加载指示器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  width: 100%;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top: 4px solid #bf52e3;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 无内容提示 */
.no-content-message {
  text-align: center;
  padding: 50px 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 900px) {
  .content-card {
    max-width: 100%;
    height: auto;
  }
  
  .card-image {
    height: 300px;
  }
  
  .card-content {
    height: auto;
  flex-direction: column;
    padding: 20px;
    gap: 15px;
  }
  
  .card-title {
    font-size: 20px;
  text-align: center;
  }
}

/* PC端样式 */
@media (min-width: 768px) {
  .content-cards {
    max-width: 861px;
  }
  
  .content-card {
    max-width: 861px;
    height: 490px;
  }
  
  .card-image {
    height: 400px;
  }
  
  .card-content {
    height: 90px;
  }
}

/* 移动端样式 */ 
@media (max-width: 767px) {
  .page-background {
    padding: 10px 0;
  }

  .community-container {
    padding: 0;
    max-width: 100%;
  }
  
  .search-container {
    margin-bottom: 20px;
    padding: 0 15px;
  }
  
  .news-tabs {
    margin: 0 auto 20px;
  }
  
  .content-cards {
    gap: 15px;
    padding: 0;
    max-width: 100%;
    width: 100%;
  }
  
  .content-card {
    max-width: 100%;
    height: 209px;
    position: relative;
    width: calc(103% - 20px);
    margin: 0 auto;
    border-radius: 15px;
  }
  
  .card-image {
    height: 209px;
    position: relative;
    border-radius: 15px;
    width: 100%;
  }
  
  .card-content {
    position: absolute;
    bottom: 0;
  left: 0;
  right: 0;
    background: linear-gradient(180deg, transparent 0%, rgba(84, 44, 130, 0.5) 20%, rgba(57, 36, 78, 0.8) 50%, rgba(19, 6, 20, 0.95) 80%);
    height: 70px;
    padding: 0 20px;
  display: flex;
    flex-direction: row;
  align-items: center;
  justify-content: space-between;
    border-radius: 0 0 15px 15px;
  }
  
  .card-title {
    font-size: 16px;
    margin-bottom: 0;
    text-align: left;
    max-width: 220px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: 400;
  }
  
  .card-actions {
    gap: 22px;
  justify-content: flex-end;
  }
  
  .action-button {
    font-size: 20px;
    padding: 0;
    color: white;
  display: flex;
  align-items: center;
    gap: 3px;
  }
  
  .action-count {
    font-size: 12px;
  }
  
  .search-input {
    padding: 12px 15px 12px 45px;
  font-size: 14px;
  }
  
  .search-icon {
    left: 15px;
    font-size: 16px;
  }
  
  .tab-button {
    padding: 12px 0;
    font-size: 16px;
  }
}

@media (max-width: 480px) {
  .card-content {
    height: 60px;
    padding: 0 15px;
  }
  
  .card-title {
  font-size: 14px;
    max-width: 180px;
  }
  
  .card-actions {
    gap: 18px;
  }
  
  .action-button {
    font-size: 18px;
  }
  
  .action-count {
    font-size: 10px;
  }
  
  .news-tabs {
    max-width: 320px;
  }
}

/* 修改响应式布局，确保任何屏幕宽度都是单列 */
@media (min-width: 768px) and (max-width: 1023px) {
  .content-cards {
  display: flex;
  flex-direction: column;
  }
}

@media (min-width: 1024px) {
  .content-cards {
  display: flex;
  flex-direction: column;
  }
}

/* 添加移动端优化 */
@media (max-width: 480px) {
  .card-image {
    height: 180px;
  }
  
  .news-tabs {
    max-width: 320px;
  }
  
  .card-actions {
    gap: 15px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .action-button {
  font-size: 18px;
  }
}
</style>
