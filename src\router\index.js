import { createRouter, createWebHistory } from 'vue-router'
import SellersView from '../views/SellersView.vue'
import { tokenUtil } from '@/services/api'
import { ElMessage } from 'element-plus'

const routes = [
  {
    path: '/',
    name: 'root',
    component: () => import('../views/HomeView.vue')
  },
  {
    path: '/home',
    name: 'home',
    component: () => import('../views/HomeView.vue')
  },
  {
    path: '/products',
    name: 'products',
    component: () => import('../views/ProductsView.vue')
  },
  {
    path: '/productstwo',
    name: 'productstwo',
    component: () => import('../views/ProductsTwo.vue')
  },
  {
    path: '/product/:id',
    name: 'product-detail',
    component: () => import('../views/ProductDetailView.vue'),
    props: true,
    meta: {
      // 使用这个meta属性作为路由的独特标识，确保在不同产品ID之间导航时组件会重新创建
      alwaysRefresh: true
    }
  },
  {
    path: '/sellers',
    name: 'sellers',
    component: SellersView
  },
  {
    path: '/faq',
    name: 'faq',
    redirect: '/#faq-anchor'
  },
  {
    path: '/how-to',
    name: 'how-to',
    component: () => import('../views/HowToView.vue')
  },
  {
    path: '/about',
    name: 'about',
    // route level code-splitting
    // this generates a separate chunk (about.[hash].js) for this route
    // which is lazy-loaded when the route is visited.
    component: () => import(/* webpackChunkName: "about" */ '../views/AboutView.vue')
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('../views/LoginView.vue')
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('../views/RegisterView.vue')
  },
  {
    path: '/favorites',
    name: 'favorites',
    component: () => import('../views/FavoritesView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/profile',
    name: 'profile',
    component: () => import('../views/ProfileView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/bookmarked-posts',
    name: 'bookmarked-posts',
    component: () => import('../views/BookmarkedPostsView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/my-posts',
    name: 'my-posts',
    component: () => import('../views/UserPostsView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/points-center',
    name: 'points-center',
    component: () => import('../views/PointsCenterView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/favorite-sellers',
    name: 'favorite-sellers',
    component: () => import('../views/FavoriteSellersView.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/community',
    name: 'community',
    component: () => import('../views/CommunityView.vue')
  },
  {
    path: '/post/:id',
    name: 'post-detail',
    component: () => import('../views/PostDetailView.vue'),
    props: true
  },
  {
    path: '/client/post/:id',
    name: 'client-post-detail',
    component: () => import('../views/PostDetailView.vue'),
    props: true
  },
  {
    path: '/community/post/:id/comments',
    name: 'post-comments',
    component: () => import('../views/PostDetailView.vue'),
    props: route => ({
      id: route.params.id,
      scrollToComments: true
    })
  },
  {
    path: '/front/oauth2/code/google',
    name: 'google-callback',
    component: () => import('../views/GoogleCallback.vue')
  },
  {
    path: '/trending',
    name: 'trending',
    component: () => import('../views/TrendingProductsView.vue')
  },
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  base: '/client/',
  routes,
  scrollBehavior(to, from, savedPosition) {
    // 如果用户使用浏览器的前进/后退按钮，恢复到之前保存的位置
    if (savedPosition) {
      return savedPosition;
    }

    // 如果路由有hash，滚动到指定的锚点
    if (to.hash) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            el: to.hash,
            behavior: 'smooth',
            top: 80, // 添加一些偏移，避免被导航栏遮挡
          });
        }, 500); // 延迟500毫秒，确保组件已经渲染
      });
    }

    // 对于到商品详情页面的导航，始终滚动到顶部，立即执行不使用平滑效果
    if (to.name === 'product-detail') {
      return { top: 0, left: 0, behavior: 'auto' };
    }

    // 默认情况下滚动到顶部
    return { top: 0, left: 0 };
  }
})



// 全局前置守卫，检查是否需要登录
router.beforeEach((to, from, next) => {
  // 检查目标路由是否需要登录
  if (to.matched.some(record => record.meta.requiresAuth)) {
    // 检查是否已登录
    if (!tokenUtil.isLoggedIn()) {
      // 未登录，显示提示信息
      ElMessage({
        message: '请先登录后再访问此页面',
        type: 'warning',
        duration: 2000
      })
      // 重定向到登录页面，并记录原始目标路由
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
    } else {
      // 已登录，允许访问
      next()
    }
  } else {
    // 不需要登录的页面，直接访问
    next()
  }
})

// 添加路由守卫，处理同路由不同参数的情况
router.beforeEach((to, from, next) => {
  // 如果是同一路由但不同参数，且目标路由需要强制刷新
  if (
    to.name === from.name &&
    to.params.id !== from.params.id &&
    to.meta.alwaysRefresh
  ) {
    // 给组件一个唯一的key，强制Vue重新创建组件实例
    to.matched.forEach(record => {
      // 通过随机数或时间戳作为key，确保每次都是唯一的
      record.meta.componentKey = Date.now();
    });
  }
  next();
});

export default router
