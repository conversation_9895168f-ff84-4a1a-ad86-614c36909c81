<template>
  <StarryBackground />
  <div class="favorites-page">
    <h1>My Favorites</h1>

    <!-- 移动端搜索框 -->
    <div class="mobile-search-container">
      <div class="search-box">
        <el-icon class="search-icon"><Search /></el-icon>
        <input
          v-model="searchQuery"
          type="text"
          placeholder="Search"
          class="search-input"
          @input="handleSearch"
        />
      </div>
    </div>

    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="favorites.length === 0" class="empty-favorites">
      <el-empty description="You haven't added any products to favorites">
        <template #image>
          <el-icon style="font-size: 64px; color: #c3a3ff;">
            <Box />
          </el-icon>
        </template>
        <el-button type="primary" @click="$router.push('/products')">
          Browse Products
        </el-button>
      </el-empty>
    </div>
    
    <div v-else class="favorites-container">
      <!-- PC端布局 -->
      <div class="pc-layout">
        <el-row :gutter="16">
          <el-col v-for="item in filteredFavorites" :key="item.productId" :xs="12" :sm="12" :md="6" :lg="6" :xl="6">
            <el-card class="product-card" shadow="hover">
              <div class="product-image">
                <img :src="(item.omgProducts && item.omgProducts.mainImage) || item.mainImage || '/placeholder.png'"
                     :alt="(item.omgProducts && item.omgProducts.name) || item.name"
                     @click="viewProduct(item.productId)">
              </div>
              <div class="card-content">
                <div class="product-info" @click="viewProduct(item.productId)">
                  <h3>{{ (item.omgProducts && item.omgProducts.name) || item.name }}</h3>
                  <p class="price">￥{{ (item.omgProducts && item.omgProducts.price) || item.price }}</p>
                  <p class="date">Added: {{ formatDate(item.createdAt) }}</p>
                </div>
                <div class="action-area">
                  <el-button
                    class="remove-btn"
                    size="small"
                    type="danger"
                    plain
                    @click="confirmRemove(item.productId)"
                  >
                    Remove
                  </el-button>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 移动端布局 -->
      <div class="mobile-layout">
        <div v-for="item in filteredFavorites" :key="item.productId" class="mobile-product-card">
          <div class="mobile-product-image" @click="viewProduct(item.productId)">
            <img :src="(item.omgProducts && item.omgProducts.mainImage) || item.mainImage || '/placeholder.png'"
                 :alt="(item.omgProducts && item.omgProducts.name) || item.name">
          </div>
          <div class="mobile-product-info" @click="viewProduct(item.productId)">
            <h3 class="mobile-product-title">{{ (item.omgProducts && item.omgProducts.name) || item.name }}</h3>
            <p class="mobile-product-subtitle">{{ (item.omgProducts && item.omgProducts.name) || item.name }} (10+)</p>
            <div class="mobile-price-container">
              <span class="mobile-price">￥{{ (item.omgProducts && item.omgProducts.price) || item.price }}</span>
              <span class="mobile-price-suffix">￥311</span>
            </div>
          </div>
          <div class="mobile-favorite-icon" @click="confirmRemove(item.productId)">
            <el-icon :size="20">
              <Heart />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFavorites, removeFavorite } from '@/api/favorites'
import { Box, Search, Heart } from '@element-plus/icons-vue'
import StarryBackground from '@/components/StarryBackground.vue'

export default {
  name: 'FavoritesView',
  components: {
    StarryBackground,
    Search,
    Heart
  },
  setup() {
    const router = useRouter()
    const favorites = ref([])
    const loading = ref(true)
    const isDarkMode = ref(true)
    const searchQuery = ref('')

    // 过滤后的收藏列表
    const filteredFavorites = computed(() => {
      if (!searchQuery.value.trim()) {
        return favorites.value
      }

      const query = searchQuery.value.toLowerCase().trim()
      return favorites.value.filter(item => {
        const name = ((item.omgProducts && item.omgProducts.name) || item.name || '').toLowerCase()
        return name.includes(query)
      })
    })

    // 搜索处理函数
    const handleSearch = () => {
      // 搜索逻辑已通过computed属性实现
    }

    const fetchFavorites = async () => {
      loading.value = true
      try {
        const response = await getFavorites()
        console.log('API返回的完整响应：', response)
        
        // 处理新的数据结构
        if (response.data && Array.isArray(response.data)) {
          favorites.value = response.data
        } else if (response.data && response.data.list && Array.isArray(response.data.list)) {
          favorites.value = response.data.list
        } else if (Array.isArray(response)) {
          favorites.value = response
        } else if (response.code === 200 && Array.isArray(response.data)) {
          favorites.value = response.data
        } else {
          console.error('无法识别的数据格式:', response)
          favorites.value = []
        }
        
        console.log('处理后的收藏数据:', favorites.value)
        
        if (favorites.value.length > 0) {
          // 检查数据结构，查看第一个元素的属性
          console.log('第一个收藏项的数据结构:', JSON.stringify(favorites.value[0]))
        }
      } catch (error) {
        console.error('获取收藏列表失败:', error)
        ElMessage.error('加载收藏产品失败')
      } finally {
        loading.value = false
      }
    }

    // 检测当前主题
    const checkCurrentTheme = () => {
      // 检查HTML元素的data-theme属性
      const htmlElement = document.documentElement;
      const theme = htmlElement.getAttribute('data-theme');
      isDarkMode.value = theme === 'dark';
      console.log('当前主题:', theme, '是暗色系:', isDarkMode.value);
    };
    
    // 处理主题变化
    const handleThemeChange = (data) => {
      if (data && typeof data.isDarkMode !== 'undefined') {
        isDarkMode.value = data.isDarkMode;
      }
    };

    const confirmRemove = (productId) => {
      ElMessageBox.confirm(
        'Are you sure you want to remove this item from favorites?',
        'Confirm',
        {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }
      ).then(() => {
        removeFromFavorites(productId)
      }).catch(() => {
        // 用户点击取消，不做任何操作
      })
    }

    const removeFromFavorites = async (productId) => {
      try {
        const response = await removeFavorite(productId)
        console.log('取消收藏响应:', response)
        
        // 无论返回结果如何，从本地列表中移除
        favorites.value = favorites.value.filter(item => {
          // 使用productId作为唯一标识
          return item.productId != productId // 使用非严格比较，因为ID可能是字符串或数字
        })
        
        ElMessage.success('Successfully removed from favorites')
      } catch (error) {
        console.error('移除收藏失败:', error)
        ElMessage.error('Failed to remove from favorites')
      } finally {
        // 如果请求成功，并且收藏数量为0，可能需要显示空状态
        if (favorites.value.length === 0) {
           // 这边可能需要重新评估empty-favorites的显示逻辑
        }
      }
    }

    const viewProduct = (productId) => {
      console.log('查看产品详情:', productId)
      router.push({ name: 'product-detail', params: { id: productId } })
    }

    const formatDate = (dateString) => {
      if (!dateString) return 'Unknown date'
      
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    onMounted(() => {
      fetchFavorites()
      checkCurrentTheme() // 检查当前主题
      
      // 添加主题变化监听
      document.addEventListener('themechange', checkCurrentTheme);
      
      // 如果存在emitter，也添加监听
      if (window.emitter) {
        window.emitter.on('theme-changed', handleThemeChange);
      }
      
      // 监听HTML元素的data-theme属性变化
      const htmlElement = document.documentElement;
      const observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.attributeName === 'data-theme') {
            checkCurrentTheme();
          }
        });
      });
      
      observer.observe(htmlElement, { attributes: true, attributeFilter: ['data-theme'] });
      
      // 组件卸载时移除监听
      return () => {
        document.removeEventListener('themechange', checkCurrentTheme);
        if (window.emitter) {
          window.emitter.off('theme-changed', handleThemeChange);
        }
        observer.disconnect();
      };
    })

    return {
      favorites,
      loading,
      searchQuery,
      filteredFavorites,
      handleSearch,
      confirmRemove,
      removeFromFavorites,
      viewProduct,
      formatDate,
      Box,
      Search,
      Heart,
      isDarkMode
    }
  }
}
</script>

<style scoped>
.favorites-page {
  padding: 2rem 5rem 32px;
  max-width: 1400px;
  margin: 0 auto;
  background: transparent;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  transition: all 0.3s ease;
}

/* 深色主题样式 */
[data-theme="dark"] .favorites-page {
  color: #e0e0e0;
}

/* 浅色主题样式 */
[data-theme="light"] .favorites-page {
  color: #333;
}

.favorites-page h1 {
  margin-bottom: 2rem;
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

/* 深色主题标题样式 */
[data-theme="dark"] .favorites-page h1 {
  color: #c3a3ff;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.5);
}

/* 浅色主题标题样式 */
[data-theme="light"] .favorites-page h1 {
  color: #8e2de2;
  text-shadow: 0 0 10px rgba(142, 45, 226, 0.2);
}

.loading-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
  border-radius: 12px;
  transition: all 0.3s ease;
}

/* 深色主题加载容器样式 */
[data-theme="dark"] .loading-container {
  background: rgba(30, 30, 35, 0.9);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

/* 浅色主题加载容器样式 */
[data-theme="light"] .loading-container {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 5px 15px rgba(100, 100, 100, 0.1);
  border: 1px solid rgba(200, 200, 220, 0.3);
}

.empty-favorites {
  margin: 2rem auto;
  max-width: 400px;
  padding: 3rem 2rem;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
}

/* 深色主题空收藏样式 */
[data-theme="dark"] .empty-favorites {
  background: rgba(30, 30, 35, 0.9);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(80, 80, 100, 0.3);
  color: #e0e0e0;
}

/* 浅色主题空收藏样式 */
[data-theme="light"] .empty-favorites {
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 5px 15px rgba(100, 100, 100, 0.1);
  border: 1px solid rgba(200, 200, 220, 0.3);
  color: #333;
}

/* 深色主题空收藏描述样式 */
[data-theme="dark"] .empty-favorites :deep(.el-empty__description p) {
  color: #a0a0a0;
  font-size: 1.1rem;
}

/* 浅色主题空收藏描述样式 */
[data-theme="light"] .empty-favorites :deep(.el-empty__description p) {
  color: #666;
  font-size: 1.1rem;
}

/* 深色主题空收藏按钮样式 */
[data-theme="dark"] .empty-favorites :deep(.el-button--primary) {
  background: linear-gradient(135deg, #323232, #252525);
  border-color: rgba(120, 70, 200, 0.3);
  box-shadow: 0 2px 8px rgba(70, 20, 100, 0.3);
  transition: all 0.3s ease;
  color: #c3a3ff;
  padding: 10px 24px;
  border-radius: 30px;
  font-weight: 600;
}

/* 浅色主题空收藏按钮样式 */
[data-theme="light"] .empty-favorites :deep(.el-button--primary) {
  background: linear-gradient(135deg, #9d4de8, #8e2de2);
  border-color: rgba(142, 45, 226, 0.3);
  box-shadow: 0 2px 8px rgba(142, 45, 226, 0.2);
  transition: all 0.3s ease;
  color: #fff;
  padding: 10px 24px;
  border-radius: 30px;
  font-weight: 600;
}

/* 深色主题空收藏按钮悬停样式 */
[data-theme="dark"] .empty-favorites :deep(.el-button--primary:hover) {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4);
  background: linear-gradient(135deg, #404040, #303030);
  border-color: rgba(120, 70, 200, 0.6);
}

/* 浅色主题空收藏按钮悬停样式 */
[data-theme="light"] .empty-favorites :deep(.el-button--primary:hover) {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(142, 45, 226, 0.3);
  background: linear-gradient(135deg, #8e2de2, #7b1fa2);
  border-color: rgba(142, 45, 226, 0.6);
}

.favorites-container {
  margin-top: 2rem;
}

.el-row {
  margin-bottom: -30px !important;
}

.el-row:last-child {
  margin-bottom: 0;
}

.el-col {
  margin-bottom: 30px;
}

.favorites-page .product-card {
  margin-bottom: 0 !important;
  height: 100% !important;
  cursor: pointer !important;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
  overflow: hidden !important;
  border-radius: 15px !important;
  position: relative !important;
}

/* 深色主题产品卡片样式 */
[data-theme="dark"] .favorites-page .product-card {
  background: linear-gradient(135deg, #242428, #1a1a20) !important;
  border: 1px solid rgba(80, 80, 100, 0.3) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
}

/* 浅色主题产品卡片样式 */
[data-theme="light"] .favorites-page .product-card {
  background: linear-gradient(135deg, #ffffff, #f5f5f5) !important;
  border: 1px solid rgba(200, 200, 220, 0.3) !important;
  box-shadow: 0 8px 20px rgba(100, 100, 100, 0.1) !important;
}

/* 深色主题产品卡片悬停样式 */
[data-theme="dark"] .favorites-page .product-card:hover {
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(120, 70, 200, 0.4) !important;
  border-color: rgba(120, 70, 200, 0.5) !important;
  background: linear-gradient(135deg, #2d2d35, #22222a) !important;
}

/* 浅色主题产品卡片悬停样式 */
[data-theme="light"] .favorites-page .product-card:hover {
  box-shadow: 0 15px 30px rgba(100, 100, 100, 0.2), 0 0 20px rgba(142, 45, 226, 0.2) !important;
  border-color: rgba(142, 45, 226, 0.3) !important;
  background: linear-gradient(135deg, #f5f5f5, #e8e8e8) !important;
}

.favorites-page .product-image {
  height: 180px !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin: 0 !important;
  margin-bottom: 0 !important;
  border-radius: 15px 15px 0 0 !important;
  transition: all 0.3s ease !important;
}

/* 深色主题产品图片容器样式 */
[data-theme="dark"] .favorites-page .product-image {
  border-bottom: 1px solid rgba(80, 80, 100, 0.3) !important;
  background-color: #1a1a1a !important;
}

/* 浅色主题产品图片容器样式 */
[data-theme="light"] .favorites-page .product-image {
  border-bottom: 1px solid rgba(200, 200, 220, 0.3) !important;
  background-color: #f5f5f5 !important;
}

.favorites-page .product-image img {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  cursor: pointer !important;
  transition: transform 0.3s !important;
}

/* 深色主题产品图片样式 */
[data-theme="dark"] .favorites-page .product-image img {
  filter: drop-shadow(0 0 8px rgba(120, 70, 200, 0.3)) !important;
}

/* 浅色主题产品图片样式 */
[data-theme="light"] .favorites-page .product-image img {
  filter: drop-shadow(0 0 8px rgba(142, 45, 226, 0.2)) !important;
}

/* 深色主题产品图片悬停样式 */
[data-theme="dark"] .favorites-page .product-image img:hover {
  transform: scale(1.05) !important;
  filter: drop-shadow(0 0 12px rgba(120, 70, 200, 0.5)) !important;
}

/* 浅色主题产品图片悬停样式 */
[data-theme="light"] .favorites-page .product-image img:hover {
  transform: scale(1.05) !important;
  filter: drop-shadow(0 0 12px rgba(142, 45, 226, 0.3)) !important;
}

.favorites-page .card-content {
  display: flex !important;
  flex-direction: column !important;
  padding: 15px !important;
  flex: 1 !important;
  transition: all 0.3s ease !important;
}

/* 深色主题卡片内容样式 */
[data-theme="dark"] .favorites-page .card-content {
  background: rgba(35, 35, 40, 0.9) !important;
}

/* 浅色主题卡片内容样式 */
[data-theme="light"] .favorites-page .card-content {
  background: rgba(255, 255, 255, 0.9) !important;
}

.favorites-page .product-info {
  flex: 1 !important;
  cursor: pointer !important;
  margin-bottom: 1rem !important;
}

.favorites-page .product-info h3 {
  margin: 0 0 0.5rem 0 !important;
  font-size: 1rem !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  font-weight: 700 !important;
  line-height: 1.4 !important;
  transition: all 0.3s ease !important;
}

/* 深色主题产品标题样式 */
[data-theme="dark"] .favorites-page .product-info h3 {
  color: #e0e0e0 !important;
}

/* 浅色主题产品标题样式 */
[data-theme="light"] .favorites-page .product-info h3 {
  color: #333 !important;
}

.favorites-page .price {
  font-weight: bold !important;
  margin: 0.4rem 0 !important;
  font-size: 1.1rem !important;
  transition: all 0.3s ease !important;
}

/* 深色主题价格样式 */
[data-theme="dark"] .favorites-page .price {
  color: #c3a3ff !important;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3) !important;
}

/* 浅色主题价格样式 */
[data-theme="light"] .favorites-page .price {
  color: #8e2de2 !important;
  text-shadow: 0 0 10px rgba(142, 45, 226, 0.2) !important;
}

.favorites-page .date {
  font-size: 0.85rem !important;
  margin: 0.4rem 0 !important;
  transition: all 0.3s ease !important;
}

/* 深色主题日期样式 */
[data-theme="dark"] .favorites-page .date {
  color: #888 !important;
}

/* 浅色主题日期样式 */
[data-theme="light"] .favorites-page .date {
  color: #666 !important;
}

.favorites-page .action-area {
  display: flex !important;
  justify-content: flex-end !important;
  align-items: center !important;
  margin-left: 0 !important;
  padding-top: 0.8rem !important;
  transition: all 0.3s ease !important;
}

/* 深色主题操作区域样式 */
[data-theme="dark"] .favorites-page .action-area {
  border-top: 1px solid rgba(80, 80, 100, 0.3) !important;
}

/* 浅色主题操作区域样式 */
[data-theme="light"] .favorites-page .action-area {
  border-top: 1px solid rgba(200, 200, 220, 0.3) !important;
}

.favorites-page .remove-btn {
  font-size: 0.9rem !important;
  padding: 0 1rem !important;
  height: 30px !important;
  min-height: 30px !important;
  line-height: 1 !important;
  border-radius: 20px !important;
  margin: 0 !important;
  transition: all 0.3s !important;
  font-weight: 600 !important;
}

/* 深色主题移除按钮样式 */
[data-theme="dark"] .favorites-page .remove-btn {
  background: rgba(245, 108, 108, 0.1) !important;
  border-color: rgba(245, 108, 108, 0.3) !important;
  color: #f56c6c !important;
}

/* 浅色主题移除按钮样式 */
[data-theme="light"] .favorites-page .remove-btn {
  background: rgba(245, 108, 108, 0.05) !important;
  border-color: rgba(245, 108, 108, 0.2) !important;
  color: #f56c6c !important;
}

/* 深色主题移除按钮悬停样式 */
[data-theme="dark"] .favorites-page .remove-btn:hover {
  background: linear-gradient(135deg, #f56c6c, #e05151) !important;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.4) !important;
  color: white !important;
  border-color: rgba(245, 108, 108, 0.6) !important;
}

/* 浅色主题移除按钮悬停样式 */
[data-theme="light"] .favorites-page .remove-btn:hover {
  background: linear-gradient(135deg, #f56c6c, #e05151) !important;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3) !important;
  color: white !important;
  border-color: rgba(245, 108, 108, 0.4) !important;
}

@media (max-width: 1200px) {
  .favorites-page {
    padding: 2rem 3rem 32px;
  }
  
  .product-image {
    height: 160px;
  }
}

@media (max-width: 992px) {
  .favorites-page {
    padding: 2rem 2rem 32px;
  }
  
  .favorites-page h1 {
    font-size: 2.2rem;
  }

  .product-card {
    border-radius: 12px;
  }

  .product-image {
    height: 140px;
    border-radius: 12px 12px 0 0;
  }
  
  .card-content {
    padding: 12px;
  }

  .product-info h3 {
    font-size: 0.9rem;
  }

  .price {
    font-size: 1rem;
  }

  .date {
    font-size: 0.75rem;
  }

  .remove-btn {
    font-size: 0.8rem;
    height: 26px;
    min-height: 26px;
  }
}

@media (max-width: 768px) {
  .favorites-page {
    padding: 1rem 1rem 32px;
  }
  
  .favorites-page h1 {
    font-size: 1.8rem;
  }
  
  .el-col {
    margin-bottom: 20px;
  }
  
  .product-image {
    height: 120px;
  }
  
  .card-content {
    padding: 10px;
  }
  
  .product-info h3 {
    font-size: 0.85rem;
  }
  
  .price {
    font-size: 0.9rem;
  }
  
  .date {
    font-size: 0.7rem;
  }
  
  .remove-btn {
    font-size: 0.7rem;
    height: 22px;
    min-height: 22px;
    padding: 0 0.8rem;
  }
}

@media (max-width: 576px) {
  .el-row {
    margin-left: -8px !important;
    margin-right: -8px !important;
  }
  
  .el-col {
    padding-left: 8px !important;
    padding-right: 8px !important;
    margin-bottom: 16px;
  }
  
  .product-image {
    height: 100px;
  }
  
  .card-content {
    padding: 8px;
  }

  .product-info h3 {
    font-size: 0.8rem;
  }

  .price {
    font-size: 0.85rem;
  }

  .date {
    font-size: 0.65rem;
  }

  .remove-btn {
    font-size: 0.65rem;
    height: 20px;
    min-height: 20px;
    padding: 0 0.6rem;
  }

  .empty-favorites {
    padding: 2rem 1rem;
  }
  
  .empty-favorites :deep(.el-empty__description p) {
    font-size: 1rem;
  }
  
  .empty-favorites :deep(.el-button--primary) {
    padding: 8px 16px;
    font-size: 14px;
  }
}

/* For touch devices */
@media (hover: none) {
  .product-card {
    transform: none !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
    border-radius: 15px !important;
  }
  
  /* 深色主题触摸设备样式 */
  [data-theme="dark"] .product-card {
    background: linear-gradient(135deg, #242428, #1a1a20) !important;
    border: 1px solid rgba(80, 80, 100, 0.3) !important;
  }
  
  /* 浅色主题触摸设备样式 */
  [data-theme="light"] .product-card {
    background: linear-gradient(135deg, #ffffff, #f5f5f5) !important;
    border: 1px solid rgba(200, 200, 220, 0.3) !important;
    box-shadow: 0 8px 20px rgba(100, 100, 100, 0.1) !important;
  }

  .remove-btn {
    min-width: 30px;
    min-height: 30px;
  }
}

/* 移动端搜索框样式 */
.mobile-search-container {
  display: none;
  margin-bottom: 20px;
  padding: 0 20px;
}

.search-box {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  height: 45px;
  padding: 0 15px 0 45px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  outline: none;
  transition: all 0.3s ease;
}

.search-icon {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 18px;
  z-index: 1;
}

/* 深色主题搜索框 */
[data-theme="dark"] .search-input {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .search-input:focus {
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.3);
}

[data-theme="dark"] .search-icon {
  color: rgba(255, 255, 255, 0.6);
}

/* 浅色主题搜索框 */
[data-theme="light"] .search-input {
  background: rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

[data-theme="light"] .search-input::placeholder {
  color: rgba(0, 0, 0, 0.4);
}

[data-theme="light"] .search-input:focus {
  background: rgba(0, 0, 0, 0.08);
  box-shadow: 0 0 20px rgba(142, 45, 226, 0.1);
  border-color: rgba(142, 45, 226, 0.3);
}

[data-theme="light"] .search-icon {
  color: rgba(0, 0, 0, 0.4);
}

/* PC端和移动端布局控制 */
.pc-layout {
  display: block;
}

.mobile-layout {
  display: none;
}

/* 移动端产品卡片样式 */
.mobile-product-card {
  display: flex;
  align-items: center;
  border-radius: 15px;
  padding: 15px;
  margin-bottom: 15px;
  transition: all 0.3s ease;
  cursor: pointer;
}

/* 深色主题产品卡片 */
[data-theme="dark"] .mobile-product-card {
  background: linear-gradient(135deg, #7b4ca7 0%, #9b59b6 100%);
  box-shadow: 0 4px 15px rgba(123, 76, 167, 0.3);
}

[data-theme="dark"] .mobile-product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(123, 76, 167, 0.4);
}

/* 浅色主题产品卡片 */
[data-theme="light"] .mobile-product-card {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

[data-theme="light"] .mobile-product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

.mobile-product-image {
  width: 80px;
  height: 80px;
  border-radius: 10px;
  overflow: hidden;
  margin-right: 15px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.mobile-product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

/* 深色主题图片容器 */
[data-theme="dark"] .mobile-product-image {
  background: rgba(255, 255, 255, 0.1);
}

/* 浅色主题图片容器 */
[data-theme="light"] .mobile-product-image {
  background: rgba(255, 255, 255, 0.15);
}

.mobile-product-info {
  flex: 1;
}

.mobile-product-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 5px 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

.mobile-product-subtitle {
  font-size: 14px;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
}

/* 深色主题文字颜色 */
[data-theme="dark"] .mobile-product-info {
  color: white;
}

[data-theme="dark"] .mobile-product-title {
  color: white;
}

[data-theme="dark"] .mobile-product-subtitle {
  color: rgba(255, 255, 255, 0.8);
}

/* 浅色主题文字颜色 */
[data-theme="light"] .mobile-product-info {
  color: white;
}

[data-theme="light"] .mobile-product-title {
  color: white;
}

[data-theme="light"] .mobile-product-subtitle {
  color: rgba(255, 255, 255, 0.9);
}

.mobile-price-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mobile-price {
  font-size: 18px;
  font-weight: 700;
}

.mobile-price-suffix {
  font-size: 14px;
  text-decoration: line-through;
}

.mobile-favorite-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 50%;
}

/* 深色主题价格和图标 */
[data-theme="dark"] .mobile-price {
  color: #ffd700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

[data-theme="dark"] .mobile-price-suffix {
  color: rgba(255, 255, 255, 0.6);
}

[data-theme="dark"] .mobile-favorite-icon {
  color: #ff6b9d;
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .mobile-favorite-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* 浅色主题价格和图标 */
[data-theme="light"] .mobile-price {
  color: #ffd700;
  text-shadow: 0 0 10px rgba(255, 215, 0, 0.4);
}

[data-theme="light"] .mobile-price-suffix {
  color: rgba(255, 255, 255, 0.7);
}

[data-theme="light"] .mobile-favorite-icon {
  color: #ff6b9d;
  background: rgba(255, 255, 255, 0.15);
}

[data-theme="light"] .mobile-favorite-icon:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(1.1);
}

@media (max-width: 768px) {
  .mobile-search-container {
    display: block;
  }

  .pc-layout {
    display: none;
  }

  .mobile-layout {
    display: block;
  }

  .favorites-page {
    padding: 1rem 0 32px;
  }

  .favorites-page h1 {
    margin-bottom: 1rem;
    padding: 0 20px;
  }
}
</style>